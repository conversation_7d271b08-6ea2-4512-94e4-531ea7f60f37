import crypto from 'crypto';
import fs from 'fs';
import path from 'path';

function generateSecureSecret(length: number = 64): string {
  return crypto.randomBytes(length).toString('hex');
}

function generateJWTSecrets() {
  console.log('🔐 Generating secure JWT secrets...');
  
  const secrets = {
    JWT_ACCESS_SECRET: generateSecureSecret(32),
    JWT_REFRESH_SECRET: generateSecureSecret(32),
    JWT_ISSUER: 'peoplenest-api',
    JWT_AUDIENCE: 'peoplenest-client',
    JWT_ACCESS_EXPIRY: '15m',
    JWT_REFRESH_EXPIRY: '7d',
    ENCRYPTION_KEY: generateSecureSecret(32),
    SESSION_SECRET: generateSecureSecret(32)
  };

  console.log('Generated secrets:');
  Object.entries(secrets).forEach(([key, value]) => {
    console.log(`${key}=${value}`);
  });

  // Read current .env file
  const envPath = path.join(__dirname, '../../.env');
  let envContent = '';
  
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  }

  // Update or add JWT secrets
  Object.entries(secrets).forEach(([key, value]) => {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    if (regex.test(envContent)) {
      envContent = envContent.replace(regex, `${key}=${value}`);
    } else {
      envContent += `\n${key}=${value}`;
    }
  });

  // Write updated .env file
  fs.writeFileSync(envPath, envContent);
  console.log('\n✅ Updated .env file with new secrets');
  
  return secrets;
}

if (require.main === module) {
  generateJWTSecrets();
}

export { generateJWTSecrets };
