"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1712],{26126:(e,t,r)=>{r.d(t,{E:()=>s});var n=r(95155);r(12115);var o=r(74466),a=r(59434);let i=(0,o.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500/10 text-green-600 hover:bg-green-500/20 dark:text-green-400",warning:"border-transparent bg-yellow-500/10 text-yellow-600 hover:bg-yellow-500/20 dark:text-yellow-400",info:"border-transparent bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 dark:text-blue-400",active:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",inactive:"border-transparent bg-muted text-muted-foreground",pending:"border-transparent bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",approved:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",rejected:"border-transparent bg-red-500/10 text-red-600 dark:text-red-400",draft:"border-transparent bg-muted text-muted-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function s(e){let{className:t,variant:r,size:o,icon:s,removable:l,onRemove:c,children:u,...d}=e;return(0,n.jsxs)("div",{className:(0,a.cn)(i({variant:r,size:o}),t),...d,children:[s&&(0,n.jsx)("span",{className:"mr-1",children:s}),u,l&&(0,n.jsx)("button",{type:"button",className:"ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10",onClick:c,children:(0,n.jsx)("svg",{className:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}},30285:(e,t,r)=>{r.d(t,{$:()=>c});var n=r(95155),o=r(12115),a=r(99708),i=r(74466),s=r(59434);let l=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700",warning:"bg-yellow-600 text-white hover:bg-yellow-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=o.forwardRef((e,t)=>{let{className:r,variant:o,size:i,asChild:c=!1,loading:u=!1,leftIcon:d,rightIcon:f,children:g,disabled:h,...p}=e,b=c?a.DX:"button";return(0,n.jsxs)(b,{className:(0,s.cn)(l({variant:o,size:i,className:r})),ref:t,disabled:h||u,...p,children:[u&&(0,n.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!u&&d&&(0,n.jsx)("span",{className:"mr-2",children:d}),g,!u&&f&&(0,n.jsx)("span",{className:"ml-2",children:f})]})});c.displayName="Button"},59434:(e,t,r)=>{r.d(t,{Ee:()=>s,cn:()=>a,vv:()=>i});var n=r(52596),o=r(39688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,n.$)(t))}function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e)}function s(e){return new Intl.NumberFormat("en-US",{style:"percent",minimumFractionDigits:1,maximumFractionDigits:1}).format(e/100)}},79323:(e,t,r)=>{r.d(t,{$g:()=>s,BU:()=>h,Pt:()=>a,Ti:()=>l,To:()=>c,_m:()=>u,a7:()=>p,do:()=>i,hf:()=>f,pX:()=>g,sx:()=>d});let n="peoplenest_auth_token",o="peoplenest_user";function a(){let e=localStorage.getItem(n);if(!e)return null;try{return JSON.parse(e).accessToken||null}catch(t){return e}}function i(e){localStorage.setItem(n,e)}function s(){localStorage.removeItem(n),localStorage.removeItem(o)}function l(){let e=localStorage.getItem(o);if(!e)return null;try{return JSON.parse(e)}catch(e){return null}}function c(e){localStorage.setItem(o,JSON.stringify(e))}function u(e,t){return!!e&&e.permissions.includes(t)}function d(e,t){return!!e&&t.some(t=>e.permissions.includes(t))}function f(e,t){return!!e&&e.role===t}function g(e,t){return!!e&&t.includes(e.role)}function h(e,t){if(!e)return!1;let r=["employee","manager","hr","hr_admin","super_admin"];return r.indexOf(e.role)>=r.indexOf(t)}let p={LOGIN:"/api/auth/login",LOGOUT:"/api/auth/logout",REFRESH:"/api/auth/refresh",ME:"/api/auth/me"}},94819:(e,t,r)=>{r.d(t,{A:()=>s,O:()=>l});var n=r(95155),o=r(12115),a=r(79323);let i=(0,o.createContext)(void 0);function s(){let e=(0,o.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l(e){let{children:t}=e,[r,s]=(0,o.useState)({user:null,isAuthenticated:!1,isLoading:!0,token:null});(0,o.useEffect)(()=>{(async()=>{try{let e=(0,a.Pt)(),t=(0,a.Ti)();e&&t?await l(e)?s({user:t,isAuthenticated:!0,isLoading:!1,token:e}):((0,a.$g)(),s({user:null,isAuthenticated:!1,isLoading:!1,token:null})):s({user:null,isAuthenticated:!1,isLoading:!1,token:null})}catch(e){console.error("Failed to initialize auth:",e),s({user:null,isAuthenticated:!1,isLoading:!1,token:null})}})()},[]);let l=async e=>{try{return(await fetch("".concat("http://localhost:3002").concat(a.a7.ME),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}})).ok}catch(e){return console.error("Token verification failed:",e),!1}},c=(0,o.useCallback)(async(e,t)=>{try{s(e=>({...e,isLoading:!0}));let r=await fetch("".concat("http://localhost:3002").concat(a.a7.LOGIN),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(r.ok){let{user:e,tokens:t,sessionId:n,deviceFingerprint:o}=await r.json(),i={accessToken:t.accessToken,refreshToken:t.refreshToken,expiresIn:t.expiresIn,tokenType:t.tokenType,sessionId:n,deviceFingerprint:o};return(0,a.do)(JSON.stringify(i)),(0,a.To)(e),s({user:e,isAuthenticated:!0,isLoading:!1,token:t.accessToken}),!0}{let e=await r.json().catch(()=>({}));return console.error("Login failed:",e),s(e=>({...e,isLoading:!1})),!1}}catch(e){return console.error("Login failed:",e),s(e=>({...e,isLoading:!1})),!1}},[]),u=(0,o.useCallback)(async()=>{try{let e=(0,a.Pt)(),t=null;if(e)try{t=JSON.parse(e)}catch(r){t={accessToken:e}}if(null==t?void 0:t.accessToken)try{await fetch("".concat("http://localhost:3002").concat(a.a7.LOGOUT),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t.accessToken)},body:JSON.stringify({refreshToken:t.refreshToken,sessionId:t.sessionId})})}catch(e){console.warn("Logout API call failed:",e)}}catch(e){console.warn("Error during logout:",e)}finally{(0,a.$g)(),s({user:null,isAuthenticated:!1,isLoading:!1,token:null}),window.location.href="/auth/login"}},[]),d=(0,o.useCallback)(async()=>{try{let e,t=(0,a.Pt)();if(!t)return!1;try{e=JSON.parse(t)}catch(r){e={accessToken:t}}if(!e.refreshToken)return u(),!1;let r=await fetch("".concat("http://localhost:3002").concat(a.a7.REFRESH),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e.refreshToken,deviceFingerprint:e.deviceFingerprint})});if(!r.ok)return u(),!1;{let{tokens:t}=await r.json(),n={...e,accessToken:t.accessToken,refreshToken:t.refreshToken,expiresIn:t.expiresIn};return(0,a.do)(JSON.stringify(n)),s(e=>({...e,token:t.accessToken})),!0}}catch(e){return console.error("Token refresh failed:",e),u(),!1}},[u]),f=(0,o.useCallback)(e=>{(0,a.To)(e),s(t=>({...t,user:e}))},[]),g={...r,login:c,logout:u,refreshToken:d,updateUser:f};return(0,n.jsx)(i.Provider,{value:g,children:t})}}}]);