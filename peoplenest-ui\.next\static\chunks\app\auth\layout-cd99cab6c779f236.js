(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[382],{25067:(e,t,n)=>{Promise.resolve().then(n.bind(n,34661))},34661:(e,t,n)=>{"use strict";n.d(t,{AuthLayoutClient:()=>i});var r=n(95155),o=n(94819);function i(e){let{children:t}=e;return(0,r.jsx)(o.O,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,r.jsx)("div",{className:"relative z-10",children:t}),(0,r.jsx)("footer",{className:"absolute bottom-4 left-0 right-0 text-center",children:(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"\xa9 2024 PeopleNest. All rights reserved. | Privacy Policy | Terms of Service"})})]})})}},79323:(e,t,n)=>{"use strict";n.d(t,{$g:()=>s,BU:()=>p,Pt:()=>i,Ti:()=>c,To:()=>l,_m:()=>u,a7:()=>g,do:()=>a,hf:()=>d,pX:()=>f,sx:()=>h});let r="peoplenest_auth_token",o="peoplenest_user";function i(){let e=localStorage.getItem(r);if(!e)return null;try{return JSON.parse(e).accessToken||null}catch(t){return e}}function a(e){localStorage.setItem(r,e)}function s(){localStorage.removeItem(r),localStorage.removeItem(o)}function c(){let e=localStorage.getItem(o);if(!e)return null;try{return JSON.parse(e)}catch(e){return null}}function l(e){localStorage.setItem(o,JSON.stringify(e))}function u(e,t){return!!e&&e.permissions.includes(t)}function h(e,t){return!!e&&t.some(t=>e.permissions.includes(t))}function d(e,t){return!!e&&e.role===t}function f(e,t){return!!e&&t.includes(e.role)}function p(e,t){if(!e)return!1;let n=["employee","manager","hr","hr_admin","super_admin"];return n.indexOf(e.role)>=n.indexOf(t)}let g={LOGIN:"/api/auth/login",LOGOUT:"/api/auth/logout",REFRESH:"/api/auth/refresh",ME:"/api/auth/me"}},94819:(e,t,n)=>{"use strict";n.d(t,{A:()=>s,O:()=>c});var r=n(95155),o=n(12115),i=n(79323);let a=(0,o.createContext)(void 0);function s(){let e=(0,o.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c(e){let{children:t}=e,[n,s]=(0,o.useState)({user:null,isAuthenticated:!1,isLoading:!0,token:null});(0,o.useEffect)(()=>{(async()=>{try{let e=(0,i.Pt)(),t=(0,i.Ti)();e&&t?await c(e)?s({user:t,isAuthenticated:!0,isLoading:!1,token:e}):((0,i.$g)(),s({user:null,isAuthenticated:!1,isLoading:!1,token:null})):s({user:null,isAuthenticated:!1,isLoading:!1,token:null})}catch(e){console.error("Failed to initialize auth:",e),s({user:null,isAuthenticated:!1,isLoading:!1,token:null})}})()},[]);let c=async e=>{try{return(await fetch("".concat("http://localhost:3002").concat(i.a7.ME),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}})).ok}catch(e){return console.error("Token verification failed:",e),!1}},l=(0,o.useCallback)(async(e,t)=>{try{s(e=>({...e,isLoading:!0}));let n=await fetch("".concat("http://localhost:3002").concat(i.a7.LOGIN),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(n.ok){let{user:e,tokens:t,sessionId:r,deviceFingerprint:o}=await n.json(),a={accessToken:t.accessToken,refreshToken:t.refreshToken,expiresIn:t.expiresIn,tokenType:t.tokenType,sessionId:r,deviceFingerprint:o};return(0,i.do)(JSON.stringify(a)),(0,i.To)(e),s({user:e,isAuthenticated:!0,isLoading:!1,token:t.accessToken}),!0}{let e=await n.json().catch(()=>({}));return console.error("Login failed:",e),s(e=>({...e,isLoading:!1})),!1}}catch(e){return console.error("Login failed:",e),s(e=>({...e,isLoading:!1})),!1}},[]),u=(0,o.useCallback)(async()=>{try{let e=(0,i.Pt)(),t=null;if(e)try{t=JSON.parse(e)}catch(n){t={accessToken:e}}if(null==t?void 0:t.accessToken)try{await fetch("".concat("http://localhost:3002").concat(i.a7.LOGOUT),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t.accessToken)},body:JSON.stringify({refreshToken:t.refreshToken,sessionId:t.sessionId})})}catch(e){console.warn("Logout API call failed:",e)}}catch(e){console.warn("Error during logout:",e)}finally{(0,i.$g)(),s({user:null,isAuthenticated:!1,isLoading:!1,token:null}),window.location.href="/auth/login"}},[]),h=(0,o.useCallback)(async()=>{try{let e,t=(0,i.Pt)();if(!t)return!1;try{e=JSON.parse(t)}catch(n){e={accessToken:t}}if(!e.refreshToken)return u(),!1;let n=await fetch("".concat("http://localhost:3002").concat(i.a7.REFRESH),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e.refreshToken,deviceFingerprint:e.deviceFingerprint})});if(!n.ok)return u(),!1;{let{tokens:t}=await n.json(),r={...e,accessToken:t.accessToken,refreshToken:t.refreshToken,expiresIn:t.expiresIn};return(0,i.do)(JSON.stringify(r)),s(e=>({...e,token:t.accessToken})),!0}}catch(e){return console.error("Token refresh failed:",e),u(),!1}},[u]),d=(0,o.useCallback)(e=>{(0,i.To)(e),s(t=>({...t,user:e}))},[]),f={...n,login:l,logout:u,refreshToken:h,updateUser:d};return(0,r.jsx)(a.Provider,{value:f,children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8441,1684,7358],()=>t(25067)),_N_E=e.O()}]);