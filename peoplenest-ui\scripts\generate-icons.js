const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Icon sizes needed based on manifest.json
const iconSizes = [
  16, 32, 72, 96, 128, 144, 152, 192, 384, 512
];

// Additional shortcut icon sizes
const shortcutSizes = [96];

// Create a simple colored square icon as SVG
function createIconData(size, color = '#3b82f6', text = 'PN') {
  const svg = `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="${size}" height="${size}" rx="${Math.round(size * 0.125)}" fill="${color}"/>
    <g transform="translate(${size * 0.167}, ${size * 0.167})">
      <!-- People icon -->
      <circle cx="${size * 0.25}" cy="${size * 0.208}" r="${size * 0.083}" fill="white" opacity="0.9"/>
      <circle cx="${size * 0.417}" cy="${size * 0.208}" r="${size * 0.083}" fill="white" opacity="0.9"/>
      <circle cx="${size * 0.583}" cy="${size * 0.208}" r="${size * 0.083}" fill="white" opacity="0.9"/>

      <!-- Bodies -->
      <ellipse cx="${size * 0.25}" cy="${size * 0.417}" rx="${size * 0.104}" ry="${size * 0.125}" fill="white" opacity="0.8"/>
      <ellipse cx="${size * 0.417}" cy="${size * 0.417}" rx="${size * 0.104}" ry="${size * 0.125}" fill="white" opacity="0.8"/>
      <ellipse cx="${size * 0.583}" cy="${size * 0.417}" rx="${size * 0.104}" ry="${size * 0.125}" fill="white" opacity="0.8"/>

      <!-- Connection lines -->
      <path d="M${size * 0.167} ${size * 0.5} Q${size * 0.333} ${size * 0.458} ${size * 0.5} ${size * 0.5} Q${size * 0.583} ${size * 0.458} ${size * 0.667} ${size * 0.5}" stroke="white" stroke-width="${Math.max(1, size * 0.016)}" fill="none" opacity="0.7"/>
    </g>

    <!-- Company name -->
    <text x="${size / 2}" y="${size * 0.833}" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="${Math.max(8, size * 0.083)}" font-weight="bold">${text}</text>
  </svg>`;

  return svg;
}

// Convert SVG to PNG using Sharp
async function convertSvgToPng(svgContent, outputPath, size) {
  try {
    await sharp(Buffer.from(svgContent))
      .resize(size, size)
      .png()
      .toFile(outputPath);
    return true;
  } catch (error) {
    console.error(`Error converting ${outputPath}:`, error.message);
    return false;
  }
}

// Main async function to generate icons
async function generateIcons() {
  // Create icons directory if it doesn't exist
  const iconsDir = path.join(__dirname, '../public/icons');
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
  }

  console.log('Generating main app icons...');

  // Generate main app icons
  for (const size of iconSizes) {
    const svgContent = createIconData(size);

    // Save SVG
    const svgFilename = `icon-${size}x${size}.svg`;
    const svgFilepath = path.join(iconsDir, svgFilename);
    fs.writeFileSync(svgFilepath, svgContent);

    // Convert to PNG
    const pngFilename = `icon-${size}x${size}.png`;
    const pngFilepath = path.join(iconsDir, pngFilename);
    const success = await convertSvgToPng(svgContent, pngFilepath, size);

    if (success) {
      console.log(`Generated ${pngFilename}`);
    } else {
      console.log(`Generated ${svgFilename} (PNG conversion failed)`);
    }
  }

  console.log('Generating shortcut icons...');

  // Generate shortcut icons
  const shortcuts = ['dashboard', 'employees', 'payroll', 'performance'];
  for (const shortcut of shortcuts) {
    for (const size of shortcutSizes) {
      const svgContent = createIconData(size, '#3b82f6', shortcut.substring(0, 2).toUpperCase());

      // Save SVG
      const svgFilename = `${shortcut}-${size}x${size}.svg`;
      const svgFilepath = path.join(iconsDir, svgFilename);
      fs.writeFileSync(svgFilepath, svgContent);

      // Convert to PNG
      const pngFilename = `${shortcut}-${size}x${size}.png`;
      const pngFilepath = path.join(iconsDir, pngFilename);
      const success = await convertSvgToPng(svgContent, pngFilepath, size);

      if (success) {
        console.log(`Generated ${pngFilename}`);
      } else {
        console.log(`Generated ${svgFilename} (PNG conversion failed)`);
      }
    }
  }

  console.log('Icon generation complete!');
}

// Run the icon generation
generateIcons().catch(console.error);
