<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, viewport-fit=cover, user-scalable=no"/><link rel="stylesheet" href="/_next/static/css/56cc0dd3233d2018.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-101e750c36e44303.js"/><script src="/_next/static/chunks/4bd1b696-58114847d125c4a3.js" async=""></script><script src="/_next/static/chunks/1684-0a620f6f17136dca.js" async=""></script><script src="/_next/static/chunks/main-app-ad34e622f20ad987.js" async=""></script><script src="/_next/static/chunks/app/layout-c92ee0b72061c2db.js" async=""></script><script src="/_next/static/chunks/3706-cf1b45900489dd08.js" async=""></script><script src="/_next/static/chunks/app/page-838797e4107edbd9.js" async=""></script><meta name="theme-color" content="#3b82f6"/><script>
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\b(light|dark)\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\b(light|dark)\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\b(light|dark)\b/g, '').trim() + ' light';
                }
              })();
            </script><title>PeopleNest - Enterprise HRMS Platform</title><meta name="description" content="Modern, AI-powered Human Resource Management System for enterprise organizations"/><link rel="manifest" href="/manifest.json"/><meta name="keywords" content="HRMS,HR,Human Resources,Employee Management,Payroll,Performance"/><meta name="mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="default"/><meta name="apple-mobile-web-app-title" content="PeopleNest"/><meta name="application-name" content="PeopleNest HRMS"/><meta name="msapplication-TileColor" content="#3b82f6"/><meta name="msapplication-config" content="/browserconfig.xml"/><meta name="format-detection" content="telephone=no"/><meta name="mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-title" content="PeopleNest HRMS"/><meta name="apple-mobile-web-app-status-bar-style" content="default"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_e8ce0c __variable_3c557b font-sans antialiased touch-manipulation"><div hidden=""><!--$--><!--/$--></div><script>
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\b(light|dark)\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\b(light|dark)\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\b(light|dark)\b/g, '').trim() + ' light';
                }
              })();
            </script><div class="min-h-screen bg-gradient-to-br from-primary/5 via-background to-primary/10 flex items-center justify-center"><div class="text-center" style="opacity:0;transform:scale(0.8)"><div class="inline-flex items-center justify-center w-20 h-20 bg-primary rounded-3xl mb-6 shadow-lg" style="transform:scale(0.8)"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building2 lucide-building-2 w-10 h-10 text-primary-foreground" aria-hidden="true"><path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"></path><path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"></path><path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"></path><path d="M10 6h4"></path><path d="M10 10h4"></path><path d="M10 14h4"></path><path d="M10 18h4"></path></svg></div><h1 class="text-4xl font-bold text-foreground mb-4" style="opacity:0;transform:translateY(20px)">PeopleNest</h1><p class="text-xl text-muted-foreground mb-8" style="opacity:0;transform:translateY(20px)">Enterprise HRMS Platform</p><div class="flex items-center justify-center space-x-2 text-muted-foreground" style="opacity:0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-loader-circle w-5 h-5 animate-spin" aria-hidden="true"><path d="M21 12a9 9 0 1 1-6.219-8.56"></path></svg><span>Loading your workspace...</span></div></div></div><!--$--><!--/$--><script>
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            </script><script src="/_next/static/chunks/webpack-101e750c36e44303.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[77890,[\"7177\",\"static/chunks/app/layout-c92ee0b72061c2db.js\"],\"ThemeProvider\"]\n3:I[87555,[],\"\"]\n4:I[31295,[],\"\"]\n5:I[90894,[],\"ClientPageRoot\"]\n6:I[33792,[\"3706\",\"static/chunks/3706-cf1b45900489dd08.js\",\"8974\",\"static/chunks/app/page-838797e4107edbd9.js\"],\"default\"]\n9:I[59665,[],\"OutletBoundary\"]\nc:I[74911,[],\"AsyncMetadataOutlet\"]\ne:I[59665,[],\"ViewportBoundary\"]\n10:I[59665,[],\"MetadataBoundary\"]\n12:I[26614,[],\"\"]\n:HL[\"/_next/static/css/56cc0dd3233d2018.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"pDhezjYFuvgxkHqbZem0i\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/56cc0dd3233d2018.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{\"children\":[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"\\n              (function() {\\n                try {\\n                  var theme = localStorage.getItem('theme-mode') || 'light';\\n                  var root = document.documentElement;\\n\\n                  // Apply theme immediately to prevent hydration mismatch\\n                  if (theme === 'system') {\\n                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + systemTheme;\\n                  } else {\\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + theme;\\n                  }\\n                } catch (e) {\\n                  // Fallback to light theme if anything goes wrong\\n                  document.documentElement.className = document.documentElement.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' light';\\n                }\\n              })();\\n            \"}}]}],[\"$\",\"body\",null,{\"className\":\"__variable_e8ce0c __variable_3c557b font-sans antialiased touch-manipulation\",\"children\":[[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"\\n              (function() {\\n                try {\\n                  var theme = localStorage.getItem('theme-mode') || 'light';\\n                  var root = document.documentElement;\\n\\n                  // Apply theme immediately to prevent hydration mismatch\\n                  if (theme === 'system') {\\n                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + systemTheme;\\n                  } else {\\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + theme;\\n                  }\\n                } catch (e) {\\n                  // Fallback to light theme if anything goes wrong\\n                  document.documentElement.className = document.documentElement.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' light';\\n                }\\n              })();\\n            \"}}],[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"\\n              if ('serviceWorker' in navigator) {\\n                window.addEventListener('load', function() {\\n                  navigator.serviceWorker.register('/sw.js')\\n                    .then(function(registration) {\\n                      console.log('SW registered: ', registration);\\n                    })\\n                    .catch(function(registrationError) {\\n                      console.log('SW registration failed: ', registrationError);\\n                    });\\n                });\\n              }\\n            \"}}]]}]]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{\"Component\":\"$6\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@7\",\"$@8\"]}],null,[\"$\",\"$L9\",null,{\"children\":[\"$La\",\"$Lb\",[\"$\",\"$Lc\",null,{\"promise\":\"$@d\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"-ccmzhzxocv9EwMvgxjVxv\",{\"children\":[[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}],null]}],[\"$\",\"$L10\",null,{\"children\":\"$L11\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$12\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"13:\"$Sreact.suspense\"\n14:I[74911,[],\"AsyncMetadata\"]\n7:{}\n8:{}\n11:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$13\",null,{\"fallback\":null,\"children\":[\"$\",\"$L14\",null,{\"promise\":\"$@15\"}]}]}]\n"])</script><script>self.__next_f.push([1,"b:null\n"])</script><script>self.__next_f.push([1,"f:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1, maximum-scale=1, viewport-fit=cover, user-scalable=no\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#3b82f6\"}]]\na:null\n"])</script><script>self.__next_f.push([1,"d:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"PeopleNest - Enterprise HRMS Platform\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Modern, AI-powered Human Resource Management System for enterprise organizations\"}],[\"$\",\"link\",\"2\",{\"rel\":\"manifest\",\"href\":\"/manifest.json\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"HRMS,HR,Human Resources,Employee Management,Payroll,Performance\"}],[\"$\",\"meta\",\"4\",{\"name\":\"mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",\"5\",{\"name\":\"apple-mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",\"6\",{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"default\"}],[\"$\",\"meta\",\"7\",{\"name\":\"apple-mobile-web-app-title\",\"content\":\"PeopleNest\"}],[\"$\",\"meta\",\"8\",{\"name\":\"application-name\",\"content\":\"PeopleNest HRMS\"}],[\"$\",\"meta\",\"9\",{\"name\":\"msapplication-TileColor\",\"content\":\"#3b82f6\"}],[\"$\",\"meta\",\"10\",{\"name\":\"msapplication-config\",\"content\":\"/browserconfig.xml\"}],[\"$\",\"meta\",\"11\",{\"name\":\"format-detection\",\"content\":\"telephone=no\"}],[\"$\",\"meta\",\"12\",{\"name\":\"mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",\"13\",{\"name\":\"apple-mobile-web-app-title\",\"content\":\"PeopleNest HRMS\"}],[\"$\",\"meta\",\"14\",{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"default\"}],[\"$\",\"link\",\"15\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n15:{\"metadata\":\"$d:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>