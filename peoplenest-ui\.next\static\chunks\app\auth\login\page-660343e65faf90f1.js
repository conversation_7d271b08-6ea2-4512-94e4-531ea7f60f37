(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4859],{23227:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},28497:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var a=r(95155),s=r(12115),l=r(35695),d=r(17859),i=r(23227),n=r(75525);let o=(0,r(19946).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var c=r(85339),m=r(28883),u=r(32919),x=r(78749),f=r(92657),h=r(62177),p=r(48778),g=r(71153),y=r(30285),b=r(62523),v=r(66695),N=r(26126),j=r(74466),w=r(59434);let k=(0,j.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-500/50 text-yellow-900 bg-yellow-50 dark:border-yellow-500 dark:text-yellow-100 dark:bg-yellow-950 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-400",success:"border-green-500/50 text-green-900 bg-green-50 dark:border-green-500 dark:text-green-100 dark:bg-green-950 [&>svg]:text-green-600 dark:[&>svg]:text-green-400"}},defaultVariants:{variant:"default"}}),A=s.forwardRef((e,t)=>{let{className:r,variant:s,...l}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,w.cn)(k({variant:s}),r),...l})});A.displayName="Alert",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h5",{ref:t,className:(0,w.cn)("mb-1 font-medium leading-none tracking-tight",r),...s})}).displayName="AlertTitle";let R=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,w.cn)("text-sm [&_p]:leading-relaxed",r),...s})});R.displayName="AlertDescription";var P=r(94819);let C=g.z.object({email:g.z.string().min(1,"Email is required").email("Please enter a valid email address"),password:g.z.string().min(1,"Password is required").min(6,"Password must be at least 6 characters"),rememberMe:g.z.boolean().default(!1)});function E(){var e,t;let[r,g]=(0,s.useState)(!1),[j,w]=(0,s.useState)(null),[k,E]=(0,s.useState)(!1),M=(0,l.useRouter)(),{login:S,isAuthenticated:z,isLoading:q}=(0,P.A)(),{register:L,handleSubmit:_,formState:{errors:F,isSubmitting:I},watch:V,trigger:D}=(0,h.mN)({resolver:(0,p.u)(C),defaultValues:{email:"<EMAIL>",password:"awadhesh123",rememberMe:!1},mode:"onChange"});(0,s.useEffect)(()=>{z&&!q&&M.push("/dashboard")},[z,q,M]),(0,s.useEffect)(()=>{let e=e=>{};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[]);let T=async e=>{try{w(null),E(!1),await S(e.email,e.password)?(E(!0),setTimeout(()=>{M.push("/dashboard")},1e3)):w("Invalid email or password. Please try again.")}catch(e){console.error("Login error:",e),w("An unexpected error occurred. Please try again.")}};return(V("email"),V("password"),q)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,a.jsxs)(d.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(d.P.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2,duration:.3},className:"inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-4 shadow-lg",children:(0,a.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-2",children:"PeopleNest"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Enterprise HRMS Platform"}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mt-3",children:[(0,a.jsxs)(N.E,{variant:"secondary",className:"text-xs",children:[(0,a.jsx)(n.A,{className:"w-3 h-3 mr-1"}),"SOC 2 Compliant"]}),(0,a.jsx)(N.E,{variant:"outline",className:"text-xs",children:"Enterprise Ready"})]})]}),(0,a.jsxs)(v.Zp,{className:"shadow-xl border-0 bg-white/80 backdrop-blur-sm",children:[(0,a.jsxs)(v.aR,{className:"space-y-1 pb-6",children:[(0,a.jsx)(v.ZB,{className:"text-2xl font-semibold text-center",children:"Welcome back"}),(0,a.jsx)(v.BT,{className:"text-center",children:"Sign in to your account to continue"})]}),(0,a.jsxs)(v.Wu,{children:[k&&(0,a.jsx)(d.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-4",children:(0,a.jsxs)(A,{className:"border-green-200 bg-green-50",children:[(0,a.jsx)(o,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)(R,{className:"text-green-800",children:"Login successful! Redirecting to dashboard..."})]})}),j&&(0,a.jsx)(d.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-4",children:(0,a.jsxs)(A,{variant:"destructive",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)(R,{children:j})]})}),(0,a.jsxs)("form",{onSubmit:_(T),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)(b.p,{...L("email"),type:"email",placeholder:"Enter your email",label:"Email Address",leftIcon:(0,a.jsx)(m.A,{className:"w-4 h-4"}),error:null==(e=F.email)?void 0:e.message,disabled:I})}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)(b.p,{...L("password"),type:r?"text":"password",placeholder:"Enter your password",label:"Password",leftIcon:(0,a.jsx)(u.A,{className:"w-4 h-4"}),rightIcon:(0,a.jsx)("button",{type:"button",onClick:()=>g(!r),className:"text-muted-foreground hover:text-foreground transition-colors",disabled:I,children:r?(0,a.jsx)(x.A,{className:"w-4 h-4"}):(0,a.jsx)(f.A,{className:"w-4 h-4"})}),error:null==(t=F.password)?void 0:t.message,disabled:I})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)("input",{...L("rememberMe"),type:"checkbox",className:"rounded border-gray-300 text-primary focus:ring-primary disabled:opacity-50",disabled:I}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"Remember me"})]}),(0,a.jsx)("a",{href:"#",className:"text-sm text-muted-foreground hover:text-foreground transition-colors cursor-not-allowed",onClick:e=>e.preventDefault(),children:"Forgot password? (Coming Soon)"})]}),(0,a.jsx)(y.$,{type:"submit",className:"w-full h-11 text-base font-medium",loading:I,disabled:I||k,children:I?"Signing in...":k?"Success!":"Sign in"})]}),!1,(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,a.jsx)("a",{href:"#",className:"text-muted-foreground hover:text-foreground font-medium transition-colors cursor-not-allowed",onClick:e=>e.preventDefault(),children:"Contact your administrator"})]})})]})]}),(0,a.jsx)(d.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8,duration:.3},className:"mt-6 text-center",children:(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Protected by enterprise-grade security and encryption"})})]})})}},28883:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},32919:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(95155),s=r(12115),l=r(59434);let d=(0,r(74466).F)("flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"",error:"border-red-500 focus-visible:ring-red-500",success:"border-green-500 focus-visible:ring-green-500"},size:{default:"h-10",sm:"h-9 px-2 text-xs",lg:"h-11 px-4"}},defaultVariants:{variant:"default",size:"default"}}),i=s.forwardRef((e,t)=>{let{className:r,type:i,variant:n,size:o,leftIcon:c,rightIcon:m,error:u,label:x,helperText:f,id:h,...p}=e,g=h||s.useId(),y=!!u;return(0,a.jsxs)("div",{className:"w-full",children:[x&&(0,a.jsx)("label",{htmlFor:g,className:"block text-sm font-medium text-foreground mb-1",children:x}),(0,a.jsxs)("div",{className:"relative",children:[c&&(0,a.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:c}),(0,a.jsx)("input",{type:i,className:(0,l.cn)(d({variant:y?"error":n,size:o,className:r}),c&&"pl-10",m&&"pr-10"),ref:t,id:g,...p}),m&&(0,a.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:m})]}),(u||f)&&(0,a.jsx)("p",{className:(0,l.cn)("mt-1 text-xs",y?"text-destructive":"text-muted-foreground"),children:u||f})]})});i.displayName="Input"},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>m,ZB:()=>o,Zp:()=>i,aR:()=>n});var a=r(95155),s=r(12115),l=r(59434);let d=(0,r(74466).F)("rounded-lg border bg-card text-card-foreground shadow-sm",{variants:{variant:{default:"border-border",elevated:"shadow-md",outlined:"border-2",ghost:"border-transparent shadow-none"},padding:{none:"",sm:"p-4",default:"p-6",lg:"p-8"}},defaultVariants:{variant:"default",padding:"default"}}),i=s.forwardRef((e,t)=>{let{className:r,variant:s,padding:i,hover:n=!1,...o}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)(d({variant:s,padding:i}),n&&"transition-shadow hover:shadow-md",r),...o})});i.displayName="Card";let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...s})});n.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});o.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...s})});m.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},78749:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85339:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},93717:(e,t,r)=>{Promise.resolve().then(r.bind(r,28497))}},e=>{var t=t=>e(e.s=t);e.O(0,[3706,9380,2426,1712,8441,1684,7358],()=>t(93717)),_N_E=e.O()}]);