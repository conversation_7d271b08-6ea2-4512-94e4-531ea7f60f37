(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7275],{16698:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eA});var t=a(95155),l=a(12115),r=a(17859),i=a(19145),n=a(53904),c=a(91788),d=a(84616),o=a(12486),m=a(57434),x=a(55670),h=a(54416),p=a(47924),j=a(66932),u=a(85213),g=a(95488),f=a(17580),v=a(14186),y=a(33109),N=a(54653),b=a(15968),w=a(5623),C=a(38564),A=a(28883),k=a(17576),E=a(4516),S=a(69074),I=a(55868),R=a(92657),D=a(13717),P=a(62525),F=a(51154),B=a(30285),Z=a(62523),T=a(66695),L=a(91394),J=a(8619),z=a(58829),Y=a(59434);function W(e){let{children:s,onClick:a,variant:i="primary",size:n="md",disabled:c=!1,className:d}=e,[o,m]=(0,l.useState)(!1),x=()=>{m(!0),"vibrate"in navigator&&navigator.vibrate(10)},h=()=>{m(!1),!c&&a&&a()};return(0,t.jsxs)(r.P.button,{className:(0,Y.cn)("relative overflow-hidden rounded-lg font-medium transition-all duration-200 select-none",{primary:"bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/80",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/70",ghost:"bg-transparent text-muted-foreground hover:bg-muted active:bg-muted/80"}[i],{sm:"px-4 py-2 text-sm min-h-[40px]",md:"px-6 py-3 text-base min-h-[48px]",lg:"px-8 py-4 text-lg min-h-[56px]"}[n],c&&"opacity-50 cursor-not-allowed",d),onTouchStart:x,onTouchEnd:h,onMouseDown:x,onMouseUp:h,onMouseLeave:()=>m(!1),whileTap:{scale:.98},disabled:c,children:[(0,t.jsx)(r.P.div,{className:"absolute inset-0 bg-white/20 rounded-lg",initial:{scale:0,opacity:0},animate:o?{scale:1,opacity:1}:{scale:0,opacity:0},transition:{duration:.2}}),(0,t.jsx)("span",{className:"relative z-10",children:s})]})}function q(e){let{children:s,onSwipeLeft:a,onSwipeRight:l,swipeThreshold:i=100,className:n}=e,c=(0,J.d)(0),d=(0,z.G)(c,[-200,-100,0,100,200],[.5,.8,1,.8,.5]),o=(0,z.G)(c,[-200,-100,0,100,200],["#ef4444","#f87171","#ffffff","#10b981","#059669"]);return(0,t.jsxs)(r.P.div,{className:(0,Y.cn)("relative bg-white rounded-lg shadow-sm border border-gray-200",n),style:{x:c,opacity:d,backgroundColor:o},drag:"x",dragConstraints:{left:-200,right:200},dragElastic:.2,onDragEnd:(e,s)=>{let t=s.offset.x;t>i&&l?l():t<-i&&a&&a(),c.set(0)},whileDrag:{scale:1.02},children:[s,(0,t.jsx)(r.P.div,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white font-medium",style:{opacity:(0,z.G)(c,[-200,-100,0],[1,.5,0])},children:"Delete"}),(0,t.jsx)(r.P.div,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-white font-medium",style:{opacity:(0,z.G)(c,[0,100,200],[0,.5,1])},children:"Archive"})]})}function $(e){let{children:s,onRefresh:a,refreshThreshold:i=80,className:n}=e,[c,d]=(0,l.useState)(!1),[o,m]=(0,l.useState)(0),x=(0,l.useRef)(null),h=async()=>{let e=x.current;if(e){if(delete e.dataset.startY,o>=i&&!c){d(!0);try{await a()}finally{d(!1)}}m(0)}},p=Math.min(o/i,1);return(0,t.jsxs)("div",{ref:x,className:(0,Y.cn)("relative overflow-auto",n),onTouchStart:e=>{let s=x.current;if(s&&0===s.scrollTop){let a=e.touches[0];s.dataset.startY=a.clientY.toString()}},onTouchMove:e=>{let s=x.current;if(s&&s.dataset.startY&&0===s.scrollTop){let a=e.touches[0],t=parseInt(s.dataset.startY),l=Math.max(0,a.clientY-t);l>0&&(e.preventDefault(),m(Math.min(l,1.5*i)))}},onTouchEnd:h,children:[(0,t.jsxs)(r.P.div,{className:"absolute top-0 left-0 right-0 flex items-center justify-center bg-primary/10 text-primary",style:{height:o},initial:{opacity:0},animate:{opacity:+(o>0)},children:[(0,t.jsx)(r.P.div,{className:"flex items-center space-x-2",animate:{rotate:c?360:180*p},transition:{duration:+!!c,repeat:c?1/0:0,ease:"linear"},children:(0,t.jsx)("div",{className:"w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full"})}),(0,t.jsx)("span",{className:"ml-2 text-sm font-medium",children:c?"Refreshing...":o>=i?"Release to refresh":"Pull to refresh"})]}),(0,t.jsx)(r.P.div,{style:{paddingTop:o},transition:{type:"spring",damping:20,stiffness:300},children:s})]})}function O(e){let{icon:s,onClick:a,position:l="bottom-right",className:i}=e;return(0,t.jsx)(r.P.button,{className:(0,Y.cn)("fixed z-50 w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-lg flex items-center justify-center",{"bottom-right":"bottom-6 right-6","bottom-left":"bottom-6 left-6","bottom-center":"bottom-6 left-1/2 transform -translate-x-1/2"}[l],i),onClick:a,whileTap:{scale:.9},whileHover:{scale:1.1},initial:{scale:0},animate:{scale:1},transition:{type:"spring",damping:15,stiffness:300},children:s})}var _=a(54165),M=a(62177),G=a(48778),U=a(71153),V=a(71007),H=a(4229),K=a(29869),X=a(19420),Q=a(85057),ee=a(59409),es=a(26126),ea=a(56671);let et=U.Ik({firstName:U.Yj().min(2,"First name must be at least 2 characters"),lastName:U.Yj().min(2,"Last name must be at least 2 characters"),email:U.Yj().email("Invalid email address"),personalEmail:U.Yj().email("Invalid email address").optional().or(U.eu("")),phone:U.Yj().min(10,"Phone number must be at least 10 digits"),dateOfBirth:U.Yj().optional(),gender:U.k5(["male","female","other","prefer_not_to_say"]).optional(),address:U.Ik({street:U.Yj().optional(),city:U.Yj().optional(),state:U.Yj().optional(),zipCode:U.Yj().optional(),country:U.Yj().optional()}).optional(),emergencyContact:U.Ik({name:U.Yj().optional(),relationship:U.Yj().optional(),phone:U.Yj().optional(),email:U.Yj().email().optional().or(U.eu(""))}).optional(),departmentId:U.Yj().min(1,"Department is required"),positionId:U.Yj().min(1,"Position is required"),managerId:U.Yj().optional(),employeeType:U.k5(["full_time","part_time","contract","intern"]),hireDate:U.Yj().min(1,"Hire date is required"),salary:U.ai().min(0,"Salary must be positive").optional(),currency:U.Yj().default("USD"),workLocation:U.k5(["office","remote","hybrid"]),skills:U.YO(U.Yj()).optional(),certifications:U.YO(U.Ik({name:U.Yj(),issuer:U.Yj(),issueDate:U.Yj().optional(),expiryDate:U.Yj().optional()})).optional()});function el(e){var s,a,i,n,c,d,o,m,x,p;let{employee:j,onSubmit:u,onCancel:g,isLoading:f=!1,departments:v=[],positions:y=[],managers:N=[]}=e,[b,w]=(0,l.useState)((null==j?void 0:j.departmentId)||""),[C,k]=(0,l.useState)((null==j?void 0:j.skills)||[]),[R,D]=(0,l.useState)(""),[P,J]=(0,l.useState)((null==j?void 0:j.certifications)||[]),[z,Y]=(0,l.useState)((null==j?void 0:j.avatar)||null),[W,q]=(0,l.useState)(null),{register:$,handleSubmit:O,formState:{errors:_,isSubmitting:U},setValue:el,watch:er,reset:ei}=(0,M.mN)({resolver:(0,G.u)(et),defaultValues:j?{...j,address:j.address||{},emergencyContact:j.emergencyContact||{},skills:j.skills||[],certifications:j.certifications||[]}:{currency:"USD",workLocation:"office",employeeType:"full_time"}}),en=er("departmentId");(0,l.useEffect)(()=>{en&&w(en)},[en]);let ec=y.filter(e=>e.departmentId===b),ed=N.filter(e=>e.departmentId===b),eo=async e=>{try{let s={...e,skills:C,certifications:P,profileImage:W};await u(s),ea.oR.success(j?"Employee updated successfully":"Employee created successfully")}catch(e){ea.oR.error("Failed to save employee"),console.error("Form submission error:",e)}},em=()=>{R.trim()&&!C.includes(R.trim())&&(k([...C,R.trim()]),D(""))},ex=e=>{k(C.filter(s=>s!==e))},eh=()=>{J([...P,{name:"",issuer:"",issueDate:"",expiryDate:""}])},ep=(e,s,a)=>{let t=[...P];t[e]={...t[e],[s]:a},J(t)},ej=e=>{J(P.filter((s,a)=>a!==e))};return(0,t.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-4xl mx-auto",children:(0,t.jsxs)("form",{onSubmit:O(eo),className:"space-y-6",children:[(0,t.jsx)(T.Zp,{children:(0,t.jsx)(T.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(T.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"h-5 w-5"}),j?"Edit Employee":"Add New Employee"]}),(0,t.jsx)(T.BT,{children:j?"Update employee information":"Enter employee details to create a new profile"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(B.$,{type:"button",variant:"outline",onClick:g,disabled:U||f,children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Cancel"]}),(0,t.jsxs)(B.$,{type:"submit",disabled:U||f,className:"min-w-[120px]",children:[U||f?(0,t.jsx)(F.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,t.jsx)(H.A,{className:"h-4 w-4 mr-2"}),j?"Update":"Create"]})]})]})})}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsx)(T.ZB,{className:"text-lg",children:"Profile Photo"})}),(0,t.jsx)(T.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)(L.eu,{className:"h-20 w-20",children:[(0,t.jsx)(L.BK,{src:z||void 0}),(0,t.jsx)(L.q5,{children:(0,t.jsx)(V.A,{className:"h-8 w-8"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"profile-image",className:"cursor-pointer",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 px-4 py-2 border border-border rounded-lg hover:bg-muted",children:[(0,t.jsx)(K.A,{className:"h-4 w-4"}),"Upload Photo"]})}),(0,t.jsx)("input",{id:"profile-image",type:"file",accept:"image/*",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];if(a){q(a);let e=new FileReader;e.onload=e=>{var s;Y(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(a)}},className:"hidden"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"JPG, PNG or GIF. Max size 5MB."})]})]})})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsx)(T.ZB,{className:"text-lg",children:"Personal Information"})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"firstName",children:"First Name *"}),(0,t.jsx)(Z.p,{id:"firstName",...$("firstName"),error:null==(s=_.firstName)?void 0:s.message,leftIcon:(0,t.jsx)(V.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"lastName",children:"Last Name *"}),(0,t.jsx)(Z.p,{id:"lastName",...$("lastName"),error:null==(a=_.lastName)?void 0:a.message,leftIcon:(0,t.jsx)(V.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"email",children:"Work Email *"}),(0,t.jsx)(Z.p,{id:"email",type:"email",...$("email"),error:null==(i=_.email)?void 0:i.message,leftIcon:(0,t.jsx)(A.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"personalEmail",children:"Personal Email"}),(0,t.jsx)(Z.p,{id:"personalEmail",type:"email",...$("personalEmail"),error:null==(n=_.personalEmail)?void 0:n.message,leftIcon:(0,t.jsx)(A.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"phone",children:"Phone Number *"}),(0,t.jsx)(Z.p,{id:"phone",...$("phone"),error:null==(c=_.phone)?void 0:c.message,leftIcon:(0,t.jsx)(X.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"dateOfBirth",children:"Date of Birth"}),(0,t.jsx)(Z.p,{id:"dateOfBirth",type:"date",...$("dateOfBirth"),error:null==(d=_.dateOfBirth)?void 0:d.message,leftIcon:(0,t.jsx)(S.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"gender",children:"Gender"}),(0,t.jsxs)(ee.l6,{onValueChange:e=>el("gender",e),children:[(0,t.jsx)(ee.bq,{children:(0,t.jsx)(ee.yv,{placeholder:"Select gender"})}),(0,t.jsxs)(ee.gC,{children:[(0,t.jsx)(ee.eb,{value:"male",children:"Male"}),(0,t.jsx)(ee.eb,{value:"female",children:"Female"}),(0,t.jsx)(ee.eb,{value:"other",children:"Other"}),(0,t.jsx)(ee.eb,{value:"prefer_not_to_say",children:"Prefer not to say"})]})]})]})]})]})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsx)(T.ZB,{className:"text-lg",children:"Address Information"})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"address.street",children:"Street Address"}),(0,t.jsx)(Z.p,{id:"address.street",...$("address.street"),leftIcon:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"address.city",children:"City"}),(0,t.jsx)(Z.p,{id:"address.city",...$("address.city")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"address.state",children:"State/Province"}),(0,t.jsx)(Z.p,{id:"address.state",...$("address.state")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"address.zipCode",children:"ZIP/Postal Code"}),(0,t.jsx)(Z.p,{id:"address.zipCode",...$("address.zipCode")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"address.country",children:"Country"}),(0,t.jsx)(Z.p,{id:"address.country",...$("address.country")})]})]})]})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsx)(T.ZB,{className:"text-lg",children:"Emergency Contact"})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"emergencyContact.name",children:"Contact Name"}),(0,t.jsx)(Z.p,{id:"emergencyContact.name",...$("emergencyContact.name"),leftIcon:(0,t.jsx)(V.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"emergencyContact.relationship",children:"Relationship"}),(0,t.jsx)(Z.p,{id:"emergencyContact.relationship",...$("emergencyContact.relationship")})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"emergencyContact.phone",children:"Phone Number"}),(0,t.jsx)(Z.p,{id:"emergencyContact.phone",...$("emergencyContact.phone"),leftIcon:(0,t.jsx)(X.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"emergencyContact.email",children:"Email"}),(0,t.jsx)(Z.p,{id:"emergencyContact.email",type:"email",...$("emergencyContact.email"),leftIcon:(0,t.jsx)(A.A,{className:"h-4 w-4"})})]})]})]})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsx)(T.ZB,{className:"text-lg",children:"Employment Information"})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"departmentId",children:"Department *"}),(0,t.jsxs)(ee.l6,{onValueChange:e=>el("departmentId",e),children:[(0,t.jsx)(ee.bq,{children:(0,t.jsx)(ee.yv,{placeholder:"Select department"})}),(0,t.jsx)(ee.gC,{children:v.map(e=>(0,t.jsx)(ee.eb,{value:e.id,children:e.name},e.id))})]}),_.departmentId&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:_.departmentId.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"positionId",children:"Position *"}),(0,t.jsxs)(ee.l6,{onValueChange:e=>el("positionId",e),children:[(0,t.jsx)(ee.bq,{children:(0,t.jsx)(ee.yv,{placeholder:"Select position"})}),(0,t.jsx)(ee.gC,{children:ec.map(e=>(0,t.jsx)(ee.eb,{value:e.id,children:e.title},e.id))})]}),_.positionId&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:_.positionId.message})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"managerId",children:"Manager"}),(0,t.jsxs)(ee.l6,{onValueChange:e=>el("managerId",e),children:[(0,t.jsx)(ee.bq,{children:(0,t.jsx)(ee.yv,{placeholder:"Select manager"})}),(0,t.jsx)(ee.gC,{children:ed.map(e=>(0,t.jsx)(ee.eb,{value:e.id,children:e.name},e.id))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"employeeType",children:"Employment Type *"}),(0,t.jsxs)(ee.l6,{onValueChange:e=>el("employeeType",e),children:[(0,t.jsx)(ee.bq,{children:(0,t.jsx)(ee.yv,{placeholder:"Select employment type"})}),(0,t.jsxs)(ee.gC,{children:[(0,t.jsx)(ee.eb,{value:"full_time",children:"Full Time"}),(0,t.jsx)(ee.eb,{value:"part_time",children:"Part Time"}),(0,t.jsx)(ee.eb,{value:"contract",children:"Contract"}),(0,t.jsx)(ee.eb,{value:"intern",children:"Intern"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"hireDate",children:"Hire Date *"}),(0,t.jsx)(Z.p,{id:"hireDate",type:"date",...$("hireDate"),error:null==(o=_.hireDate)?void 0:o.message,leftIcon:(0,t.jsx)(S.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"workLocation",children:"Work Location *"}),(0,t.jsxs)(ee.l6,{onValueChange:e=>el("workLocation",e),children:[(0,t.jsx)(ee.bq,{children:(0,t.jsx)(ee.yv,{placeholder:"Select work location"})}),(0,t.jsxs)(ee.gC,{children:[(0,t.jsx)(ee.eb,{value:"office",children:"Office"}),(0,t.jsx)(ee.eb,{value:"remote",children:"Remote"}),(0,t.jsx)(ee.eb,{value:"hybrid",children:"Hybrid"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"salary",children:"Annual Salary"}),(0,t.jsx)(Z.p,{id:"salary",type:"number",...$("salary",{valueAsNumber:!0}),error:null==(m=_.salary)?void 0:m.message,leftIcon:(0,t.jsx)(I.A,{className:"h-4 w-4"})})]})]})]})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsx)(T.ZB,{className:"text-lg",children:"Skills & Competencies"})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(Z.p,{placeholder:"Add a skill...",value:R,onChange:e=>D(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),em())}),(0,t.jsx)(B.$,{type:"button",onClick:em,variant:"outline",children:"Add"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:C.map((e,s)=>(0,t.jsxs)(es.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,t.jsx)(h.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>ex(e)})]},s))})]})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(T.ZB,{className:"text-lg",children:"Certifications"}),(0,t.jsx)(B.$,{type:"button",onClick:eh,variant:"outline",size:"sm",children:"Add Certification"})]})}),(0,t.jsx)(T.Wu,{className:"space-y-4",children:P.map((e,s)=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h4",{className:"font-medium",children:["Certification ",s+1]}),(0,t.jsx)(B.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>ej(s),children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,t.jsx)(Z.p,{placeholder:"Certification name",value:e.name,onChange:e=>ep(s,"name",e.target.value)}),(0,t.jsx)(Z.p,{placeholder:"Issuing organization",value:e.issuer,onChange:e=>ep(s,"issuer",e.target.value)}),(0,t.jsx)(Z.p,{type:"date",placeholder:"Issue date",value:e.issueDate,onChange:e=>ep(s,"issueDate",e.target.value)}),(0,t.jsx)(Z.p,{type:"date",placeholder:"Expiry date",value:e.expiryDate,onChange:e=>ep(s,"expiryDate",e.target.value)})]})]},s))})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsx)(T.ZB,{className:"text-lg",children:"Address Information"})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"address.street",children:"Street Address"}),(0,t.jsx)(Z.p,{id:"address.street",...$("address.street"),leftIcon:(0,t.jsx)(E.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"address.city",children:"City"}),(0,t.jsx)(Z.p,{id:"address.city",...$("address.city")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"address.state",children:"State/Province"}),(0,t.jsx)(Z.p,{id:"address.state",...$("address.state")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"address.zipCode",children:"ZIP/Postal Code"}),(0,t.jsx)(Z.p,{id:"address.zipCode",...$("address.zipCode")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"address.country",children:"Country"}),(0,t.jsx)(Z.p,{id:"address.country",...$("address.country")})]})]})]})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsx)(T.ZB,{className:"text-lg",children:"Emergency Contact"})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"emergencyContact.name",children:"Contact Name"}),(0,t.jsx)(Z.p,{id:"emergencyContact.name",...$("emergencyContact.name"),leftIcon:(0,t.jsx)(V.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"emergencyContact.relationship",children:"Relationship"}),(0,t.jsx)(Z.p,{id:"emergencyContact.relationship",...$("emergencyContact.relationship")})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"emergencyContact.phone",children:"Phone Number"}),(0,t.jsx)(Z.p,{id:"emergencyContact.phone",...$("emergencyContact.phone"),leftIcon:(0,t.jsx)(X.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"emergencyContact.email",children:"Email"}),(0,t.jsx)(Z.p,{id:"emergencyContact.email",type:"email",...$("emergencyContact.email"),leftIcon:(0,t.jsx)(A.A,{className:"h-4 w-4"})})]})]})]})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsx)(T.ZB,{className:"text-lg",children:"Employment Information"})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"departmentId",children:"Department *"}),(0,t.jsxs)(ee.l6,{onValueChange:e=>el("departmentId",e),children:[(0,t.jsx)(ee.bq,{children:(0,t.jsx)(ee.yv,{placeholder:"Select department"})}),(0,t.jsx)(ee.gC,{children:v.map(e=>(0,t.jsx)(ee.eb,{value:e.id,children:e.name},e.id))})]}),_.departmentId&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:_.departmentId.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"positionId",children:"Position *"}),(0,t.jsxs)(ee.l6,{onValueChange:e=>el("positionId",e),children:[(0,t.jsx)(ee.bq,{children:(0,t.jsx)(ee.yv,{placeholder:"Select position"})}),(0,t.jsx)(ee.gC,{children:ec.map(e=>(0,t.jsx)(ee.eb,{value:e.id,children:e.title},e.id))})]}),_.positionId&&(0,t.jsx)("p",{className:"text-xs text-red-600 mt-1",children:_.positionId.message})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"managerId",children:"Manager"}),(0,t.jsxs)(ee.l6,{onValueChange:e=>el("managerId",e),children:[(0,t.jsx)(ee.bq,{children:(0,t.jsx)(ee.yv,{placeholder:"Select manager"})}),(0,t.jsx)(ee.gC,{children:ed.map(e=>(0,t.jsx)(ee.eb,{value:e.id,children:e.name},e.id))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"employeeType",children:"Employment Type *"}),(0,t.jsxs)(ee.l6,{onValueChange:e=>el("employeeType",e),children:[(0,t.jsx)(ee.bq,{children:(0,t.jsx)(ee.yv,{placeholder:"Select employment type"})}),(0,t.jsxs)(ee.gC,{children:[(0,t.jsx)(ee.eb,{value:"full_time",children:"Full Time"}),(0,t.jsx)(ee.eb,{value:"part_time",children:"Part Time"}),(0,t.jsx)(ee.eb,{value:"contract",children:"Contract"}),(0,t.jsx)(ee.eb,{value:"intern",children:"Intern"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"hireDate",children:"Hire Date *"}),(0,t.jsx)(Z.p,{id:"hireDate",type:"date",...$("hireDate"),error:null==(x=_.hireDate)?void 0:x.message,leftIcon:(0,t.jsx)(S.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"workLocation",children:"Work Location *"}),(0,t.jsxs)(ee.l6,{onValueChange:e=>el("workLocation",e),children:[(0,t.jsx)(ee.bq,{children:(0,t.jsx)(ee.yv,{placeholder:"Select work location"})}),(0,t.jsxs)(ee.gC,{children:[(0,t.jsx)(ee.eb,{value:"office",children:"Office"}),(0,t.jsx)(ee.eb,{value:"remote",children:"Remote"}),(0,t.jsx)(ee.eb,{value:"hybrid",children:"Hybrid"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(Q.J,{htmlFor:"salary",children:"Annual Salary"}),(0,t.jsx)(Z.p,{id:"salary",type:"number",...$("salary",{valueAsNumber:!0}),error:null==(p=_.salary)?void 0:p.message,leftIcon:(0,t.jsx)(I.A,{className:"h-4 w-4"})})]})]})]})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsx)(T.ZB,{className:"text-lg",children:"Skills & Competencies"})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(Z.p,{placeholder:"Add a skill...",value:R,onChange:e=>D(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),em())}),(0,t.jsx)(B.$,{type:"button",onClick:em,variant:"outline",children:"Add"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:C.map((e,s)=>(0,t.jsxs)(es.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,t.jsx)(h.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>ex(e)})]},s))})]})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(T.ZB,{className:"text-lg",children:"Certifications"}),(0,t.jsx)(B.$,{type:"button",onClick:eh,variant:"outline",size:"sm",children:"Add Certification"})]})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[P.map((e,s)=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("h4",{className:"font-medium",children:["Certification ",s+1]}),(0,t.jsx)(B.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>ej(s),children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,t.jsx)(Z.p,{placeholder:"Certification name",value:e.name,onChange:e=>ep(s,"name",e.target.value)}),(0,t.jsx)(Z.p,{placeholder:"Issuing organization",value:e.issuer,onChange:e=>ep(s,"issuer",e.target.value)}),(0,t.jsx)(Z.p,{type:"date",placeholder:"Issue date",value:e.issueDate,onChange:e=>ep(s,"issueDate",e.target.value)}),(0,t.jsx)(Z.p,{type:"date",placeholder:"Expiry date",value:e.expiryDate,onChange:e=>ep(s,"expiryDate",e.target.value)})]})]},s)),0===P.length&&(0,t.jsx)("p",{className:"text-muted-foreground text-center py-4",children:"No certifications added yet."})]})]})]})})}var er=a(85339),ei=a(36683),en=a(16785),ec=a(69037),ed=a(75525),eo=a(30064);let em=eo.bL,ex=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eo.B8,{ref:s,className:(0,Y.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...l})});ex.displayName=eo.B8.displayName;let eh=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eo.l9,{ref:s,className:(0,Y.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...l})});eh.displayName=eo.l9.displayName;let ep=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(eo.UC,{ref:s,className:(0,Y.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...l})});ep.displayName=eo.UC.displayName;var ej=a(24944),eu=a(79323);let eg=a(91950).i3.API_BASE_URL;class ef{async makeRequest(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let a=(0,eu.Pt)(),t=await fetch("".concat(eg).concat(e),{headers:{"Content-Type":"application/json",...a&&{Authorization:"Bearer ".concat(a)},...s.headers},...s});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"HTTP error! status: ".concat(t.status))}return{data:await t.json()}}catch(e){return console.error("API request failed:",e),{error:e instanceof Error?e.message:"An unexpected error occurred"}}}async getEmployees(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,t]=e;null!=t&&""!==t&&s.append(a,t.toString())});let a=s.toString();return this.makeRequest("/employees".concat(a?"?".concat(a):""))}async getEmployeeById(e){return this.makeRequest("/employees/".concat(e))}async getEmployeeProfile(e){return this.makeRequest("/employees/".concat(e,"/profile"))}async createEmployee(e){return this.makeRequest("/employees",{method:"POST",body:JSON.stringify(e)})}async updateEmployee(e,s){return this.makeRequest("/employees/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deleteEmployee(e){return this.makeRequest("/employees/".concat(e),{method:"DELETE"})}async uploadEmployeeAvatar(e,s){let a=new FormData;a.append("avatar",s);let t=(0,eu.Pt)();try{let s=await fetch("".concat(eg,"/employees/").concat(e,"/avatar"),{method:"POST",headers:{...t&&{Authorization:"Bearer ".concat(t)}},body:a});if(!s.ok){let e=await s.json().catch(()=>({}));throw Error(e.message||"HTTP error! status: ".concat(s.status))}return{data:await s.json()}}catch(e){return console.error("Avatar upload failed:",e),{error:e instanceof Error?e.message:"Avatar upload failed"}}}async bulkUpdateEmployees(e,s){return this.makeRequest("/employees/bulk-update",{method:"POST",body:JSON.stringify({employeeIds:e,updates:s})})}async exportEmployees(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,t]=e;null!=t&&""!==t&&s.append(a,t.toString())});let a=s.toString();return this.makeRequest("/employees/export".concat(a?"?".concat(a):""))}async getDepartments(){return this.makeRequest("/departments")}async getPositions(){return this.makeRequest("/positions")}async getManagers(){return this.makeRequest("/employees/managers")}async searchEmployees(e){return this.getEmployees({search:e,limit:50}).then(e=>{var s;return{...e,data:(null==(s=e.data)?void 0:s.employees)||[]}})}async getEmployeesByDepartment(e){return this.getEmployees({department:e}).then(e=>{var s;return{...e,data:(null==(s=e.data)?void 0:s.employees)||[]}})}async getEmployeesByManager(e){return this.getEmployees({manager:e}).then(e=>{var s;return{...e,data:(null==(s=e.data)?void 0:s.employees)||[]}})}}let ev=new ef,ey=function(e,s){let a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return e.error?(a&&ea.oR.error(e.error),null):(s&&e.data&&ea.oR.success(s),e.data||null)},eN=async(e,s)=>{try{return s(!0),await e()}catch(e){return console.error("Operation failed:",e),ea.oR.error(e instanceof Error?e.message:"Operation failed"),null}finally{s(!1)}};function eb(e){var s;let{employeeId:a,onEdit:i,onClose:n}=e,[d,o]=(0,l.useState)(null),[m,x]=(0,l.useState)(!0),[h,p]=(0,l.useState)("overview");(0,l.useEffect)(()=>{j()},[a]);let j=async()=>{x(!0);try{let e=await ev.getEmployeeProfile(a),s=ey(e,void 0,!0);s&&o(s)}catch(e){console.error("Failed to load employee profile:",e),ea.oR.error("Failed to load employee profile")}finally{x(!1)}},u=async()=>{try{ea.oR.success("Profile exported successfully")}catch(e){ea.oR.error("Failed to export profile")}},g=async()=>{try{ea.oR.success("Profile link copied to clipboard")}catch(e){ea.oR.error("Failed to share profile")}};if(m)return(0,t.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,t.jsx)(F.A,{className:"h-8 w-8 animate-spin"})});if(!d)return(0,t.jsx)("div",{className:"flex items-center justify-center h-96",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(er.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-foreground mb-2",children:"Employee Not Found"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"The requested employee profile could not be loaded."})]})});let f="".concat(d.firstName," ").concat(d.lastName),y=d.recentReviews.length>0?d.recentReviews.reduce((e,s)=>e+s.rating,0)/d.recentReviews.length:0;return(0,t.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-6xl mx-auto space-y-6",children:[(0,t.jsx)(T.Zp,{children:(0,t.jsx)(T.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsxs)(L.eu,{className:"h-20 w-20",children:[(0,t.jsx)(L.BK,{src:d.avatar,alt:f}),(0,t.jsxs)(L.q5,{className:"text-lg",children:[d.firstName[0],d.lastName[0]]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-foreground",children:f}),(0,t.jsx)("p",{className:"text-lg text-muted-foreground",children:d.positionId}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:d.departmentId})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(A.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:d.email})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(X.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:d.phone})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Joined ",new Date(d.hireDate).toLocaleDateString()]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(es.E,{variant:"active"===d.status?"default":"secondary",children:d.status}),(0,t.jsx)(es.E,{variant:"outline",children:d.employeeType.replace("_"," ")}),(0,t.jsx)(es.E,{variant:"outline",children:d.workLocation})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(B.$,{variant:"outline",size:"sm",onClick:g,children:[(0,t.jsx)(ei.A,{className:"h-4 w-4 mr-2"}),"Share"]}),(0,t.jsxs)(B.$,{variant:"outline",size:"sm",onClick:u,children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Export"]}),i&&(0,t.jsxs)(B.$,{size:"sm",onClick:i,children:[(0,t.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,t.jsx)(B.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})]})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(T.Zp,{children:(0,t.jsx)(T.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(C.A,{className:"h-5 w-5 text-yellow-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Performance"}),(0,t.jsxs)("p",{className:"text-lg font-semibold",children:[y.toFixed(1),"/5.0"]})]})]})})}),(0,t.jsx)(T.Zp,{children:(0,t.jsx)(T.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(en.A,{className:"h-5 w-5 text-blue-500 dark:text-blue-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Active Goals"}),(0,t.jsx)("p",{className:"text-lg font-semibold",children:d.activeGoals.length})]})]})})}),(0,t.jsx)(T.Zp,{children:(0,t.jsx)(T.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ec.A,{className:"h-5 w-5 text-green-500 dark:text-green-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Skills"}),(0,t.jsx)("p",{className:"text-lg font-semibold",children:(null==(s=d.skills)?void 0:s.length)||0})]})]})})}),(0,t.jsx)(T.Zp,{children:(0,t.jsx)(T.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 text-purple-500 dark:text-purple-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tenure"}),(0,t.jsxs)("p",{className:"text-lg font-semibold",children:[Math.floor((Date.now()-new Date(d.hireDate).getTime())/31536e6)," years"]})]})]})})})]}),(0,t.jsxs)(em,{value:h,onValueChange:p,children:[(0,t.jsxs)(ex,{className:"grid w-full grid-cols-5",children:[(0,t.jsx)(eh,{value:"overview",children:"Overview"}),(0,t.jsx)(eh,{value:"skills",children:"Skills"}),(0,t.jsx)(eh,{value:"performance",children:"Performance"}),(0,t.jsx)(eh,{value:"goals",children:"Goals"}),(0,t.jsx)(eh,{value:"documents",children:"Documents"})]}),(0,t.jsxs)(ep,{value:"overview",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsxs)(T.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"h-5 w-5"}),"Personal Information"]})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Date of Birth"}),(0,t.jsx)("p",{className:"font-medium",children:d.dateOfBirth?new Date(d.dateOfBirth).toLocaleDateString():"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Gender"}),(0,t.jsx)("p",{className:"font-medium",children:d.gender||"Not specified"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Personal Email"}),(0,t.jsx)("p",{className:"font-medium",children:d.personalEmail||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Phone"}),(0,t.jsx)("p",{className:"font-medium",children:d.phone})]})]}),d.address&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground text-sm",children:"Address"}),(0,t.jsx)("p",{className:"font-medium",children:[d.address.street,d.address.city,d.address.state,d.address.zipCode,d.address.country].filter(Boolean).join(", ")||"Not provided"})]})]})]}),(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsxs)(T.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"h-5 w-5"}),"Employment Details"]})}),(0,t.jsx)(T.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Employee ID"}),(0,t.jsx)("p",{className:"font-medium",children:d.id})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Hire Date"}),(0,t.jsx)("p",{className:"font-medium",children:new Date(d.hireDate).toLocaleDateString()})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Employment Type"}),(0,t.jsx)("p",{className:"font-medium",children:d.employeeType.replace("_"," ")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Work Location"}),(0,t.jsx)("p",{className:"font-medium",children:d.workLocation})]}),d.salary&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Salary"}),(0,t.jsxs)("p",{className:"font-medium",children:[d.currency," ",d.salary.toLocaleString()]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manager"}),(0,t.jsx)("p",{className:"font-medium",children:d.managerId||"Not assigned"})]})]})})]})]}),d.emergencyContact&&(0,t.jsxs)(T.Zp,{children:[(0,t.jsx)(T.aR,{children:(0,t.jsxs)(T.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(ed.A,{className:"h-5 w-5"}),"Emergency Contact"]})}),(0,t.jsx)(T.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Name"}),(0,t.jsx)("p",{className:"font-medium",children:d.emergencyContact.name||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Relationship"}),(0,t.jsx)("p",{className:"font-medium",children:d.emergencyContact.relationship||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Phone"}),(0,t.jsx)("p",{className:"font-medium",children:d.emergencyContact.phone||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-muted-foreground",children:"Email"}),(0,t.jsx)("p",{className:"font-medium",children:d.emergencyContact.email||"Not provided"})]})]})})]})]}),(0,t.jsx)(ep,{value:"skills",className:"space-y-6",children:(0,t.jsxs)(T.Zp,{children:[(0,t.jsxs)(T.aR,{children:[(0,t.jsx)(T.ZB,{children:"Skills & Competencies"}),(0,t.jsx)(T.BT,{children:"Employee skills and proficiency levels"})]}),(0,t.jsx)(T.Wu,{children:d.skills&&d.skills.length>0?(0,t.jsx)("div",{className:"space-y-4",children:d.skills.map(e=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsx)(es.E,{variant:"outline",children:e.category})]}),(0,t.jsx)(ej.k,{value:20*e.level,className:"h-2"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Level ",e.level,"/5"]})]},e.id))}):(0,t.jsx)("p",{className:"text-muted-foreground text-center py-8",children:"No skills recorded yet."})})]})}),(0,t.jsx)(ep,{value:"performance",className:"space-y-6",children:(0,t.jsxs)(T.Zp,{children:[(0,t.jsxs)(T.aR,{children:[(0,t.jsx)(T.ZB,{children:"Performance Reviews"}),(0,t.jsx)(T.BT,{children:"Recent performance evaluations and feedback"})]}),(0,t.jsx)(T.Wu,{children:d.recentReviews&&d.recentReviews.length>0?(0,t.jsx)("div",{className:"space-y-4",children:d.recentReviews.map(e=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"font-medium",children:["Review by ",e.reviewer]}),(0,t.jsx)("div",{className:"flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((s,a)=>(0,t.jsx)(C.A,{className:"h-4 w-4 ".concat(a<e.rating?"text-yellow-500 fill-current":"text-muted-foreground")},a))})]}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:new Date(e.reviewDate).toLocaleDateString()})]}),(0,t.jsx)("p",{className:"text-foreground",children:e.comments})]},e.id))}):(0,t.jsx)("p",{className:"text-muted-foreground text-center py-8",children:"No performance reviews yet."})})]})}),(0,t.jsx)(ep,{value:"goals",className:"space-y-6",children:(0,t.jsxs)(T.Zp,{children:[(0,t.jsxs)(T.aR,{children:[(0,t.jsx)(T.ZB,{children:"Active Goals"}),(0,t.jsx)(T.BT,{children:"Current objectives and progress tracking"})]}),(0,t.jsx)(T.Wu,{children:d.activeGoals&&d.activeGoals.length>0?(0,t.jsx)("div",{className:"space-y-4",children:d.activeGoals.map(e=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.title}),(0,t.jsx)(es.E,{variant:"on_track"===e.status?"default":"secondary",children:e.status.replace("_"," ")})]}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm mb-3",children:e.description}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{children:"Progress"}),(0,t.jsxs)("span",{children:[e.progress,"%"]})]}),(0,t.jsx)(ej.k,{value:e.progress,className:"h-2"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Due: ",new Date(e.dueDate).toLocaleDateString()]})]})]},e.id))}):(0,t.jsx)("p",{className:"text-muted-foreground text-center py-8",children:"No active goals set."})})]})}),(0,t.jsx)(ep,{value:"documents",className:"space-y-6",children:(0,t.jsxs)(T.Zp,{children:[(0,t.jsxs)(T.aR,{children:[(0,t.jsx)(T.ZB,{children:"Documents & Certifications"}),(0,t.jsx)(T.BT,{children:"Employee documents and certifications"})]}),(0,t.jsx)(T.Wu,{children:d.certifications&&d.certifications.length>0?(0,t.jsx)("div",{className:"space-y-4",children:d.certifications.map((e,s)=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.name}),(0,t.jsx)(es.E,{variant:"outline",children:e.issuer})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm text-muted-foreground",children:[e.issueDate&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Issued:"})," ",new Date(e.issueDate).toLocaleDateString()]}),e.expiryDate&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Expires:"})," ",new Date(e.expiryDate).toLocaleDateString()]})]})]},s))}):(0,t.jsx)("p",{className:"text-muted-foreground text-center py-8",children:"No certifications recorded."})})]})})]})]})}var ew=a(25409);let eC=["All","active","on-leave","inactive"];function eA(){let[e,s]=(0,l.useState)([]),[a,J]=(0,l.useState)([]),[z,Y]=(0,l.useState)([]),[M,G]=(0,l.useState)([]),[U,V]=(0,l.useState)(!0),[H,K]=(0,l.useState)(0),[X,Q]=(0,l.useState)(1),[ee,et]=(0,l.useState)(1),[er,ei]=(0,l.useState)(""),[en,ec]=(0,l.useState)("All"),[ed,eo]=(0,l.useState)("All"),[em,ex]=(0,l.useState)("grid"),[eh,ep]=(0,l.useState)("firstName"),[ej,eu]=(0,l.useState)("asc"),[eg,ef]=(0,l.useState)([]),[eA,ek]=(0,l.useState)(!1),[eE,eS]=(0,l.useState)("all"),[eI,eR]=(0,l.useState)([0,2e5]),[eD,eP]=(0,l.useState)(!1),[eF,eB]=(0,l.useState)(!1),[eZ,eT]=(0,l.useState)(!1),[eL,eJ]=(0,l.useState)(!1),[ez,eY]=(0,l.useState)(null),[eW,eq]=(0,l.useState)(null);(0,l.useEffect)(()=>{e$()},[]),(0,l.useEffect)(()=>{eO()},[er,en,ed,eh,ej,X]);let e$=async()=>{V(!0);try{let[e,s,a]=await Promise.all([ev.getDepartments(),ev.getPositions(),ev.getManagers()]),t=ey(e),l=ey(s),r=ey(a);t&&J(t),l&&Y(l),r&&G(r),await eO()}catch(e){console.error("Failed to load initial data:",e),ea.oR.error("Failed to load employee data")}finally{V(!1)}},eO=async()=>{let e={page:X,limit:20,search:er||void 0,department:"All"!==en?en:void 0,status:"All"!==ed?ed.toLowerCase():void 0,sortBy:eh,sortOrder:ej},a=ey(await ev.getEmployees(e));a&&(s(a.employees),K(a.total),et(a.totalPages))},e_=e=>{switch(e){case"active":return"success";case"on-leave":return"warning";case"inactive":return"destructive";default:return"secondary"}},eM=e=>e>=4.5?"text-green-600 dark:text-green-400":e>=4?"text-blue-600 dark:text-blue-400":e>=3.5?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400",eG=e=>{ef(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},eU=e=>{eq(e),eT(!0)},eV=()=>{eq(null),eT(!0)},eH=async e=>{confirm("Are you sure you want to delete this employee?")&&ey(await ev.deleteEmployee(e),"Employee deleted successfully")&&await eO()},eK=async e=>{try{switch(e){case"delete":confirm("Are you sure you want to delete ".concat(eg.length," employees?"))&&ea.oR.success("".concat(eg.length," employees deleted"));break;case"export":let s=await ev.exportEmployees({search:eg.join(",")});ey(s,"Export started");break;default:console.log("Performing ".concat(e," on employees:"),eg)}}catch(e){ea.oR.error("Bulk action failed")}finally{ef([]),eB(!1)}},eX=e=>{eh===e?eu("asc"===ej?"desc":"asc"):(ep(e),eu("asc"))},eQ=async()=>{await eN(eO,eP)},e0=e=>{eH(e)},e4=e=>{ea.oR.info("Archive functionality coming soon")},e2=async e=>{try{if(eW){let s=await ev.updateEmployee(eW.id,e);ey(s,"Employee updated successfully")&&(eT(!1),eq(null),await eO())}else{let s=await ev.createEmployee(e);ey(s,"Employee created successfully")&&(eT(!1),await eO())}}catch(e){console.error("Form submission error:",e),ea.oR.error("Failed to save employee")}};return(0,t.jsx)($,{onRefresh:eQ,className:"flex-1",children:(0,t.jsxs)("div",{className:"space-y-6 p-6",children:[(0,t.jsx)(ew.Y,{title:"Employee Management",subtitle:"Managing ".concat(H," employees across ").concat((null==a?void 0:a.length)||0," departments"),actions:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[eg.length>0&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 mr-4",children:[(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:[eg.length," selected"]}),(0,t.jsxs)(B.$,{variant:"outline",size:"sm",onClick:()=>eB(!eF),children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Bulk Actions"]})]}),(0,t.jsxs)(W,{variant:"secondary",size:"sm",onClick:eQ,disabled:eD,children:[(0,t.jsx)(n.A,{className:"w-4 h-4 mr-2 ".concat(eD?"animate-spin":"")}),eD?"Refreshing...":"Refresh"]}),(0,t.jsxs)(B.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Export"]}),(0,t.jsxs)(B.$,{size:"sm",onClick:eV,children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Add Employee"]})]})}),eF&&eg.length>0&&(0,t.jsx)(r.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{className:"text-sm font-medium text-blue-900",children:["Bulk Actions for ",eg.length," employees:"]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(B.$,{variant:"outline",size:"sm",onClick:()=>eK("send-email"),children:[(0,t.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Send Email"]}),(0,t.jsxs)(B.$,{variant:"outline",size:"sm",onClick:()=>eK("export-data"),children:[(0,t.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Export Data"]}),(0,t.jsxs)(B.$,{variant:"outline",size:"sm",onClick:()=>eK("update-status"),children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Update Status"]})]})]}),(0,t.jsx)(B.$,{variant:"ghost",size:"sm",onClick:()=>eB(!1),children:(0,t.jsx)(h.A,{className:"w-4 h-4"})})]})}),(0,t.jsx)(T.Zp,{children:(0,t.jsxs)(T.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(Z.p,{placeholder:"Search employees by name, email, position, or department...",value:er,onChange:e=>ei(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:en,onChange:e=>ec(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Filter by department",children:[(0,t.jsx)("option",{value:"All",children:"All Departments"},"all"),(null==a?void 0:a.map(e=>(0,t.jsxs)("option",{value:e.name,children:[e.name," Department"]},e.id)))||[]]}),(0,t.jsx)("select",{value:ed,onChange:e=>eo(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Filter by status",children:eC.map(e=>(0,t.jsx)("option",{value:e,children:"All"===e?"All Status":e.charAt(0).toUpperCase()+e.slice(1)},e))}),(0,t.jsx)(B.$,{variant:"outline",size:"icon",onClick:()=>ek(!eA),className:eA?"bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800":"",children:(0,t.jsx)(j.A,{className:"h-4 w-4"})}),(0,t.jsx)(B.$,{variant:"outline",size:"icon",onClick:()=>eX(eh),children:"asc"===ej?(0,t.jsx)(u.A,{className:"h-4 w-4"}):(0,t.jsx)(g.A,{className:"h-4 w-4"})})]})]}),eA&&(0,t.jsx)(r.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"mt-4 pt-4 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Sort By"}),(0,t.jsxs)("select",{value:eh,onChange:e=>ep(e.target.value),className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Sort by field",children:[(0,t.jsx)("option",{value:"name",children:"Name"}),(0,t.jsx)("option",{value:"department",children:"Department"}),(0,t.jsx)("option",{value:"performance",children:"Performance"}),(0,t.jsx)("option",{value:"startDate",children:"Start Date"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Performance Level"}),(0,t.jsxs)("select",{value:eE,onChange:e=>eS(e.target.value),className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary","aria-label":"Filter by performance level",children:[(0,t.jsx)("option",{value:"all",children:"All Performance Levels"}),(0,t.jsx)("option",{value:"high",children:"High (4.5+)"}),(0,t.jsx)("option",{value:"medium",children:"Medium (3.5-4.4)"}),(0,t.jsx)("option",{value:"low",children:"Low (<3.5)"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-foreground mb-2",children:"Salary Range"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(Z.p,{type:"number",placeholder:"Min",value:eI[0],onChange:e=>eR([parseInt(e.target.value)||0,eI[1]]),className:"w-20"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"-"}),(0,t.jsx)(Z.p,{type:"number",placeholder:"Max",value:eI[1],onChange:e=>eR([eI[0],parseInt(e.target.value)||2e5]),className:"w-20"})]})]})]})})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(T.Zp,{children:(0,t.jsx)(T.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:e.length}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Employees"})]})]})})}),(0,t.jsx)(T.Zp,{children:(0,t.jsx)(T.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:e.filter(e=>"active"===e.status).length}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Active"})]})]})})}),(0,t.jsx)(T.Zp,{children:(0,t.jsx)(T.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"h-8 w-8 text-yellow-600 dark:text-yellow-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:e.filter(e=>"on-leave"===e.status).length}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"On Leave"})]})]})})}),(0,t.jsx)(T.Zp,{children:(0,t.jsx)(T.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:e.length>0?(e.reduce((e,s)=>e+s.performance,0)/e.length).toFixed(1):"0.0"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Performance"})]})]})})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing ",e.length," of ",e.length," employees"]}),eg.length>0&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)("span",{className:"text-sm text-blue-600 font-medium",children:[eg.length," selected"]}),(0,t.jsx)(B.$,{variant:"ghost",size:"sm",onClick:()=>ef([]),className:"text-blue-600 hover:text-blue-700",children:"Clear selection"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(B.$,{variant:"grid"===em?"default":"outline",size:"sm",onClick:()=>ex("grid"),children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Grid"]}),(0,t.jsxs)(B.$,{variant:"list"===em?"default":"outline",size:"sm",onClick:()=>ex("list"),children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"List"]})]})]}),"grid"===em&&(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:e.map((e,s)=>(0,t.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},children:(0,t.jsx)(q,{onSwipeLeft:()=>e0(e.id),onSwipeRight:()=>e4(e.id),className:"hover:shadow-lg transition-all cursor-pointer ".concat(eg.includes(e.id)?"ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950/20":""),children:(0,t.jsxs)(T.Zp,{className:"border-0 shadow-none bg-transparent",children:[(0,t.jsx)(T.aR,{className:"pb-4",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"checkbox",checked:eg.includes(e.id),onChange:()=>eG(e.id),className:"absolute top-0 left-0 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500","aria-label":"Select ".concat(e.name)}),(0,t.jsxs)(L.eu,{className:"h-12 w-12 ml-6",children:[(0,t.jsx)(L.BK,{src:e.avatar,alt:e.name}),(0,t.jsx)(L.q5,{children:e.name.split(" ").map(e=>e[0]).join("")})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-foreground",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.position})]})]}),(0,t.jsx)(B.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})}),(0,t.jsxs)(T.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(es.E,{variant:e_(e.status),children:e.status.replace("-"," ")}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 ".concat(eM(e.performance))}),(0,t.jsx)("span",{className:"text-sm font-medium ".concat(eM(e.performance)),children:e.performance})]})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(A.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"truncate",children:e.email})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.department})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(E.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.location})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Started ",new Date(e.startDate).toLocaleDateString()]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(I.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["$",e.salary.toLocaleString()]})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2 pt-4",children:[(0,t.jsxs)(B.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"View"]}),(0,t.jsxs)(B.$,{variant:"outline",size:"sm",className:"flex-1",children:[(0,t.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Edit"]})]})]})]})})},e.id))}),"list"===em&&(0,t.jsx)(T.Zp,{children:(0,t.jsx)(T.Wu,{className:"p-0",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:(0,t.jsx)("input",{type:"checkbox",checked:eg.length===e.length&&e.length>0,onChange:()=>{eg.length===e.length?ef([]):ef(e.map(e=>e.id))},className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500","aria-label":"Select all employees"})}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50",onClick:()=>eX("name"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"Employee"}),"name"===eh&&("asc"===ej?(0,t.jsx)(u.A,{className:"h-4 w-4"}):(0,t.jsx)(g.A,{className:"h-4 w-4"}))]})}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Position"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50",onClick:()=>eX("department"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"Department"}),"department"===eh&&("asc"===ej?(0,t.jsx)(u.A,{className:"h-4 w-4"}):(0,t.jsx)(g.A,{className:"h-4 w-4"}))]})}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground cursor-pointer hover:bg-muted/50",onClick:()=>eX("performance"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"Performance"}),"performance"===eh&&("asc"===ej?(0,t.jsx)(u.A,{className:"h-4 w-4"}):(0,t.jsx)(g.A,{className:"h-4 w-4"}))]})}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Salary"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,t.jsx)("tbody",{children:e.map((e,s)=>(0,t.jsxs)(r.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*s},className:"border-b border-border hover:bg-muted/50 ".concat(eg.includes(e.id)?"bg-blue-50 dark:bg-blue-950/20":""),children:[(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsx)("input",{type:"checkbox",checked:eg.includes(e.id),onChange:()=>eG(e.id),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500","aria-label":"Select ".concat(e.name)})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(L.eu,{className:"h-10 w-10",children:[(0,t.jsx)(L.BK,{src:e.avatar,alt:e.name}),(0,t.jsx)(L.q5,{children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email})]})]})}),(0,t.jsx)("td",{className:"py-4 px-6 text-foreground",children:e.position}),(0,t.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:e.department}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsx)(es.E,{variant:e_(e.status),children:e.status.replace("-"," ")})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 ".concat(eM(e.performance))}),(0,t.jsx)("span",{className:"font-medium ".concat(eM(e.performance)),children:e.performance})]})}),(0,t.jsxs)("td",{className:"py-4 px-6 text-foreground font-medium",children:["$",e.salary.toLocaleString()]}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(B.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(R.A,{className:"h-4 w-4"})}),(0,t.jsx)(B.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(D.A,{className:"h-4 w-4"})}),(0,t.jsx)(B.$,{variant:"ghost",size:"icon",className:"h-8 w-8 text-red-600",children:(0,t.jsx)(P.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})}),(0,t.jsx)(O,{icon:(0,t.jsx)(d.A,{className:"h-6 w-6"}),onClick:eV,position:"bottom-right",className:"lg:hidden"}),(0,t.jsx)(_.lG,{open:eZ,onOpenChange:eT,children:(0,t.jsxs)(_.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(_.c7,{children:(0,t.jsx)(_.L3,{children:eW?"Edit Employee":"Add New Employee"})}),(0,t.jsx)(el,{employee:eW,onSubmit:e2,onCancel:()=>{eT(!1),eq(null)},departments:a,positions:z,managers:M})]})}),(0,t.jsx)(_.lG,{open:eL,onOpenChange:eJ,children:(0,t.jsxs)(_.Cf,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(_.c7,{children:(0,t.jsx)(_.L3,{children:"Employee Profile"})}),ez&&(0,t.jsx)(eb,{employeeId:ez,onEdit:()=>{let s=e.find(e=>e.id===ez);s&&(eJ(!1),eU(s))},onClose:()=>{eJ(!1),eY(null)}})]})}),U&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 flex items-center space-x-3",children:[(0,t.jsx)(F.A,{className:"h-6 w-6 animate-spin"}),(0,t.jsx)("span",{children:"Loading employees..."})]})})]})})}},24944:(e,s,a)=>{"use strict";a.d(s,{k:()=>n});var t=a(95155),l=a(12115),r=a(55863),i=a(59434);let n=l.forwardRef((e,s)=>{let{className:a,value:l,...n}=e;return(0,t.jsx)(r.bL,{ref:s,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...n,children:(0,t.jsx)(r.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(l||0),"%)")}})})});n.displayName=r.bL.displayName},37877:(e,s,a)=>{Promise.resolve().then(a.bind(a,16698))},85057:(e,s,a)=>{"use strict";a.d(s,{J:()=>d});var t=a(95155),l=a(12115),r=a(40968),i=a(74466),n=a(59434);let c=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.b,{ref:s,className:(0,n.cn)(c(),a),...l})});d.displayName=r.b.displayName},91950:(e,s,a)=>{"use strict";a.d(s,{i3:()=>t});let t={BASE_URL:"http://localhost:3002",API_BASE_URL:"http://localhost:3002/api",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3};parseInt("3600"),parseInt("604800"),parseInt("1800000"),parseInt("300000"),parseInt("10485760"),parseInt("10"),parseInt("100"),parseInt("300000"),parseInt("100"),parseInt("900000")}},e=>{var s=s=>e(e.s=s);e.O(0,[3706,9380,6110,1647,2426,5106,5200,1712,5409,8441,1684,7358],()=>s(37877)),_N_E=e.O()}]);