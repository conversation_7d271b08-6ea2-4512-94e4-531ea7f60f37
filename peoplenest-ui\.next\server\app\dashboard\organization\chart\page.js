(()=>{var e={};e.id=74,e.ids=[74],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>z});var a=t(60687),s=t(43210),n=t(50371),i=t(62688);let l=(0,i.A)("zoom-out",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),o=(0,i.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),d=(0,i.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var c=t(31158),m=t(41312),p=t(79410),x=t(57800),u=t(1994),g=t(99270),h=t(80462),b=t(29523),f=t(89667),j=t(44493),v=t(96834),y=t(32584),N=t(96724),k=t(35224);let w={ceo:{id:"ceo-1",name:"Sarah Wilson",title:"Chief Executive Officer",department:"Executive",avatar:"/avatars/sarah-wilson.jpg",email:"<EMAIL>",directReports:["cto-1","cfo-1","coo-1","chro-1"]},employees:{"cto-1":{id:"cto-1",name:"John Smith",title:"Chief Technology Officer",department:"Engineering",avatar:"/avatars/john-smith.jpg",email:"<EMAIL>",directReports:["eng-mgr-1","eng-mgr-2"]},"cfo-1":{id:"cfo-1",name:"Emily Davis",title:"Chief Financial Officer",department:"Finance",avatar:"/avatars/emily-davis.jpg",email:"<EMAIL>",directReports:["fin-mgr-1"]},"coo-1":{id:"coo-1",name:"Michael Brown",title:"Chief Operating Officer",department:"Operations",avatar:"/avatars/michael-brown.jpg",email:"<EMAIL>",directReports:["ops-mgr-1","sales-mgr-1"]},"chro-1":{id:"chro-1",name:"Lisa Johnson",title:"Chief Human Resources Officer",department:"Human Resources",avatar:"/avatars/lisa-johnson.jpg",email:"<EMAIL>",directReports:["hr-mgr-1"]},"eng-mgr-1":{id:"eng-mgr-1",name:"David Chen",title:"Engineering Manager - Frontend",department:"Engineering",avatar:"/avatars/david-chen.jpg",email:"<EMAIL>",directReports:["dev-1","dev-2","dev-3"]},"eng-mgr-2":{id:"eng-mgr-2",name:"Maria Garcia",title:"Engineering Manager - Backend",department:"Engineering",avatar:"/avatars/maria-garcia.jpg",email:"<EMAIL>",directReports:["dev-4","dev-5","dev-6"]},"fin-mgr-1":{id:"fin-mgr-1",name:"Robert Taylor",title:"Finance Manager",department:"Finance",avatar:"/avatars/robert-taylor.jpg",email:"<EMAIL>",directReports:["fin-1","fin-2"]},"ops-mgr-1":{id:"ops-mgr-1",name:"Jennifer Lee",title:"Operations Manager",department:"Operations",avatar:"/avatars/jennifer-lee.jpg",email:"<EMAIL>",directReports:["ops-1","ops-2"]},"sales-mgr-1":{id:"sales-mgr-1",name:"Alex Rodriguez",title:"Sales Manager",department:"Sales",avatar:"/avatars/alex-rodriguez.jpg",email:"<EMAIL>",directReports:["sales-1","sales-2","sales-3"]},"hr-mgr-1":{id:"hr-mgr-1",name:"Amanda White",title:"HR Manager",department:"Human Resources",avatar:"/avatars/amanda-white.jpg",email:"<EMAIL>",directReports:["hr-1","hr-2"]}}},R={Executive:{bg:"bg-purple-50 dark:bg-purple-950/20",border:"border-purple-200 dark:border-purple-800",text:"text-purple-700 dark:text-purple-300",icon:"text-purple-600 dark:text-purple-400"},Engineering:{bg:"bg-blue-50 dark:bg-blue-950/20",border:"border-blue-200 dark:border-blue-800",text:"text-blue-700 dark:text-blue-300",icon:"text-blue-600 dark:text-blue-400"},Finance:{bg:"bg-green-50 dark:bg-green-950/20",border:"border-green-200 dark:border-green-800",text:"text-green-700 dark:text-green-300",icon:"text-green-600 dark:text-green-400"},Operations:{bg:"bg-orange-50 dark:bg-orange-950/20",border:"border-orange-200 dark:border-orange-800",text:"text-orange-700 dark:text-orange-300",icon:"text-orange-600 dark:text-orange-400"},Sales:{bg:"bg-red-50 dark:bg-red-950/20",border:"border-red-200 dark:border-red-800",text:"text-red-700 dark:text-red-300",icon:"text-red-600 dark:text-red-400"},"Human Resources":{bg:"bg-amber-50 dark:bg-amber-950/20",border:"border-amber-200 dark:border-amber-800",text:"text-amber-700 dark:text-amber-300",icon:"text-amber-600 dark:text-amber-400"}},P={totalEmployees:124,departments:6,managers:12,avgTeamSize:8};function z(){let[e,r]=(0,s.useState)(""),[t,i]=(0,s.useState)("All"),[z,A]=(0,s.useState)(100),E=async()=>{console.log("Refreshing organization chart...")},C=({employee:e,isRoot:r=!1})=>{let t=R[e.department]||{bg:"bg-muted",border:"border-border",text:"text-muted-foreground",icon:"text-muted-foreground"};return(0,a.jsx)(n.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3},className:`p-4 rounded-lg border-2 ${t.bg} ${t.border}
                   ${r?"ring-2 ring-primary shadow-lg":""}
                   hover:shadow-lg hover:scale-105 transition-all duration-200 cursor-pointer min-w-[200px]`,children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(y.eu,{className:"h-12 w-12 ring-2 ring-white dark:ring-gray-800",children:[(0,a.jsx)(y.BK,{src:e.avatar,alt:e.name}),(0,a.jsx)(y.q5,{className:`${t.bg} ${t.text} font-semibold`,children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-sm truncate text-foreground",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:e.title}),(0,a.jsx)(v.E,{variant:"outline",className:`text-xs mt-1 ${t.text} ${t.border} bg-transparent`,children:e.department})]})]})})},$=(e,r=0)=>e&&0!==e.length?(0,a.jsx)("div",{className:"flex flex-col items-center space-y-8",children:(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-6",children:e.map(e=>{let t=w.employees[e];return t?(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(C,{employee:t}),t.directReports&&t.directReports.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-px h-8 bg-gradient-to-b from-border to-muted-foreground/30"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute top-0 left-1/2 w-px h-4 bg-gradient-to-b from-muted-foreground/30 to-border transform -translate-x-1/2"}),$(t.directReports,r+1)]})]})]},e):null})})}):null;return(0,a.jsx)(k.k,{onRefresh:E,className:"flex-1",children:(0,a.jsxs)("div",{className:"space-y-6 p-6",children:[(0,a.jsx)(N.Y,{title:"Organization Chart",subtitle:`Visualizing ${P.totalEmployees} employees across ${P.departments} departments`,actions:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.$,{variant:"outline",size:"sm",onClick:()=>{A(e=>Math.max(e-25,50))},children:(0,a.jsx)(l,{className:"w-4 h-4"})}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground px-2",children:[z,"%"]}),(0,a.jsx)(b.$,{variant:"outline",size:"sm",onClick:()=>{A(e=>Math.min(e+25,200))},children:(0,a.jsx)(o,{className:"w-4 h-4"})}),(0,a.jsx)(b.$,{variant:"outline",size:"sm",onClick:()=>{A(100)},children:(0,a.jsx)(d,{className:"w-4 h-4"})}),(0,a.jsxs)(b.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Export"]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(j.Zp,{className:"border-purple-200 dark:border-purple-800 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20",children:(0,a.jsx)(j.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-purple-600 dark:text-purple-400",children:"Total Employees"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-900 dark:text-purple-100",children:P.totalEmployees})]}),(0,a.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full",children:(0,a.jsx)(m.A,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"})})]})})}),(0,a.jsx)(j.Zp,{className:"border-blue-200 dark:border-blue-800 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20",children:(0,a.jsx)(j.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-600 dark:text-blue-400",children:"Departments"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-900 dark:text-blue-100",children:P.departments})]}),(0,a.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full",children:(0,a.jsx)(p.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})})]})})}),(0,a.jsx)(j.Zp,{className:"border-green-200 dark:border-green-800 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20",children:(0,a.jsx)(j.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:"Managers"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-900 dark:text-green-100",children:P.managers})]}),(0,a.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-full",children:(0,a.jsx)(x.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"})})]})})}),(0,a.jsx)(j.Zp,{className:"border-amber-200 dark:border-amber-800 bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950/20 dark:to-amber-900/20",children:(0,a.jsx)(j.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-amber-600 dark:text-amber-400",children:"Avg Team Size"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-amber-900 dark:text-amber-100",children:P.avgTeamSize})]}),(0,a.jsx)("div",{className:"p-3 bg-amber-100 dark:bg-amber-900/30 rounded-full",children:(0,a.jsx)(u.A,{className:"h-8 w-8 text-amber-600 dark:text-amber-400"})})]})})})]}),(0,a.jsx)(j.Zp,{children:(0,a.jsx)(j.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,a.jsx)(f.p,{placeholder:"Search employees...",value:e,onChange:e=>r(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(b.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Department Filter"]})]})})}),(0,a.jsxs)(j.Zp,{children:[(0,a.jsx)(j.aR,{children:(0,a.jsxs)(j.ZB,{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 mr-2"}),"Organization Structure"]})}),(0,a.jsx)(j.Wu,{children:(0,a.jsx)("div",{className:"overflow-auto p-8 bg-gradient-to-br from-muted/20 via-background to-muted/30 rounded-lg border border-muted",style:{transform:`scale(${z/100})`,transformOrigin:"top center"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-8",children:[(0,a.jsx)(C,{employee:w.ceo,isRoot:!0}),w.ceo.directReports&&w.ceo.directReports.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-px h-8 bg-gradient-to-b from-purple-300 to-muted-foreground/30 dark:from-purple-700 dark:to-muted-foreground/30"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute top-0 left-1/2 w-px h-4 bg-gradient-to-b from-muted-foreground/30 to-purple-300 dark:to-purple-700 transform -translate-x-1/2"}),$(w.ceo.directReports)]})]})]})})})]}),(0,a.jsxs)(j.Zp,{children:[(0,a.jsx)(j.aR,{children:(0,a.jsxs)(j.ZB,{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 mr-2"}),"Department Legend"]})}),(0,a.jsx)(j.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:Object.entries(R).map(([e,r])=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg border transition-all hover:shadow-md",children:[(0,a.jsx)("div",{className:`w-6 h-6 rounded-full border-2 ${r.bg} ${r.border} flex items-center justify-center`,children:(0,a.jsx)("div",{className:`w-3 h-3 rounded-full ${r.bg.replace("50","200").replace("950/20","800")}`})}),(0,a.jsx)("span",{className:`text-sm font-medium ${r.text}`,children:e})]},e))})})]})]})})}},17135:(e,r,t)=>{"use strict";t.d(r,{d:()=>l});var a=t(24342),s=t(43210),n=t(32582),i=t(72789);function l(e){let r=(0,i.M)(()=>(0,a.OQ)(e)),{isStatic:t}=(0,s.useContext)(n.Q);if(t){let[,t]=(0,s.useState)(e);(0,s.useEffect)(()=>r.on("change",t),[])}return r}},18620:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=t(65239),s=t(48088),n=t(88170),i=t.n(n),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d={children:["",{children:["dashboard",{children:["organization",{children:["chart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20930)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\chart\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\chart\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/organization/chart/page",pathname:"/dashboard/organization/chart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20930:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\organization\\\\chart\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\chart\\page.tsx","default")},28101:(e,r,t)=>{Promise.resolve().then(t.bind(t,20930))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35224:(e,r,t)=>{"use strict";t.d(r,{k:()=>d});var a=t(60687),s=t(43210),n=t(17135),i=t(53342),l=t(50371),o=t(78122);function d({onRefresh:e,children:r,className:t="",threshold:d=80,disabled:c=!1}){let[m,p]=(0,s.useState)(!1),[x,u]=(0,s.useState)(!1),g=(0,s.useRef)(null),h=(0,n.d)(0),b=(0,i.G)(h,[0,d],[0,1]),f=(0,i.G)(h,[0,d],[0,180]),j=(0,i.G)(h,[0,d],[.8,1]),v=async(r,t)=>{if(!c&&!m&&x){if(u(!1),t.offset.y>=d){p(!0);try{await e()}catch(e){console.error("Refresh failed:",e)}finally{p(!1)}}h.set(0)}};return(0,a.jsxs)("div",{className:`relative overflow-hidden ${t}`,children:[(0,a.jsx)(l.P.div,{className:"absolute top-0 left-0 right-0 z-10 flex items-center justify-center",style:{opacity:b,y:(0,i.G)(h,e=>e-60)},children:(0,a.jsx)(l.P.div,{className:"flex items-center justify-center w-12 h-12 bg-primary rounded-full shadow-lg",style:{scale:j,rotate:m?360:f},animate:m?{rotate:360}:{},transition:m?{duration:1,repeat:1/0,ease:"linear"}:{},children:(0,a.jsx)(o.A,{className:"w-6 h-6 text-primary-foreground"})})}),(0,a.jsx)(l.P.div,{ref:g,className:"h-full overflow-auto",style:{y:h},onPanStart:()=>{if(c||m)return;let e=g.current;e&&0===e.scrollTop&&u(!0)},onPan:(e,r)=>{!c&&!m&&x&&r.delta.y>0&&h.set(Math.min(r.offset.y,1.5*d))},onPanEnd:v,drag:"y",dragConstraints:{top:0,bottom:0},dragElastic:{top:.3,bottom:0},children:r})]})}},53342:(e,r,t)=>{"use strict";t.d(r,{G:()=>c});var a=t(19331),s=t(72789),n=t(23671),i=t(15124),l=t(17135);function o(e,r){let t=(0,l.d)(r()),a=()=>t.set(r());return a(),(0,i.E)(()=>{let r=()=>n.Gt.preRender(a,!1,!0),t=e.map(e=>e.on("change",r));return()=>{t.forEach(e=>e()),(0,n.WG)(a)}}),t}var d=t(24342);function c(e,r,t,s){if("function"==typeof e){d.bt.current=[],e();let r=o(d.bt.current,e);return d.bt.current=void 0,r}let n="function"==typeof r?r:function(...e){let r=!Array.isArray(e[0]),t=r?0:-1,s=e[0+t],n=e[1+t],i=e[2+t],l=e[3+t],o=(0,a.G)(n,i,l);return r?o(s):o}(r,t,s);return Array.isArray(e)?m(e,n):m([e],([e])=>n(e))}function m(e,r){let t=(0,s.M)(()=>[]);return o(e,()=>{t.length=0;let a=e.length;for(let r=0;r<a;r++)t[r]=e[r].get();return r(t)})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64549:(e,r,t)=>{Promise.resolve().then(t.bind(t,12934))},79551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,912,18,722,632,809,456],()=>t(18620));module.exports=a})();