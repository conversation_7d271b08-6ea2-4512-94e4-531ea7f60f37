(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7569],{82275:(e,s,a)=>{Promise.resolve().then(a.bind(a,96167))},96167:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>$});var t=a(95155),l=a(12115),r=a(17859),n=a(47298),i=a(51976),c=a(17576),d=a(87949),o=a(69074),x=a(40646),m=a(14186),p=a(54861),h=a(1243),u=a(91788),j=a(84616),v=a(33109),g=a(17580),N=a(47924),f=a(92657),y=a(13717),w=a(81497),A=a(30285),b=a(62523),k=a(66695),D=a(26126),q=a(25409),T=a(91394),R=a(83540),C=a(3401),L=a(94754),P=a(96025),S=a(52071),E=a(85755),F=a(64683),Z=a(56690),B=a(90170),W=a(54811);let z=[{id:1,employeeId:1,employeeName:"Sarah Johnson",department:"Engineering",leaveType:"vacation",startDate:"2024-02-15",endDate:"2024-02-19",days:5,status:"approved",reason:"Family vacation",appliedDate:"2024-01-20",approver:"Mike Chen",avatar:"/avatars/sarah.jpg"},{id:2,employeeId:2,employeeName:"David Wilson",department:"Product",leaveType:"sick",startDate:"2024-01-28",endDate:"2024-01-30",days:3,status:"approved",reason:"Medical appointment",appliedDate:"2024-01-25",approver:"Emily Davis",avatar:"/avatars/david.jpg"},{id:3,employeeId:3,employeeName:"Lisa Park",department:"Design",leaveType:"personal",startDate:"2024-02-05",endDate:"2024-02-05",days:1,status:"pending",reason:"Personal matters",appliedDate:"2024-01-30",approver:"Tom Anderson",avatar:"/avatars/lisa.jpg"},{id:4,employeeId:4,employeeName:"James Liu",department:"Engineering",leaveType:"vacation",startDate:"2024-03-01",endDate:"2024-03-15",days:11,status:"rejected",reason:"Extended vacation",appliedDate:"2024-01-15",approver:"Mike Chen",avatar:"/avatars/james.jpg"}],I=[{name:"Vacation",value:45,color:"#0088FE",icon:n.A},{name:"Sick Leave",value:28,color:"#00C49F",icon:i.A},{name:"Personal",value:15,color:"#FFBB28",icon:c.A},{name:"Training",value:8,color:"#FF8042",icon:d.A},{name:"Other",value:4,color:"#8884D8",icon:o.A}],K=[{month:"Jan",vacation:12,sick:8,personal:4,training:2},{month:"Feb",vacation:15,sick:6,personal:5,training:3},{month:"Mar",vacation:18,sick:7,personal:3,training:1},{month:"Apr",vacation:22,sick:5,personal:6,training:4},{month:"May",vacation:25,sick:9,personal:4,training:2},{month:"Jun",vacation:28,sick:4,personal:7,training:3}],M={totalRequests:z.length,pendingRequests:z.filter(e=>"pending"===e.status).length,approvedRequests:z.filter(e=>"approved"===e.status).length,rejectedRequests:z.filter(e=>"rejected"===e.status).length,avgProcessingTime:2.5,totalDaysRequested:z.reduce((e,s)=>e+s.days,0)};function $(){let[e,s]=(0,l.useState)(""),[a,$]=(0,l.useState)("All"),[_,J]=(0,l.useState)("All"),[V,O]=(0,l.useState)("All"),[U,Y]=(0,l.useState)("requests"),G=z.filter(s=>{let t=s.employeeName.toLowerCase().includes(e.toLowerCase())||s.department.toLowerCase().includes(e.toLowerCase())||s.reason.toLowerCase().includes(e.toLowerCase()),l="All"===a||s.status===a,r="All"===_||s.leaveType===_,n="All"===V||s.department===V;return t&&l&&r&&n}),H=e=>{switch(e){case"approved":return"success";case"pending":return"warning";case"rejected":return"destructive";default:return"secondary"}},Q=e=>{switch(e){case"approved":return(0,t.jsx)(x.A,{className:"h-4 w-4"});case"pending":return(0,t.jsx)(m.A,{className:"h-4 w-4"});case"rejected":return(0,t.jsx)(p.A,{className:"h-4 w-4"});default:return(0,t.jsx)(h.A,{className:"h-4 w-4"})}},X=e=>{switch(e){case"vacation":return(0,t.jsx)(n.A,{className:"h-4 w-4"});case"sick":return(0,t.jsx)(i.A,{className:"h-4 w-4"});case"personal":return(0,t.jsx)(c.A,{className:"h-4 w-4"});case"training":return(0,t.jsx)(d.A,{className:"h-4 w-4"});default:return(0,t.jsx)(o.A,{className:"h-4 w-4"})}},ee=e=>{switch(e){case"vacation":return"text-blue-600 dark:text-blue-400";case"sick":return"text-red-600 dark:text-red-400";case"personal":return"text-green-600 dark:text-green-400";case"training":return"text-purple-600 dark:text-purple-400";default:return"text-muted-foreground"}};return(0,t.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,t.jsx)(q.Y,{title:"Leave Management",subtitle:"Managing ".concat(M.totalRequests," leave requests with ").concat(M.pendingRequests," pending approval"),actions:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(A.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Export Report"]}),(0,t.jsxs)(A.$,{size:"sm",children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2"}),"New Request"]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,t.jsx)(k.Zp,{children:(0,t.jsx)(k.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(o.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:M.totalRequests}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Requests"})]})]})})}),(0,t.jsx)(k.Zp,{children:(0,t.jsx)(k.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.A,{className:"h-8 w-8 text-yellow-600 dark:text-yellow-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:M.pendingRequests}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pending"})]})]})})}),(0,t.jsx)(k.Zp,{children:(0,t.jsx)(k.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:M.approvedRequests}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Approved"})]})]})})}),(0,t.jsx)(k.Zp,{children:(0,t.jsx)(k.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-8 w-8 text-red-600 dark:text-red-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:M.rejectedRequests}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Rejected"})]})]})})}),(0,t.jsx)(k.Zp,{children:(0,t.jsx)(k.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:M.avgProcessingTime}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Days"})]})]})})}),(0,t.jsx)(k.Zp,{children:(0,t.jsx)(k.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.A,{className:"h-8 w-8 text-teal-600 dark:text-teal-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:M.totalDaysRequested}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Days"})]})]})})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(A.$,{variant:"requests"===U?"default":"outline",size:"sm",onClick:()=>Y("requests"),children:"Requests"}),(0,t.jsx)(A.$,{variant:"calendar"===U?"default":"outline",size:"sm",onClick:()=>Y("calendar"),children:"Calendar"}),(0,t.jsx)(A.$,{variant:"analytics"===U?"default":"outline",size:"sm",onClick:()=>Y("analytics"),children:"Analytics"})]}),(0,t.jsx)(k.Zp,{children:(0,t.jsx)(k.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(N.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(b.p,{placeholder:"Search by employee name, department, or reason...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:a,onChange:e=>$(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"All",children:"All Status"}),(0,t.jsx)("option",{value:"pending",children:"Pending"}),(0,t.jsx)("option",{value:"approved",children:"Approved"}),(0,t.jsx)("option",{value:"rejected",children:"Rejected"})]}),(0,t.jsxs)("select",{value:_,onChange:e=>J(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"All",children:"All Types"}),(0,t.jsx)("option",{value:"vacation",children:"Vacation"}),(0,t.jsx)("option",{value:"sick",children:"Sick Leave"}),(0,t.jsx)("option",{value:"personal",children:"Personal"}),(0,t.jsx)("option",{value:"training",children:"Training"})]}),(0,t.jsxs)("select",{value:V,onChange:e=>O(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"All",children:"All Departments"}),(0,t.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,t.jsx)("option",{value:"Product",children:"Product"}),(0,t.jsx)("option",{value:"Design",children:"Design"})]})]})]})})}),"requests"===U?(0,t.jsx)(k.Zp,{children:(0,t.jsx)(k.Wu,{className:"p-0",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Employee"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Leave Type"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Dates"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Days"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Reason"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Approver"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,t.jsx)("tbody",{children:G.map((e,s)=>(0,t.jsxs)(r.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*s},className:"border-b border-border hover:bg-muted/50",children:[(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(T.eu,{className:"h-10 w-10",children:[(0,t.jsx)(T.BK,{src:e.avatar,alt:e.employeeName}),(0,t.jsx)(T.q5,{children:e.employeeName.split(" ").map(e=>e[0]).join("")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:e.employeeName}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.department})]})]})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:ee(e.leaveType),children:X(e.leaveType)}),(0,t.jsx)("span",{className:"capitalize",children:e.leaveType})]})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:new Date(e.startDate).toLocaleDateString()}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["to ",new Date(e.endDate).toLocaleDateString()]})]})}),(0,t.jsx)("td",{className:"py-4 px-6 font-medium",children:e.days}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)(D.E,{variant:H(e.status),className:"flex items-center space-x-1 w-fit",children:[Q(e.status),(0,t.jsx)("span",{className:"ml-1",children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsx)("p",{className:"text-sm max-w-xs truncate",children:e.reason})}),(0,t.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:e.approver}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(A.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(f.A,{className:"h-4 w-4"})}),(0,t.jsx)(A.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(y.A,{className:"h-4 w-4"})}),(0,t.jsx)(A.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})}):"calendar"===U?(0,t.jsxs)(k.Zp,{children:[(0,t.jsxs)(k.aR,{children:[(0,t.jsx)(k.ZB,{children:"Leave Calendar"}),(0,t.jsx)(k.BT,{children:"Visual overview of upcoming leave requests"})]}),(0,t.jsx)(k.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:z.filter(e=>"approved"===e.status).map((e,s)=>(0,t.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},className:"p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(T.eu,{className:"h-8 w-8",children:[(0,t.jsx)(T.BK,{src:e.avatar,alt:e.employeeName}),(0,t.jsx)(T.q5,{children:e.employeeName.split(" ").map(e=>e[0]).join("")})]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium text-sm",children:e.employeeName}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:e.department})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("p",{className:"text-sm font-medium",children:[e.days," days"]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground capitalize",children:e.leaveType})]})]}),(0,t.jsx)("div",{className:"mt-2 text-sm",children:(0,t.jsxs)("p",{children:[new Date(e.startDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]})})]},e.id))})})})]}):(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(k.Zp,{children:[(0,t.jsxs)(k.aR,{children:[(0,t.jsxs)(k.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Monthly Leave Trends"})]}),(0,t.jsx)(k.BT,{children:"Leave requests by type over time"})]}),(0,t.jsx)(k.Wu,{children:(0,t.jsx)(R.u,{width:"100%",height:300,children:(0,t.jsxs)(C.E,{data:K,children:[(0,t.jsx)(L.d,{strokeDasharray:"3 3"}),(0,t.jsx)(P.W,{dataKey:"month"}),(0,t.jsx)(S.h,{}),(0,t.jsx)(E.m,{}),(0,t.jsx)(F.s,{}),(0,t.jsx)(Z.y,{dataKey:"vacation",stackId:"a",fill:"#0088FE",name:"Vacation"}),(0,t.jsx)(Z.y,{dataKey:"sick",stackId:"a",fill:"#00C49F",name:"Sick Leave"}),(0,t.jsx)(Z.y,{dataKey:"personal",stackId:"a",fill:"#FFBB28",name:"Personal"}),(0,t.jsx)(Z.y,{dataKey:"training",stackId:"a",fill:"#FF8042",name:"Training"})]})})})]}),(0,t.jsxs)(k.Zp,{children:[(0,t.jsxs)(k.aR,{children:[(0,t.jsxs)(k.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(o.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Leave Type Distribution"})]}),(0,t.jsx)(k.BT,{children:"Breakdown of leave types"})]}),(0,t.jsx)(k.Wu,{children:(0,t.jsx)(R.u,{width:"100%",height:300,children:(0,t.jsxs)(B.r,{children:[(0,t.jsx)(Pie,{data:I,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:s,value:a}=e;return"".concat(s," ").concat(a,"%")},outerRadius:80,fill:"#8884d8",dataKey:"value",children:I.map((e,s)=>(0,t.jsx)(W.f,{fill:e.color},"cell-".concat(s)))}),(0,t.jsx)(E.m,{})]})})})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3706,9380,6110,1647,7463,1932,2446,1712,5409,8441,1684,7358],()=>s(82275)),_N_E=e.O()}]);