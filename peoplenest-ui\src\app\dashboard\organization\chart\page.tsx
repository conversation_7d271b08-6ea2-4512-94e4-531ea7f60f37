"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import {
  Network,
  Users,
  Building,
  Briefcase,
  Search,
  Filter,
  Download,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Maximize
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Header } from "@/components/layout/header"
import { PullToRefresh } from "@/components/ui/pull-to-refresh"

// Mock organizational data
const orgData = {
  ceo: {
    id: "ceo-1",
    name: "<PERSON>",
    title: "Chief Executive Officer",
    department: "Executive",
    avatar: "/avatars/sarah-wilson.jpg",
    email: "<EMAIL>",
    directReports: ["cto-1", "cfo-1", "coo-1", "chro-1"]
  },
  employees: {
    "cto-1": {
      id: "cto-1",
      name: "<PERSON>",
      title: "Chief Technology Officer",
      department: "Engineering",
      avatar: "/avatars/john-smith.jpg",
      email: "<EMAIL>",
      directReports: ["eng-mgr-1", "eng-mgr-2"]
    },
    "cfo-1": {
      id: "cfo-1",
      name: "Emily Davis",
      title: "Chief Financial Officer",
      department: "Finance",
      avatar: "/avatars/emily-davis.jpg",
      email: "<EMAIL>",
      directReports: ["fin-mgr-1"]
    },
    "coo-1": {
      id: "coo-1",
      name: "Michael Brown",
      title: "Chief Operating Officer",
      department: "Operations",
      avatar: "/avatars/michael-brown.jpg",
      email: "<EMAIL>",
      directReports: ["ops-mgr-1", "sales-mgr-1"]
    },
    "chro-1": {
      id: "chro-1",
      name: "Lisa Johnson",
      title: "Chief Human Resources Officer",
      department: "Human Resources",
      avatar: "/avatars/lisa-johnson.jpg",
      email: "<EMAIL>",
      directReports: ["hr-mgr-1"]
    },
    "eng-mgr-1": {
      id: "eng-mgr-1",
      name: "David Chen",
      title: "Engineering Manager - Frontend",
      department: "Engineering",
      avatar: "/avatars/david-chen.jpg",
      email: "<EMAIL>",
      directReports: ["dev-1", "dev-2", "dev-3"]
    },
    "eng-mgr-2": {
      id: "eng-mgr-2",
      name: "Maria Garcia",
      title: "Engineering Manager - Backend",
      department: "Engineering",
      avatar: "/avatars/maria-garcia.jpg",
      email: "<EMAIL>",
      directReports: ["dev-4", "dev-5", "dev-6"]
    },
    "fin-mgr-1": {
      id: "fin-mgr-1",
      name: "Robert Taylor",
      title: "Finance Manager",
      department: "Finance",
      avatar: "/avatars/robert-taylor.jpg",
      email: "<EMAIL>",
      directReports: ["fin-1", "fin-2"]
    },
    "ops-mgr-1": {
      id: "ops-mgr-1",
      name: "Jennifer Lee",
      title: "Operations Manager",
      department: "Operations",
      avatar: "/avatars/jennifer-lee.jpg",
      email: "<EMAIL>",
      directReports: ["ops-1", "ops-2"]
    },
    "sales-mgr-1": {
      id: "sales-mgr-1",
      name: "Alex Rodriguez",
      title: "Sales Manager",
      department: "Sales",
      avatar: "/avatars/alex-rodriguez.jpg",
      email: "<EMAIL>",
      directReports: ["sales-1", "sales-2", "sales-3"]
    },
    "hr-mgr-1": {
      id: "hr-mgr-1",
      name: "Amanda White",
      title: "HR Manager",
      department: "Human Resources",
      avatar: "/avatars/amanda-white.jpg",
      email: "<EMAIL>",
      directReports: ["hr-1", "hr-2"]
    }
  }
}

const departmentColors = {
  "Executive": {
    bg: "bg-purple-50 dark:bg-purple-950/20",
    border: "border-purple-200 dark:border-purple-800",
    text: "text-purple-700 dark:text-purple-300",
    icon: "text-purple-600 dark:text-purple-400"
  },
  "Engineering": {
    bg: "bg-blue-50 dark:bg-blue-950/20",
    border: "border-blue-200 dark:border-blue-800",
    text: "text-blue-700 dark:text-blue-300",
    icon: "text-blue-600 dark:text-blue-400"
  },
  "Finance": {
    bg: "bg-green-50 dark:bg-green-950/20",
    border: "border-green-200 dark:border-green-800",
    text: "text-green-700 dark:text-green-300",
    icon: "text-green-600 dark:text-green-400"
  },
  "Operations": {
    bg: "bg-orange-50 dark:bg-orange-950/20",
    border: "border-orange-200 dark:border-orange-800",
    text: "text-orange-700 dark:text-orange-300",
    icon: "text-orange-600 dark:text-orange-400"
  },
  "Sales": {
    bg: "bg-red-50 dark:bg-red-950/20",
    border: "border-red-200 dark:border-red-800",
    text: "text-red-700 dark:text-red-300",
    icon: "text-red-600 dark:text-red-400"
  },
  "Human Resources": {
    bg: "bg-amber-50 dark:bg-amber-950/20",
    border: "border-amber-200 dark:border-amber-800",
    text: "text-amber-700 dark:text-amber-300",
    icon: "text-amber-600 dark:text-amber-400"
  }
}

const orgStats = {
  totalEmployees: 124,
  departments: 6,
  managers: 12,
  avgTeamSize: 8
}

export default function OrganizationChartPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDepartment, setSelectedDepartment] = useState("All")
  const [zoomLevel, setZoomLevel] = useState(100)

  const handleRefresh = async () => {
    console.log("Refreshing organization chart...")
  }

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 25, 200))
  }

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 25, 50))
  }

  const handleResetZoom = () => {
    setZoomLevel(100)
  }

  const EmployeeCard = ({ employee, isRoot = false }) => {
    const deptColors = departmentColors[employee.department] || {
      bg: "bg-muted",
      border: "border-border",
      text: "text-muted-foreground",
      icon: "text-muted-foreground"
    }

    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className={`p-4 rounded-lg border-2 ${deptColors.bg} ${deptColors.border}
                   ${isRoot ? 'ring-2 ring-primary shadow-lg' : ''}
                   hover:shadow-lg hover:scale-105 transition-all duration-200 cursor-pointer min-w-[200px]`}
      >
        <div className="flex items-center space-x-3">
          <Avatar className="h-12 w-12 ring-2 ring-white dark:ring-gray-800">
            <AvatarImage src={employee.avatar} alt={employee.name} />
            <AvatarFallback className={`${deptColors.bg} ${deptColors.text} font-semibold`}>
              {employee.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-sm truncate text-foreground">{employee.name}</h3>
            <p className="text-xs text-muted-foreground truncate">{employee.title}</p>
            <Badge
              variant="outline"
              className={`text-xs mt-1 ${deptColors.text} ${deptColors.border} bg-transparent`}
            >
              {employee.department}
            </Badge>
          </div>
        </div>
      </motion.div>
    )
  }

  const renderOrgLevel = (employeeIds, level = 0) => {
    if (!employeeIds || employeeIds.length === 0) return null

    return (
      <div className="flex flex-col items-center space-y-8">
        <div className="flex flex-wrap justify-center gap-6">
          {employeeIds.map(employeeId => {
            const employee = orgData.employees[employeeId]
            if (!employee) return null

            return (
              <div key={employeeId} className="flex flex-col items-center">
                <EmployeeCard employee={employee} />
                {employee.directReports && employee.directReports.length > 0 && (
                  <>
                    <div className="w-px h-8 bg-gradient-to-b from-border to-muted-foreground/30"></div>
                    <div className="relative">
                      <div className="absolute top-0 left-1/2 w-px h-4 bg-gradient-to-b from-muted-foreground/30 to-border transform -translate-x-1/2"></div>
                      {renderOrgLevel(employee.directReports, level + 1)}
                    </div>
                  </>
                )}
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  return (
    <PullToRefresh onRefresh={handleRefresh} className="flex-1">
      <div className="space-y-6 p-6">
        <Header
          title="Organization Chart"
          subtitle={`Visualizing ${orgStats.totalEmployees} employees across ${orgStats.departments} departments`}
          actions={
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleZoomOut}>
                <ZoomOut className="w-4 h-4" />
              </Button>
              <span className="text-sm text-muted-foreground px-2">{zoomLevel}%</span>
              <Button variant="outline" size="sm" onClick={handleZoomIn}>
                <ZoomIn className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleResetZoom}>
                <RotateCcw className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          }
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-purple-200 dark:border-purple-800 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Total Employees</p>
                  <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">{orgStats.totalEmployees}</p>
                </div>
                <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                  <Users className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-blue-200 dark:border-blue-800 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Departments</p>
                  <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{orgStats.departments}</p>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                  <Building className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 dark:border-green-800 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600 dark:text-green-400">Managers</p>
                  <p className="text-2xl font-bold text-green-900 dark:text-green-100">{orgStats.managers}</p>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                  <Briefcase className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-amber-200 dark:border-amber-800 bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950/20 dark:to-amber-900/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-amber-600 dark:text-amber-400">Avg Team Size</p>
                  <p className="text-2xl font-bold text-amber-900 dark:text-amber-100">{orgStats.avgTeamSize}</p>
                </div>
                <div className="p-3 bg-amber-100 dark:bg-amber-900/30 rounded-full">
                  <Network className="h-8 w-8 text-amber-600 dark:text-amber-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search employees..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Department Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Organization Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Network className="w-5 h-5 mr-2" />
              Organization Structure
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div
              className="overflow-auto p-8 bg-gradient-to-br from-muted/20 via-background to-muted/30 rounded-lg border border-muted"
              style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'top center' }}
            >
              <div className="flex flex-col items-center space-y-8">
                {/* CEO */}
                <EmployeeCard employee={orgData.ceo} isRoot={true} />
                
                {/* Direct Reports */}
                {orgData.ceo.directReports && orgData.ceo.directReports.length > 0 && (
                  <>
                    <div className="w-px h-8 bg-gradient-to-b from-purple-300 to-muted-foreground/30 dark:from-purple-700 dark:to-muted-foreground/30"></div>
                    <div className="relative">
                      <div className="absolute top-0 left-1/2 w-px h-4 bg-gradient-to-b from-muted-foreground/30 to-purple-300 dark:to-purple-700 transform -translate-x-1/2"></div>
                      {renderOrgLevel(orgData.ceo.directReports)}
                    </div>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Department Legend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="w-5 h-5 mr-2" />
              Department Legend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {Object.entries(departmentColors).map(([dept, colors]) => (
                <div key={dept} className="flex items-center space-x-3 p-3 rounded-lg border transition-all hover:shadow-md">
                  <div className={`w-6 h-6 rounded-full border-2 ${colors.bg} ${colors.border} flex items-center justify-center`}>
                    <div className={`w-3 h-3 rounded-full ${colors.bg.replace('50', '200').replace('950/20', '800')}`}></div>
                  </div>
                  <span className={`text-sm font-medium ${colors.text}`}>{dept}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </PullToRefresh>
  )
}
