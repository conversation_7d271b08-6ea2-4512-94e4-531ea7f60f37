(()=>{var e={};e.id=505,e.ids=[505],e.modules={597:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\organization\\\\departments\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\departments\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3652:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>p,tree:()=>c});var r=s(65239),n=s(48088),a=s(88170),l=s.n(a),i=s(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let c={children:["",{children:["dashboard",{children:["organization",{children:["departments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,597)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\departments\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,63144)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\departments\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/organization/departments/page",pathname:"/dashboard/organization/departments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19104:(e,t,s)=>{Promise.resolve().then(s.bind(s,74641))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32256:(e,t,s)=>{Promise.resolve().then(s.bind(s,597))},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74641:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>q});var r=s(60687),n=s(43210),a=s(31158),l=s(96474),i=s(79410),d=s(25541),c=s(41312),o=s(23928),m=s(99270),p=s(80462),x=s(93661),h=s(13861),u=s(63143),j=s(88233),g=s(29523),f=s(89667),v=s(44493),b=s(96834),N=s(6211),y=s(21342),w=s(63503),C=s(96724),D=s(35224),A=s(91956),_=s(27605),P=s(57335),z=s(9275),I=s(34729),S=s(71669),E=s(15079),R=s(11860),B=s(41862),k=s(8819);let $=z.Ik({name:z.Yj().min(2,"Department name must be at least 2 characters").max(100,"Department name must be less than 100 characters"),code:z.Yj().min(2,"Department code must be at least 2 characters").max(20,"Department code must be less than 20 characters").regex(/^[A-Z0-9_-]+$/,"Department code must contain only uppercase letters, numbers, underscores, and hyphens"),description:z.Yj().optional(),headOfDepartment:z.Yj().optional(),parentDepartmentId:z.Yj().optional(),costCenter:z.Yj().optional(),budgetAllocated:z.ai().min(0,"Budget must be a positive number").optional()});function L({department:e,onSuccess:t,onCancel:s,className:a}){let[l,i]=(0,n.useState)(!1),[d,c]=(0,n.useState)([]),[o,m]=(0,n.useState)(!1),p=!!e,x=(0,_.mN)({resolver:(0,P.u)($),defaultValues:{name:e?.name||"",code:e?.code||"",description:e?.description||"",headOfDepartment:e?.headOfDepartment||"",parentDepartmentId:e?.parentDepartmentId||"none",costCenter:e?.costCenter||"",budgetAllocated:e?.budgetAllocated||void 0}}),h=async s=>{await (0,A.$A)(async()=>{let r,n,a={...s,parentDepartmentId:"none"===s.parentDepartmentId||""===s.parentDepartmentId?void 0:s.parentDepartmentId,budgetAllocated:s.budgetAllocated||void 0};p&&e?(r=await A.br.updateDepartment(e.id,a),n="Department updated successfully"):(r=await A.br.createDepartment(a),n="Department created successfully");let l=(0,A.yH)(r,n);l&&(t?.(l),p||x.reset())},i)};return(0,r.jsxs)(v.Zp,{className:a,children:[(0,r.jsx)(v.aR,{children:(0,r.jsx)(v.ZB,{children:p?"Edit Department":"Create New Department"})}),(0,r.jsx)(v.Wu,{children:(0,r.jsx)(S.lV,{...x,children:(0,r.jsxs)("form",{onSubmit:x.handleSubmit(h),className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(S.zB,{control:x.control,name:"name",render:({field:e})=>(0,r.jsxs)(S.eI,{children:[(0,r.jsx)(S.lR,{children:"Department Name *"}),(0,r.jsx)(S.MJ,{children:(0,r.jsx)(f.p,{placeholder:"e.g., Engineering",...e})}),(0,r.jsx)(S.Rr,{children:"The official name of the department"}),(0,r.jsx)(S.C5,{})]})}),(0,r.jsx)(S.zB,{control:x.control,name:"code",render:({field:e})=>(0,r.jsxs)(S.eI,{children:[(0,r.jsx)(S.lR,{children:"Department Code *"}),(0,r.jsx)(S.MJ,{children:(0,r.jsx)(f.p,{placeholder:"e.g., ENG",...e,onChange:t=>e.onChange(t.target.value.toUpperCase())})}),(0,r.jsx)(S.Rr,{children:"Unique identifier (uppercase letters, numbers, _, -)"}),(0,r.jsx)(S.C5,{})]})})]}),(0,r.jsx)(S.zB,{control:x.control,name:"description",render:({field:e})=>(0,r.jsxs)(S.eI,{children:[(0,r.jsx)(S.lR,{children:"Description"}),(0,r.jsx)(S.MJ,{children:(0,r.jsx)(I.T,{placeholder:"Brief description of the department's purpose and responsibilities",className:"min-h-[100px]",...e})}),(0,r.jsx)(S.C5,{})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(S.zB,{control:x.control,name:"headOfDepartment",render:({field:e})=>(0,r.jsxs)(S.eI,{children:[(0,r.jsx)(S.lR,{children:"Head of Department"}),(0,r.jsx)(S.MJ,{children:(0,r.jsx)(f.p,{placeholder:"e.g., John Smith",...e})}),(0,r.jsx)(S.Rr,{children:"Name of the department head/manager"}),(0,r.jsx)(S.C5,{})]})}),(0,r.jsx)(S.zB,{control:x.control,name:"parentDepartmentId",render:({field:e})=>(0,r.jsxs)(S.eI,{children:[(0,r.jsx)(S.lR,{children:"Parent Department"}),(0,r.jsxs)(E.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,r.jsx)(S.MJ,{children:(0,r.jsx)(E.bq,{children:(0,r.jsx)(E.yv,{placeholder:"Select parent department (optional)"})})}),(0,r.jsxs)(E.gC,{children:[(0,r.jsx)(E.eb,{value:"none",children:"No parent department"}),o?(0,r.jsx)(E.eb,{value:"loading",disabled:!0,children:"Loading departments..."}):d.map(e=>(0,r.jsxs)(E.eb,{value:e.id,children:[e.name," (",e.code,")"]},e.id))]})]}),(0,r.jsx)(S.Rr,{children:"Select if this is a sub-department"}),(0,r.jsx)(S.C5,{})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(S.zB,{control:x.control,name:"costCenter",render:({field:e})=>(0,r.jsxs)(S.eI,{children:[(0,r.jsx)(S.lR,{children:"Cost Center"}),(0,r.jsx)(S.MJ,{children:(0,r.jsx)(f.p,{placeholder:"e.g., CC-ENG-001",...e})}),(0,r.jsx)(S.Rr,{children:"Financial cost center code"}),(0,r.jsx)(S.C5,{})]})}),(0,r.jsx)(S.zB,{control:x.control,name:"budgetAllocated",render:({field:e})=>(0,r.jsxs)(S.eI,{children:[(0,r.jsx)(S.lR,{children:"Budget Allocated"}),(0,r.jsx)(S.MJ,{children:(0,r.jsx)(f.p,{type:"number",placeholder:"e.g., 1000000",...e,onChange:t=>e.onChange(t.target.value?parseFloat(t.target.value):void 0)})}),(0,r.jsx)(S.Rr,{children:"Annual budget in USD"}),(0,r.jsx)(S.C5,{})]})})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 pt-6",children:[(0,r.jsxs)(g.$,{type:"button",variant:"outline",onClick:()=>{x.reset(),s?.()},disabled:l,children:[(0,r.jsx)(R.A,{className:"w-4 h-4 mr-2"}),"Cancel"]}),(0,r.jsxs)(g.$,{type:"submit",disabled:l,children:[l?(0,r.jsx)(B.A,{className:"w-4 h-4 mr-2 animate-spin"}):(0,r.jsx)(k.A,{className:"w-4 h-4 mr-2"}),p?"Update Department":"Create Department"]})]})]})})})]})}var G=s(78010),M=s(28488);function q(){let e=(0,G.Sk)(),[t,s]=(0,n.useState)({departments:[],analytics:null,loading:!0,error:null}),[_,P]=(0,n.useState)(""),[z,I]=(0,n.useState)(!1),[S,E]=(0,n.useState)(null),[R,B]=(0,n.useState)(null),[k,$]=(0,n.useState)(!1),q=async()=>{await (0,A.$A)(async()=>{let e=await A.br.getDepartments({includeInactive:!0,sortBy:"name",sortOrder:"asc"}),t=(0,A.yH)(e,void 0,!0);t?s(e=>({...e,departments:t.departments,loading:!1,error:null})):s(e=>({...e,loading:!1,error:"Failed to load departments"}))},e=>s(t=>({...t,loading:e})))},O=async()=>{let e=await A.br.getDepartmentAnalytics(),t=(0,A.yH)(e,void 0,!1);t&&s(e=>({...e,analytics:t}))},Z=t.departments.filter(e=>e.name.toLowerCase().includes(_.toLowerCase())||e.code.toLowerCase().includes(_.toLowerCase())||e.description&&e.description.toLowerCase().includes(_.toLowerCase())),W=async()=>{await q(),await O()},F=e=>{E(e),I(!0)},H=e=>{B(e),$(!0)},J=async e=>{confirm("Are you sure you want to delete this department?")&&await (0,A.$A)(async()=>{let t=await A.br.deleteDepartment(e);null!==(0,A.yH)(t,"Department deleted successfully")&&(await q(),await O())},e=>s(t=>({...t,loading:e})))},U=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e);return(0,r.jsx)(M.$G,{permissions:["department_read","hr","hr_admin","super_admin"],children:(0,r.jsxs)(D.k,{onRefresh:W,className:"flex-1",children:[(0,r.jsxs)("div",{className:"space-y-6 p-6",children:[(0,r.jsx)(C.Y,{title:"Department Management",subtitle:`Managing ${t.analytics?.totalDepartments||0} departments with ${t.analytics?.totalEmployees||0} employees`,actions:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(G.LQ,{permissions:["department_read","hr","hr_admin","super_admin"],children:(0,r.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"Export"]})}),(0,r.jsx)(G.LQ,{permissions:["department_write","hr_admin","super_admin"],children:(0,r.jsxs)(g.$,{size:"sm",onClick:()=>{E(null),I(!0)},children:[(0,r.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Add Department"]})})]})}),t.loading&&(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Loading departments..."})]})}),t.error&&(0,r.jsxs)("div",{className:"bg-destructive/10 border border-destructive/20 rounded-lg p-4",children:[(0,r.jsx)("p",{className:"text-destructive",children:t.error}),(0,r.jsx)(g.$,{variant:"outline",size:"sm",onClick:W,className:"mt-2",children:"Try Again"})]}),!t.loading&&!t.error&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(v.Zp,{children:(0,r.jsx)(v.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Departments"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:t.analytics?.totalDepartments||0})]}),(0,r.jsx)(i.A,{className:"h-8 w-8 text-primary"})]})})}),(0,r.jsx)(v.Zp,{children:(0,r.jsx)(v.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Active Departments"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:t.analytics?.activeDepartments||0})]}),(0,r.jsx)(d.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,r.jsx)(v.Zp,{children:(0,r.jsx)(v.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Employees"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:t.analytics?.totalEmployees||0})]}),(0,r.jsx)(c.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,r.jsx)(v.Zp,{children:(0,r.jsx)(v.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Budget"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:t.analytics?.budgetUtilization?.totalBudget?U(t.analytics.budgetUtilization.totalBudget):"$0"})]}),(0,r.jsx)(o.A,{className:"h-8 w-8 text-yellow-600"})]})})})]}),(0,r.jsx)(v.Zp,{children:(0,r.jsx)(v.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,r.jsx)(f.p,{placeholder:"Search departments...",value:_,onChange:e=>P(e.target.value),className:"pl-10"})]}),(0,r.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Filters"]})]})})}),(0,r.jsxs)(v.Zp,{children:[(0,r.jsx)(v.aR,{children:(0,r.jsx)(v.ZB,{children:"Departments"})}),(0,r.jsx)(v.Wu,{children:(0,r.jsxs)(N.XI,{children:[(0,r.jsx)(N.A0,{children:(0,r.jsxs)(N.Hj,{children:[(0,r.jsx)(N.nd,{children:"Department"}),(0,r.jsx)(N.nd,{children:"Code"}),(0,r.jsx)(N.nd,{children:"Head"}),(0,r.jsx)(N.nd,{children:"Employees"}),(0,r.jsx)(N.nd,{children:"Budget"}),(0,r.jsx)(N.nd,{children:"Status"}),(0,r.jsx)(N.nd,{className:"text-right",children:"Actions"})]})}),(0,r.jsx)(N.BF,{children:Z.map(t=>(0,r.jsxs)(N.Hj,{children:[(0,r.jsx)(N.nA,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:t.name}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:t.description}),t.parentDepartment&&(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Parent: ",t.parentDepartment]})]})}),(0,r.jsx)(N.nA,{children:(0,r.jsx)(b.E,{variant:"outline",children:t.code})}),(0,r.jsx)(N.nA,{children:t.headOfDepartment||"Not assigned"}),(0,r.jsx)(N.nA,{children:t.employeeCount||0}),(0,r.jsx)(N.nA,{children:t.budgetAllocated?U(t.budgetAllocated):"Not set"}),(0,r.jsx)(N.nA,{children:(0,r.jsx)(b.E,{variant:t.isActive?"default":"secondary",children:t.isActive?"Active":"Inactive"})}),(0,r.jsx)(N.nA,{className:"text-right",children:(0,r.jsxs)(y.rI,{children:[(0,r.jsx)(y.ty,{asChild:!0,children:(0,r.jsx)(g.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(y.SQ,{align:"end",children:[(0,r.jsxs)(y._2,{onClick:()=>H(t),children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"View Details"]}),e.canWriteDepartments()&&(0,r.jsxs)(y._2,{onClick:()=>F(t),children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Edit"]}),e.canWriteDepartments()&&(0,r.jsxs)(y._2,{onClick:()=>J(t.id),className:"text-destructive",children:[(0,r.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},t.id))})]})})]})]}),(0,r.jsx)(w.lG,{open:z,onOpenChange:I,children:(0,r.jsxs)(w.Cf,{className:"max-w-2xl",children:[(0,r.jsx)(w.c7,{children:(0,r.jsx)(w.L3,{children:S?"Edit Department":"Add New Department"})}),(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Department form will be implemented here."})})]})}),(0,r.jsx)(w.lG,{open:z,onOpenChange:I,children:(0,r.jsxs)(w.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)(w.c7,{children:(0,r.jsx)(w.L3,{children:S?"Edit Department":"Create New Department"})}),(0,r.jsx)(L,{department:S,onSuccess:e=>{I(!1),E(null),q(),O()},onCancel:()=>{I(!1),E(null)},className:"border-0 shadow-none"})]})}),(0,r.jsx)(w.lG,{open:k,onOpenChange:$,children:(0,r.jsxs)(w.Cf,{className:"max-w-4xl",children:[(0,r.jsx)(w.c7,{children:(0,r.jsx)(w.L3,{children:"Department Details"})}),(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Department details view will be implemented here."})})]})})]})})}},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,912,18,722,632,913,780,809,456,725],()=>s(3652));module.exports=r})();