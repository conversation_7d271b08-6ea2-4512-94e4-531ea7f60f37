import { DatabaseService } from '../services/databaseService';

async function checkUserSessionsSchema() {
  const db = new DatabaseService();
  
  try {
    console.log('Checking user_sessions table schema...');
    
    // Get table structure
    const schemaResult = await db.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'user_sessions' 
      ORDER BY ordinal_position
    `);
    
    console.log('user_sessions table columns:');
    schemaResult.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    
    // Check if there are any existing sessions
    const sessionCount = await db.query('SELECT COUNT(*) as count FROM user_sessions');
    console.log(`\nExisting sessions: ${sessionCount.rows[0].count}`);
    
    // Check if there are any active sessions
    const activeSessionCount = await db.query(`
      SELECT COUNT(*) as count FROM user_sessions 
      WHERE is_active = true AND expires_at > NOW()
    `);
    console.log(`Active sessions: ${activeSessionCount.rows[0].count}`);
    
  } catch (error) {
    console.error('Error checking schema:', error.message);
  }
}

checkUserSessionsSchema();
