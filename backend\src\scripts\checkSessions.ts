import { DatabaseService } from '../services/databaseService';

async function checkSessions() {
  const db = new DatabaseService();
  
  try {
    console.log('Checking user sessions...');
    
    const result = await db.query('SELECT id, session_id, user_id, created_at FROM user_sessions ORDER BY created_at DESC LIMIT 10');
    console.log(`Found ${result.rowCount} sessions:`);
    
    result.rows.forEach((row, index) => {
      console.log(`${index + 1}. ID: ${row.id}, Session ID: ${row.session_id}, User: ${row.user_id}, Created: ${row.created_at}`);
    });
    
    // Check for duplicates
    const duplicateCheck = await db.query(`
      SELECT session_id, COUNT(*) as count 
      FROM user_sessions 
      GROUP BY session_id 
      HAVING COUNT(*) > 1
    `);
    
    if (duplicateCheck.rowCount > 0) {
      console.log('\n❌ Found duplicate session IDs:');
      duplicateCheck.rows.forEach(row => {
        console.log(`Session ID: ${row.session_id}, Count: ${row.count}`);
      });
    } else {
      console.log('\n✅ No duplicate session IDs found');
    }
    
  } catch (error) {
    console.error('Error checking sessions:', error.message);
  }
}

checkSessions();
