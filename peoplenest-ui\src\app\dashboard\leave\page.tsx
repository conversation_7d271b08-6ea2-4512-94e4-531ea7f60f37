"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Plane,
  Heart,
  Briefcase,
  GraduationCap,
  Users,
  TrendingUp,
  Download,
  Filter,
  Search,
  Plus,
  Eye,
  Edit,
  MessageSquare
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/layout/header"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LineChart, Line, BarChart, Bar, PieChart, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'

// Mock leave data
const leaveRequests = [
  {
    id: 1,
    employeeId: 1,
    employeeName: "<PERSON>",
    department: "Engineering",
    leaveType: "vacation",
    startDate: "2024-02-15",
    endDate: "2024-02-19",
    days: 5,
    status: "approved",
    reason: "Family vacation",
    appliedDate: "2024-01-20",
    approver: "<PERSON>",
    avatar: "/avatars/sarah.jpg"
  },
  {
    id: 2,
    employeeId: 2,
    employeeName: "David Wilson",
    department: "Product",
    leaveType: "sick",
    startDate: "2024-01-28",
    endDate: "2024-01-30",
    days: 3,
    status: "approved",
    reason: "Medical appointment",
    appliedDate: "2024-01-25",
    approver: "Emily Davis",
    avatar: "/avatars/david.jpg"
  },
  {
    id: 3,
    employeeId: 3,
    employeeName: "Lisa Park",
    department: "Design",
    leaveType: "personal",
    startDate: "2024-02-05",
    endDate: "2024-02-05",
    days: 1,
    status: "pending",
    reason: "Personal matters",
    appliedDate: "2024-01-30",
    approver: "Tom Anderson",
    avatar: "/avatars/lisa.jpg"
  },
  {
    id: 4,
    employeeId: 4,
    employeeName: "James Liu",
    department: "Engineering",
    leaveType: "vacation",
    startDate: "2024-03-01",
    endDate: "2024-03-15",
    days: 11,
    status: "rejected",
    reason: "Extended vacation",
    appliedDate: "2024-01-15",
    approver: "Mike Chen",
    avatar: "/avatars/james.jpg"
  }
]

const leaveTypes = [
  { name: "Vacation", value: 45, color: "#0088FE", icon: Plane },
  { name: "Sick Leave", value: 28, color: "#00C49F", icon: Heart },
  { name: "Personal", value: 15, color: "#FFBB28", icon: Briefcase },
  { name: "Training", value: 8, color: "#FF8042", icon: GraduationCap },
  { name: "Other", value: 4, color: "#8884D8", icon: Calendar }
]

const monthlyLeaveData = [
  { month: 'Jan', vacation: 12, sick: 8, personal: 4, training: 2 },
  { month: 'Feb', vacation: 15, sick: 6, personal: 5, training: 3 },
  { month: 'Mar', vacation: 18, sick: 7, personal: 3, training: 1 },
  { month: 'Apr', vacation: 22, sick: 5, personal: 6, training: 4 },
  { month: 'May', vacation: 25, sick: 9, personal: 4, training: 2 },
  { month: 'Jun', vacation: 28, sick: 4, personal: 7, training: 3 }
]

const leaveStats = {
  totalRequests: leaveRequests.length,
  pendingRequests: leaveRequests.filter(r => r.status === "pending").length,
  approvedRequests: leaveRequests.filter(r => r.status === "approved").length,
  rejectedRequests: leaveRequests.filter(r => r.status === "rejected").length,
  avgProcessingTime: 2.5,
  totalDaysRequested: leaveRequests.reduce((sum, r) => sum + r.days, 0)
}

export default function LeavePage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("All")
  const [selectedType, setSelectedType] = useState("All")
  const [selectedDepartment, setSelectedDepartment] = useState("All")
  const [viewMode, setViewMode] = useState<"requests" | "calendar" | "analytics">("requests")

  const filteredRequests = leaveRequests.filter(request => {
    const matchesSearch = request.employeeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         request.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         request.reason.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = selectedStatus === "All" || request.status === selectedStatus
    const matchesType = selectedType === "All" || request.leaveType === selectedType
    const matchesDepartment = selectedDepartment === "All" || request.department === selectedDepartment
    
    return matchesSearch && matchesStatus && matchesType && matchesDepartment
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved": return "success"
      case "pending": return "warning"
      case "rejected": return "destructive"
      default: return "secondary"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved": return <CheckCircle className="h-4 w-4" />
      case "pending": return <Clock className="h-4 w-4" />
      case "rejected": return <XCircle className="h-4 w-4" />
      default: return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getLeaveTypeIcon = (type: string) => {
    switch (type) {
      case "vacation": return <Plane className="h-4 w-4" />
      case "sick": return <Heart className="h-4 w-4" />
      case "personal": return <Briefcase className="h-4 w-4" />
      case "training": return <GraduationCap className="h-4 w-4" />
      default: return <Calendar className="h-4 w-4" />
    }
  }

  const getLeaveTypeColor = (type: string) => {
    switch (type) {
      case "vacation": return "text-blue-600 dark:text-blue-400"
      case "sick": return "text-red-600 dark:text-red-400"
      case "personal": return "text-green-600 dark:text-green-400"
      case "training": return "text-purple-600 dark:text-purple-400"
      default: return "text-muted-foreground"
    }
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <Header
        title="Leave Management"
        subtitle={`Managing ${leaveStats.totalRequests} leave requests with ${leaveStats.pendingRequests} pending approval`}
        actions={
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 mr-2" />
              New Request
            </Button>
          </div>
        }
      />

      {/* Leave Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{leaveStats.totalRequests}</p>
                <p className="text-sm text-muted-foreground">Total Requests</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{leaveStats.pendingRequests}</p>
                <p className="text-sm text-muted-foreground">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{leaveStats.approvedRequests}</p>
                <p className="text-sm text-muted-foreground">Approved</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <XCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{leaveStats.rejectedRequests}</p>
                <p className="text-sm text-muted-foreground">Rejected</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{leaveStats.avgProcessingTime}</p>
                <p className="text-sm text-muted-foreground">Avg Days</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-8 w-8 text-teal-600 dark:text-teal-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{leaveStats.totalDaysRequested}</p>
                <p className="text-sm text-muted-foreground">Total Days</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* View Mode Tabs */}
      <div className="flex items-center space-x-2">
        <Button
          variant={viewMode === "requests" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("requests")}
        >
          Requests
        </Button>
        <Button
          variant={viewMode === "calendar" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("calendar")}
        >
          Calendar
        </Button>
        <Button
          variant={viewMode === "analytics" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("analytics")}
        >
          Analytics
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by employee name, department, or reason..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="All">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="All">All Types</option>
                <option value="vacation">Vacation</option>
                <option value="sick">Sick Leave</option>
                <option value="personal">Personal</option>
                <option value="training">Training</option>
              </select>
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="All">All Departments</option>
                <option value="Engineering">Engineering</option>
                <option value="Product">Product</option>
                <option value="Design">Design</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {viewMode === "requests" ? (
        /* Leave Requests Table */
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-muted/50 border-b border-border">
                  <tr>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Employee</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Leave Type</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Dates</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Days</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Status</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Reason</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Approver</th>
                    <th className="text-left py-3 px-6 font-medium text-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRequests.map((request, index) => (
                    <motion.tr
                      key={request.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: index * 0.05 }}
                      className="border-b border-border hover:bg-muted/50"
                    >
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={request.avatar} alt={request.employeeName} />
                            <AvatarFallback>
                              {request.employeeName.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-foreground">{request.employeeName}</p>
                            <p className="text-sm text-muted-foreground">{request.department}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-2">
                          <span className={getLeaveTypeColor(request.leaveType)}>
                            {getLeaveTypeIcon(request.leaveType)}
                          </span>
                          <span className="capitalize">{request.leaveType}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div>
                          <p className="font-medium">{new Date(request.startDate).toLocaleDateString()}</p>
                          <p className="text-sm text-muted-foreground">to {new Date(request.endDate).toLocaleDateString()}</p>
                        </div>
                      </td>
                      <td className="py-4 px-6 font-medium">{request.days}</td>
                      <td className="py-4 px-6">
                        <Badge variant={getStatusColor(request.status) as any} className="flex items-center space-x-1 w-fit">
                          {getStatusIcon(request.status)}
                          <span className="ml-1">{request.status.charAt(0).toUpperCase() + request.status.slice(1)}</span>
                        </Badge>
                      </td>
                      <td className="py-4 px-6">
                        <p className="text-sm max-w-xs truncate">{request.reason}</p>
                      </td>
                      <td className="py-4 px-6 text-muted-foreground">{request.approver}</td>
                      <td className="py-4 px-6">
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MessageSquare className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      ) : viewMode === "calendar" ? (
        /* Calendar View */
        <Card>
          <CardHeader>
            <CardTitle>Leave Calendar</CardTitle>
            <CardDescription>Visual overview of upcoming leave requests</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {leaveRequests.filter(r => r.status === "approved").map((request, index) => (
                  <motion.div
                    key={request.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20"
                  >
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={request.avatar} alt={request.employeeName} />
                        <AvatarFallback>
                          {request.employeeName.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <p className="font-medium text-sm">{request.employeeName}</p>
                        <p className="text-xs text-muted-foreground">{request.department}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{request.days} days</p>
                        <p className="text-xs text-muted-foreground capitalize">{request.leaveType}</p>
                      </div>
                    </div>
                    <div className="mt-2 text-sm">
                      <p>{new Date(request.startDate).toLocaleDateString()} - {new Date(request.endDate).toLocaleDateString()}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Analytics */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Monthly Leave Trends</span>
              </CardTitle>
              <CardDescription>Leave requests by type over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={monthlyLeaveData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="vacation" stackId="a" fill="#0088FE" name="Vacation" />
                  <Bar dataKey="sick" stackId="a" fill="#00C49F" name="Sick Leave" />
                  <Bar dataKey="personal" stackId="a" fill="#FFBB28" name="Personal" />
                  <Bar dataKey="training" stackId="a" fill="#FF8042" name="Training" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>Leave Type Distribution</span>
              </CardTitle>
              <CardDescription>Breakdown of leave types</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={leaveTypes}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, value }) => `${name} ${value}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {leaveTypes.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
