"use strict";exports.id=725,exports.ids=[725],exports.modules={6211:(e,t,r)=>{r.d(t,{A0:()=>l,BF:()=>i,Hj:()=>d,XI:()=>o,nA:()=>m,nd:()=>c});var a=r(60687),s=r(43210),n=r(4780);let o=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:r,className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})}));o.displayName="Table";let l=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("thead",{ref:r,className:(0,n.cn)("[&_tr]:border-b",e),...t}));l.displayName="TableHeader";let i=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tbody",{ref:r,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t}));i.displayName="TableBody",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tfoot",{ref:r,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let d=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("tr",{ref:r,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));d.displayName="TableRow";let c=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("th",{ref:r,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));c.displayName="TableHead";let m=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("td",{ref:r,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));m.displayName="TableCell",s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("caption",{ref:r,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},21342:(e,t,r)=>{r.d(t,{SQ:()=>d,_2:()=>c,rI:()=>l,ty:()=>i});var a=r(60687),s=r(43210),n=r(4780);let o=s.createContext({open:!1,setOpen:()=>{}});function l({children:e}){let[t,r]=s.useState(!1);return(0,a.jsx)(o.Provider,{value:{open:t,setOpen:r},children:(0,a.jsx)("div",{className:"relative inline-block text-left",children:e})})}function i({asChild:e=!1,children:t,className:r}){let{open:l,setOpen:i}=s.useContext(o),d=()=>{i(!l)};return e&&s.isValidElement(t)?s.cloneElement(t,{onClick:d,"aria-expanded":l,"aria-haspopup":!0,className:(0,n.cn)(t.props.className,r)}):(0,a.jsx)("button",{onClick:d,"aria-expanded":l,"aria-haspopup":!0,className:(0,n.cn)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors","focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring","disabled:pointer-events-none disabled:opacity-50",r),children:t})}function d({align:e="center",side:t="bottom",children:r,className:l}){let{open:i,setOpen:d}=s.useContext(o),c=s.useRef(null);return(s.useEffect(()=>{let e=e=>{c.current&&!c.current.contains(e.target)&&d(!1)},t=e=>{"Escape"===e.key&&d(!1)};return i&&(document.addEventListener("mousedown",e),document.addEventListener("keydown",t)),()=>{document.removeEventListener("mousedown",e),document.removeEventListener("keydown",t)}},[i,d]),i)?(0,a.jsx)("div",{ref:c,className:(0,n.cn)("absolute z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","animate-in fade-in-0 zoom-in-95",{start:"left-0",center:"left-1/2 transform -translate-x-1/2",end:"right-0"}[e],{top:"bottom-full mb-2",right:"left-full ml-2 top-0",bottom:"top-full mt-2",left:"right-full mr-2 top-0"}[t],l),children:r}):null}function c({children:e,onClick:t,className:r,disabled:l=!1}){let{setOpen:i}=s.useContext(o);return(0,a.jsx)("div",{onClick:()=>{!l&&t&&(t(),i(!1))},className:(0,n.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none","transition-colors focus:bg-accent focus:text-accent-foreground",l?"pointer-events-none opacity-50":"hover:bg-accent hover:text-accent-foreground cursor-pointer",r),children:e})}},28488:(e,t,r)=>{r.d(t,{$G:()=>l});var a=r(60687);r(43210);var s=r(16189),n=r(40565),o=r(63523);function l({children:e,permission:t,permissions:r,requireAll:l=!1,role:i,roles:d,minRole:c,redirectTo:m="/auth/login",fallback:u=null,condition:f}){let{user:p,isAuthenticated:x,isLoading:h}=(0,n.A)();return((0,s.useRouter)(),h)?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Loading..."})]})}):x&&p&&(!f||f(p))&&(!t||(0,o._m)(p,t))&&(!r||!(r.length>0)||(l?r.every(e=>(0,o._m)(p,e)):(0,o.sx)(p,r)))?i&&!(0,o.hf)(p,i)||d&&d.length>0&&!(0,o.pX)(p,d)||c&&!(0,o.BU)(p,c)?(0,a.jsx)(a.Fragment,{children:u}):(0,a.jsx)(a.Fragment,{children:e}):(0,a.jsx)(a.Fragment,{children:u})}},34729:(e,t,r)=>{r.d(t,{T:()=>o});var a=r(60687),s=r(43210),n=r(4780);let o=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));o.displayName="Textarea"},35224:(e,t,r)=>{r.d(t,{k:()=>d});var a=r(60687),s=r(43210),n=r(17135),o=r(53342),l=r(50371),i=r(78122);function d({onRefresh:e,children:t,className:r="",threshold:d=80,disabled:c=!1}){let[m,u]=(0,s.useState)(!1),[f,p]=(0,s.useState)(!1),x=(0,s.useRef)(null),h=(0,n.d)(0),y=(0,o.G)(h,[0,d],[0,1]),b=(0,o.G)(h,[0,d],[0,180]),g=(0,o.G)(h,[0,d],[.8,1]),v=async(t,r)=>{if(!c&&!m&&f){if(p(!1),r.offset.y>=d){u(!0);try{await e()}catch(e){console.error("Refresh failed:",e)}finally{u(!1)}}h.set(0)}};return(0,a.jsxs)("div",{className:`relative overflow-hidden ${r}`,children:[(0,a.jsx)(l.P.div,{className:"absolute top-0 left-0 right-0 z-10 flex items-center justify-center",style:{opacity:y,y:(0,o.G)(h,e=>e-60)},children:(0,a.jsx)(l.P.div,{className:"flex items-center justify-center w-12 h-12 bg-primary rounded-full shadow-lg",style:{scale:g,rotate:m?360:b},animate:m?{rotate:360}:{},transition:m?{duration:1,repeat:1/0,ease:"linear"}:{},children:(0,a.jsx)(i.A,{className:"w-6 h-6 text-primary-foreground"})})}),(0,a.jsx)(l.P.div,{ref:x,className:"h-full overflow-auto",style:{y:h},onPanStart:()=>{if(c||m)return;let e=x.current;e&&0===e.scrollTop&&p(!0)},onPan:(e,t)=>{!c&&!m&&f&&t.delta.y>0&&h.set(Math.min(t.offset.y,1.5*d))},onPanEnd:v,drag:"y",dragConstraints:{top:0,bottom:0},dragElastic:{top:.3,bottom:0},children:t})]})}},66420:(e,t,r)=>{r.d(t,{i3:()=>a});let a={BASE_URL:"http://localhost:3002",API_BASE_URL:"http://localhost:3002/api",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3};parseInt("3600"),parseInt("604800"),parseInt("1800000"),parseInt("300000"),parseInt("10485760"),parseInt("10"),parseInt("100"),parseInt("300000"),parseInt("100"),parseInt("900000")},71669:(e,t,r)=>{r.d(t,{C5:()=>b,MJ:()=>h,Rr:()=>y,eI:()=>p,lR:()=>x,lV:()=>d,zB:()=>m});var a=r(60687),s=r(43210),n=r(8730),o=r(27605),l=r(4780),i=r(80013);let d=o.Op,c=s.createContext({}),m=({...e})=>(0,a.jsx)(c.Provider,{value:{name:e.name},children:(0,a.jsx)(o.xI,{...e})}),u=()=>{let e=s.useContext(c),t=s.useContext(f),{getFieldState:r,formState:a}=(0,o.xW)(),n=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...n}},f=s.createContext({}),p=s.forwardRef(({className:e,...t},r)=>{let n=s.useId();return(0,a.jsx)(f.Provider,{value:{id:n},children:(0,a.jsx)("div",{ref:r,className:(0,l.cn)("space-y-2",e),...t})})});p.displayName="FormItem";let x=s.forwardRef(({className:e,...t},r)=>{let{error:s,formItemId:n}=u();return(0,a.jsx)(i.J,{ref:r,className:(0,l.cn)(s&&"text-destructive",e),htmlFor:n,...t})});x.displayName="FormLabel";let h=s.forwardRef(({...e},t)=>{let{error:r,formItemId:s,formDescriptionId:o,formMessageId:l}=u();return(0,a.jsx)(n.DX,{ref:t,id:s,"aria-describedby":r?`${o} ${l}`:`${o}`,"aria-invalid":!!r,...e})});h.displayName="FormControl";let y=s.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:s}=u();return(0,a.jsx)("p",{ref:r,id:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})});y.displayName="FormDescription";let b=s.forwardRef(({className:e,children:t,...r},s)=>{let{error:n,formMessageId:o}=u(),i=n?String(n?.message):t;return i?(0,a.jsx)("p",{ref:s,id:o,className:(0,l.cn)("text-sm font-medium text-destructive",e),...r,children:i}):null});b.displayName="FormMessage"},80013:(e,t,r)=>{r.d(t,{J:()=>d});var a=r(60687),s=r(43210),n=r(78148),o=r(24224),l=r(4780);let i=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(n.b,{ref:r,className:(0,l.cn)(i(),e),...t}));d.displayName=n.b.displayName},91956:(e,t,r)=>{r.d(t,{$A:()=>d,br:()=>l,yH:()=>i});var a=r(52581),s=r(63523);let n=r(66420).i3.API_BASE_URL;class o{async makeRequest(e,t={}){try{let r=(0,s.Pt)(),a=await fetch(`${n}${e}`,{headers:{"Content-Type":"application/json",...r&&{Authorization:`Bearer ${r}`},...t.headers},...t});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||`HTTP error! status: ${a.status}`)}return{data:await a.json()}}catch(e){return console.error("API request failed:",e),{error:e instanceof Error?e.message:"An unexpected error occurred"}}}async getDepartments(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&""!==r&&t.append(e,r.toString())});let r=t.toString(),a=`/departments${r?`?${r}`:""}`;return this.makeRequest(a)}async getDepartmentById(e){return this.makeRequest(`/departments/${e}`)}async createDepartment(e){return this.makeRequest("/departments",{method:"POST",body:JSON.stringify(e)})}async updateDepartment(e,t){return this.makeRequest(`/departments/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteDepartment(e){return this.makeRequest(`/departments/${e}`,{method:"DELETE"})}async getDepartmentEmployees(e){return this.makeRequest(`/departments/${e}/employees`)}async getDepartmentAnalytics(){return this.makeRequest("/departments/analytics")}async searchDepartments(e){return this.getDepartments({search:e,limit:50}).then(e=>({...e,data:e.data?.departments||[]}))}async getActiveDepartments(){return this.getDepartments({includeInactive:!1}).then(e=>({...e,data:e.data?.departments||[]}))}async getDepartmentHierarchy(){return this.getDepartments({sortBy:"name",sortOrder:"asc"}).then(e=>({...e,data:e.data?.departments||[]}))}}let l=new o,i=(e,t,r=!0)=>e.error?(r&&a.oR.error(e.error),null):(t&&e.data&&a.oR.success(t),e.data||null),d=async(e,t)=>{try{return t(!0),await e()}catch(e){return console.error("Operation failed:",e),a.oR.error(e instanceof Error?e.message:"Operation failed"),null}finally{t(!1)}}}};