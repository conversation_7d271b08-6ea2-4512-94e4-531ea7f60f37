(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7505],{1048:(e,s,t)=>{Promise.resolve().then(t.bind(t,86899))},86899:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>M});var n=t(95155),a=t(12115),r=t(91788),l=t(84616),i=t(48136),d=t(33109),c=t(17580),o=t(55868),m=t(47924),x=t(66932),h=t(5623),p=t(92657),j=t(13717),u=t(62525),g=t(30285),v=t(62523),N=t(66695),f=t(26126),b=t(85127),D=t(44838),y=t(54165),w=t(25409),C=t(86292),A=t(22886),S=t(62177),I=t(48778),_=t(71153),E=t(88539),B=t(17759),R=t(59409),$=t(54416),k=t(51154),z=t(4229);let L=_.Ik({name:_.Yj().min(2,"Department name must be at least 2 characters").max(100,"Department name must be less than 100 characters"),code:_.Yj().min(2,"Department code must be at least 2 characters").max(20,"Department code must be less than 20 characters").regex(/^[A-Z0-9_-]+$/,"Department code must contain only uppercase letters, numbers, underscores, and hyphens"),description:_.Yj().optional(),headOfDepartment:_.Yj().optional(),parentDepartmentId:_.Yj().optional(),costCenter:_.Yj().optional(),budgetAllocated:_.ai().min(0,"Budget must be a positive number").optional()});function O(e){let{department:s,onSuccess:t,onCancel:r,className:l}=e,[i,d]=(0,a.useState)(!1),[c,o]=(0,a.useState)([]),[m,x]=(0,a.useState)(!1),h=!!s,p=(0,S.mN)({resolver:(0,I.u)(L),defaultValues:{name:(null==s?void 0:s.name)||"",code:(null==s?void 0:s.code)||"",description:(null==s?void 0:s.description)||"",headOfDepartment:(null==s?void 0:s.headOfDepartment)||"",parentDepartmentId:(null==s?void 0:s.parentDepartmentId)||"none",costCenter:(null==s?void 0:s.costCenter)||"",budgetAllocated:(null==s?void 0:s.budgetAllocated)||void 0}});(0,a.useEffect)(()=>{j()},[]);let j=async()=>{await (0,A.$A)(async()=>{let e=await A.br.getActiveDepartments(),t=(0,A.yH)(e,void 0,!1);t&&o(h?t.filter(e=>e.id!==(null==s?void 0:s.id)):t)},x)},u=async e=>{await (0,A.$A)(async()=>{let n,a,r={...e,parentDepartmentId:"none"===e.parentDepartmentId||""===e.parentDepartmentId?void 0:e.parentDepartmentId,budgetAllocated:e.budgetAllocated||void 0};h&&s?(n=await A.br.updateDepartment(s.id,r),a="Department updated successfully"):(n=await A.br.createDepartment(r),a="Department created successfully");let l=(0,A.yH)(n,a);l&&(null==t||t(l),h||p.reset())},d)};return(0,n.jsxs)(N.Zp,{className:l,children:[(0,n.jsx)(N.aR,{children:(0,n.jsx)(N.ZB,{children:h?"Edit Department":"Create New Department"})}),(0,n.jsx)(N.Wu,{children:(0,n.jsx)(B.lV,{...p,children:(0,n.jsxs)("form",{onSubmit:p.handleSubmit(u),className:"space-y-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsx)(B.zB,{control:p.control,name:"name",render:e=>{let{field:s}=e;return(0,n.jsxs)(B.eI,{children:[(0,n.jsx)(B.lR,{children:"Department Name *"}),(0,n.jsx)(B.MJ,{children:(0,n.jsx)(v.p,{placeholder:"e.g., Engineering",...s})}),(0,n.jsx)(B.Rr,{children:"The official name of the department"}),(0,n.jsx)(B.C5,{})]})}}),(0,n.jsx)(B.zB,{control:p.control,name:"code",render:e=>{let{field:s}=e;return(0,n.jsxs)(B.eI,{children:[(0,n.jsx)(B.lR,{children:"Department Code *"}),(0,n.jsx)(B.MJ,{children:(0,n.jsx)(v.p,{placeholder:"e.g., ENG",...s,onChange:e=>s.onChange(e.target.value.toUpperCase())})}),(0,n.jsx)(B.Rr,{children:"Unique identifier (uppercase letters, numbers, _, -)"}),(0,n.jsx)(B.C5,{})]})}})]}),(0,n.jsx)(B.zB,{control:p.control,name:"description",render:e=>{let{field:s}=e;return(0,n.jsxs)(B.eI,{children:[(0,n.jsx)(B.lR,{children:"Description"}),(0,n.jsx)(B.MJ,{children:(0,n.jsx)(E.T,{placeholder:"Brief description of the department's purpose and responsibilities",className:"min-h-[100px]",...s})}),(0,n.jsx)(B.C5,{})]})}}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsx)(B.zB,{control:p.control,name:"headOfDepartment",render:e=>{let{field:s}=e;return(0,n.jsxs)(B.eI,{children:[(0,n.jsx)(B.lR,{children:"Head of Department"}),(0,n.jsx)(B.MJ,{children:(0,n.jsx)(v.p,{placeholder:"e.g., John Smith",...s})}),(0,n.jsx)(B.Rr,{children:"Name of the department head/manager"}),(0,n.jsx)(B.C5,{})]})}}),(0,n.jsx)(B.zB,{control:p.control,name:"parentDepartmentId",render:e=>{let{field:s}=e;return(0,n.jsxs)(B.eI,{children:[(0,n.jsx)(B.lR,{children:"Parent Department"}),(0,n.jsxs)(R.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,n.jsx)(B.MJ,{children:(0,n.jsx)(R.bq,{children:(0,n.jsx)(R.yv,{placeholder:"Select parent department (optional)"})})}),(0,n.jsxs)(R.gC,{children:[(0,n.jsx)(R.eb,{value:"none",children:"No parent department"}),m?(0,n.jsx)(R.eb,{value:"loading",disabled:!0,children:"Loading departments..."}):c.map(e=>(0,n.jsxs)(R.eb,{value:e.id,children:[e.name," (",e.code,")"]},e.id))]})]}),(0,n.jsx)(B.Rr,{children:"Select if this is a sub-department"}),(0,n.jsx)(B.C5,{})]})}})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsx)(B.zB,{control:p.control,name:"costCenter",render:e=>{let{field:s}=e;return(0,n.jsxs)(B.eI,{children:[(0,n.jsx)(B.lR,{children:"Cost Center"}),(0,n.jsx)(B.MJ,{children:(0,n.jsx)(v.p,{placeholder:"e.g., CC-ENG-001",...s})}),(0,n.jsx)(B.Rr,{children:"Financial cost center code"}),(0,n.jsx)(B.C5,{})]})}}),(0,n.jsx)(B.zB,{control:p.control,name:"budgetAllocated",render:e=>{let{field:s}=e;return(0,n.jsxs)(B.eI,{children:[(0,n.jsx)(B.lR,{children:"Budget Allocated"}),(0,n.jsx)(B.MJ,{children:(0,n.jsx)(v.p,{type:"number",placeholder:"e.g., 1000000",...s,onChange:e=>s.onChange(e.target.value?parseFloat(e.target.value):void 0)})}),(0,n.jsx)(B.Rr,{children:"Annual budget in USD"}),(0,n.jsx)(B.C5,{})]})}})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-4 pt-6",children:[(0,n.jsxs)(g.$,{type:"button",variant:"outline",onClick:()=>{p.reset(),null==r||r()},disabled:i,children:[(0,n.jsx)($.A,{className:"w-4 h-4 mr-2"}),"Cancel"]}),(0,n.jsxs)(g.$,{type:"submit",disabled:i,children:[i?(0,n.jsx)(k.A,{className:"w-4 h-4 mr-2 animate-spin"}):(0,n.jsx)(z.A,{className:"w-4 h-4 mr-2"}),h?"Update Department":"Create Department"]})]})]})})})]})}var Z=t(38068),H=t(6096);function M(){var e,s,t,S,I,_,E;let B=(0,Z.Sk)(),[R,$]=(0,a.useState)({departments:[],analytics:null,loading:!0,error:null}),[k,z]=(0,a.useState)(""),[L,M]=(0,a.useState)(!1),[W,F]=(0,a.useState)(null),[J,U]=(0,a.useState)(null),[Y,G]=(0,a.useState)(!1);(0,a.useEffect)(()=>{T(),V()},[]);let T=async()=>{await (0,A.$A)(async()=>{let e=await A.br.getDepartments({includeInactive:!0,sortBy:"name",sortOrder:"asc"}),s=(0,A.yH)(e,void 0,!0);s?$(e=>({...e,departments:s.departments,loading:!1,error:null})):$(e=>({...e,loading:!1,error:"Failed to load departments"}))},e=>$(s=>({...s,loading:e})))},V=async()=>{let e=await A.br.getDepartmentAnalytics(),s=(0,A.yH)(e,void 0,!1);s&&$(e=>({...e,analytics:s}))},P=R.departments.filter(e=>e.name.toLowerCase().includes(k.toLowerCase())||e.code.toLowerCase().includes(k.toLowerCase())||e.description&&e.description.toLowerCase().includes(k.toLowerCase())),Q=async()=>{await T(),await V()},q=e=>{F(e),M(!0)},X=e=>{U(e),G(!0)},K=async e=>{confirm("Are you sure you want to delete this department?")&&await (0,A.$A)(async()=>{let s=await A.br.deleteDepartment(e);null!==(0,A.yH)(s,"Department deleted successfully")&&(await T(),await V())},e=>$(s=>({...s,loading:e})))},ee=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e);return(0,n.jsx)(H.$G,{permissions:["department_read","hr","hr_admin","super_admin"],children:(0,n.jsxs)(C.k,{onRefresh:Q,className:"flex-1",children:[(0,n.jsxs)("div",{className:"space-y-6 p-6",children:[(0,n.jsx)(w.Y,{title:"Department Management",subtitle:"Managing ".concat((null==(e=R.analytics)?void 0:e.totalDepartments)||0," departments with ").concat((null==(s=R.analytics)?void 0:s.totalEmployees)||0," employees"),actions:(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(Z.LQ,{permissions:["department_read","hr","hr_admin","super_admin"],children:(0,n.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,n.jsx)(r.A,{className:"w-4 h-4 mr-2"}),"Export"]})}),(0,n.jsx)(Z.LQ,{permissions:["department_write","hr_admin","super_admin"],children:(0,n.jsxs)(g.$,{size:"sm",onClick:()=>{F(null),M(!0)},children:[(0,n.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Add Department"]})})]})}),R.loading&&(0,n.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,n.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Loading departments..."})]})}),R.error&&(0,n.jsxs)("div",{className:"bg-destructive/10 border border-destructive/20 rounded-lg p-4",children:[(0,n.jsx)("p",{className:"text-destructive",children:R.error}),(0,n.jsx)(g.$,{variant:"outline",size:"sm",onClick:Q,className:"mt-2",children:"Try Again"})]}),!R.loading&&!R.error&&(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,n.jsx)(N.Zp,{children:(0,n.jsx)(N.Wu,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Departments"}),(0,n.jsx)("p",{className:"text-2xl font-bold",children:(null==(t=R.analytics)?void 0:t.totalDepartments)||0})]}),(0,n.jsx)(i.A,{className:"h-8 w-8 text-primary"})]})})}),(0,n.jsx)(N.Zp,{children:(0,n.jsx)(N.Wu,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Active Departments"}),(0,n.jsx)("p",{className:"text-2xl font-bold",children:(null==(S=R.analytics)?void 0:S.activeDepartments)||0})]}),(0,n.jsx)(d.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,n.jsx)(N.Zp,{children:(0,n.jsx)(N.Wu,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Employees"}),(0,n.jsx)("p",{className:"text-2xl font-bold",children:(null==(I=R.analytics)?void 0:I.totalEmployees)||0})]}),(0,n.jsx)(c.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,n.jsx)(N.Zp,{children:(0,n.jsx)(N.Wu,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Budget"}),(0,n.jsx)("p",{className:"text-2xl font-bold",children:(null==(E=R.analytics)||null==(_=E.budgetUtilization)?void 0:_.totalBudget)?ee(R.analytics.budgetUtilization.totalBudget):"$0"})]}),(0,n.jsx)(o.A,{className:"h-8 w-8 text-yellow-600"})]})})})]}),(0,n.jsx)(N.Zp,{children:(0,n.jsx)(N.Wu,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,n.jsxs)("div",{className:"relative flex-1",children:[(0,n.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,n.jsx)(v.p,{placeholder:"Search departments...",value:k,onChange:e=>z(e.target.value),className:"pl-10"})]}),(0,n.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,n.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Filters"]})]})})}),(0,n.jsxs)(N.Zp,{children:[(0,n.jsx)(N.aR,{children:(0,n.jsx)(N.ZB,{children:"Departments"})}),(0,n.jsx)(N.Wu,{children:(0,n.jsxs)(b.XI,{children:[(0,n.jsx)(b.A0,{children:(0,n.jsxs)(b.Hj,{children:[(0,n.jsx)(b.nd,{children:"Department"}),(0,n.jsx)(b.nd,{children:"Code"}),(0,n.jsx)(b.nd,{children:"Head"}),(0,n.jsx)(b.nd,{children:"Employees"}),(0,n.jsx)(b.nd,{children:"Budget"}),(0,n.jsx)(b.nd,{children:"Status"}),(0,n.jsx)(b.nd,{className:"text-right",children:"Actions"})]})}),(0,n.jsx)(b.BF,{children:P.map(e=>(0,n.jsxs)(b.Hj,{children:[(0,n.jsx)(b.nA,{children:(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium",children:e.name}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:e.description}),e.parentDepartment&&(0,n.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Parent: ",e.parentDepartment]})]})}),(0,n.jsx)(b.nA,{children:(0,n.jsx)(f.E,{variant:"outline",children:e.code})}),(0,n.jsx)(b.nA,{children:e.headOfDepartment||"Not assigned"}),(0,n.jsx)(b.nA,{children:e.employeeCount||0}),(0,n.jsx)(b.nA,{children:e.budgetAllocated?ee(e.budgetAllocated):"Not set"}),(0,n.jsx)(b.nA,{children:(0,n.jsx)(f.E,{variant:e.isActive?"default":"secondary",children:e.isActive?"Active":"Inactive"})}),(0,n.jsx)(b.nA,{className:"text-right",children:(0,n.jsxs)(D.rI,{children:[(0,n.jsx)(D.ty,{asChild:!0,children:(0,n.jsx)(g.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,n.jsx)(h.A,{className:"h-4 w-4"})})}),(0,n.jsxs)(D.SQ,{align:"end",children:[(0,n.jsxs)(D._2,{onClick:()=>X(e),children:[(0,n.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"View Details"]}),B.canWriteDepartments()&&(0,n.jsxs)(D._2,{onClick:()=>q(e),children:[(0,n.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Edit"]}),B.canWriteDepartments()&&(0,n.jsxs)(D._2,{onClick:()=>K(e.id),className:"text-destructive",children:[(0,n.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e.id))})]})})]})]}),(0,n.jsx)(y.lG,{open:L,onOpenChange:M,children:(0,n.jsxs)(y.Cf,{className:"max-w-2xl",children:[(0,n.jsx)(y.c7,{children:(0,n.jsx)(y.L3,{children:W?"Edit Department":"Add New Department"})}),(0,n.jsx)("div",{className:"p-4",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"Department form will be implemented here."})})]})}),(0,n.jsx)(y.lG,{open:L,onOpenChange:M,children:(0,n.jsxs)(y.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,n.jsx)(y.c7,{children:(0,n.jsx)(y.L3,{children:W?"Edit Department":"Create New Department"})}),(0,n.jsx)(O,{department:W,onSuccess:e=>{M(!1),F(null),T(),V()},onCancel:()=>{M(!1),F(null)},className:"border-0 shadow-none"})]})}),(0,n.jsx)(y.lG,{open:Y,onOpenChange:G,children:(0,n.jsxs)(y.Cf,{className:"max-w-4xl",children:[(0,n.jsx)(y.c7,{children:(0,n.jsx)(y.L3,{children:"Department Details"})}),(0,n.jsx)("div",{className:"p-4",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"Department details view will be implemented here."})})]})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3706,9380,6110,1647,2426,5106,1712,5409,6579,8441,1684,7358],()=>s(1048)),_N_E=e.O()}]);