(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1954],{14554:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>K});var r=s(95155),n=s(12115),t=s(35695),i=s(6874),l=s.n(i),d=s(17859),c=s(60760),o=s(73783),m=s(72713),h=s(17580),x=s(71007),u=s(69037),f=s(48136),g=s(17576),p=s(19302),b=s(55868),j=s(33109),y=s(14186),N=s(69074),v=s(81497),w=s(57434),A=s(381),_=s(75525),P=s(59947),k=s(23227),R=s(13052),z=s(42355),F=s(34835),E=s(59434),M=s(30285),S=s(91394),C=s(26126),D=s(38068),O=s(94819);let H=e=>[{title:"Overview",items:[{name:"Dashboard",icon:o.A,href:"/dashboard",badge:null},{name:"Analytics",icon:m.A,href:"/dashboard/analytics",badge:null}]},{title:"People Management",items:[...e.canReadAllEmployees()?[{name:"Employees",icon:h.A,href:"/dashboard/employees",badge:"124"}]:[],...e.canAccessHRModule()?[{name:"Recruitment",icon:x.A,href:"/dashboard/recruitment",badge:"12"},{name:"Onboarding",icon:u.A,href:"/dashboard/onboarding",badge:"3"}]:[]].filter(e=>e)},...e.canAccessOrganizationModule()?[{title:"Organization",items:[...e.canReadDepartments()?[{name:"Departments",icon:f.A,href:"/dashboard/organization/departments",badge:null}]:[],...e.canReadPositions()?[{name:"Positions",icon:g.A,href:"/dashboard/organization/positions",badge:null}]:[],{name:"Org Chart",icon:p.A,href:"/dashboard/organization/chart",badge:null}].filter(e=>e)}]:[],...e.canAccessHRModule()?[{title:"Operations",items:[{name:"Payroll",icon:b.A,href:"/dashboard/payroll",badge:null},{name:"Performance",icon:j.A,href:"/dashboard/performance",badge:"5"},{name:"Time & Attendance",icon:y.A,href:"/dashboard/attendance",badge:"2"},{name:"Leave Management",icon:N.A,href:"/dashboard/leave",badge:"8"}]}]:[],{title:"Communication",items:[{name:"Announcements",icon:v.A,href:"/dashboard/announcements",badge:"2"},{name:"Documents",icon:w.A,href:"/dashboard/documents",badge:null}]},{title:"System",items:[{name:"Settings",icon:A.A,href:"/dashboard/settings",badge:null},...e.canAccessAdminModule()?[{name:"Compliance",icon:_.A,href:"/dashboard/compliance",badge:null}]:[],{name:"Help & Support",icon:P.A,href:"/dashboard/help",badge:null}].filter(e=>e)}].filter(e=>e.items&&e.items.length>0);function L(e){let{className:a}=e,[s,i]=(0,n.useState)(!1),o=(0,t.usePathname)(),m=(0,D.Sk)(),{logout:h}=(0,O.A)(),x=H(m);return(0,r.jsxs)(d.P.div,{initial:!1,animate:{width:s?80:280},transition:{duration:.3,ease:"easeInOut"},className:(0,E.cn)("relative flex flex-col bg-card border-r border-border shadow-sm",a),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-border",children:[(0,r.jsx)(c.N,{mode:"wait",children:!s&&(0,r.jsxs)(d.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-primary rounded-lg",children:(0,r.jsx)(k.A,{className:"w-5 h-5 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"HRMS Platform"})]})]})}),(0,r.jsx)(M.$,{variant:"ghost",size:"icon",onClick:()=>i(!s),className:"h-8 w-8 text-muted-foreground hover:text-foreground",children:s?(0,r.jsx)(R.A,{className:"h-4 w-4"}):(0,r.jsx)(z.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto py-4",children:(0,r.jsx)("nav",{className:"space-y-6 px-3",children:x.map((e,a)=>(0,r.jsxs)("div",{children:[(0,r.jsx)(c.N,{children:!s&&(0,r.jsx)(d.P.h3,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:e.title})}),(0,r.jsx)("ul",{className:"space-y-1",children:e.items.map(e=>{let a=o===e.href;return(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:e.href,className:(0,E.cn)("w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",a?"bg-primary text-primary-foreground shadow-sm":"text-foreground hover:bg-muted hover:text-foreground"),children:[(0,r.jsx)(e.icon,{className:(0,E.cn)("flex-shrink-0 w-5 h-5",s?"mx-auto":"mr-3")}),(0,r.jsx)(c.N,{children:!s&&(0,r.jsxs)(d.P.div,{initial:{opacity:0,width:0},animate:{opacity:1,width:"auto"},exit:{opacity:0,width:0},className:"flex items-center justify-between flex-1 min-w-0",children:[(0,r.jsx)("span",{className:"truncate",children:e.name}),e.badge&&(0,r.jsx)(C.E,{variant:a?"secondary":"outline",className:"ml-2 text-xs",children:e.badge})]})})]})},e.name)})})]},e.title))})}),(0,r.jsx)("div",{className:"border-t border-border p-4",children:(0,r.jsxs)("div",{className:(0,E.cn)("flex items-center",s?"justify-center":"space-x-3"),children:[(0,r.jsxs)(S.eu,{className:"h-8 w-8",children:[(0,r.jsx)(S.BK,{src:"/avatars/user.jpg",alt:"User"}),(0,r.jsx)(S.q5,{children:"JD"})]}),(0,r.jsx)(c.N,{children:!s&&(0,r.jsxs)(d.P.div,{initial:{opacity:0,width:0},animate:{opacity:1,width:"auto"},exit:{opacity:0,width:0},className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-foreground truncate",children:"John Doe"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:"HR Manager"})]})}),(0,r.jsx)(c.N,{children:!s&&(0,r.jsx)(d.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,r.jsx)(M.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:h,title:"Sign out",children:(0,r.jsx)(F.A,{className:"h-4 w-4"})})})})]})})]})}var B=s(47924),$=s(23861),U=s(74783),q=s(57340),J=s(54416);let V=[{name:"Dashboard",icon:o.A,href:"/dashboard",badge:null},{name:"Employees",icon:h.A,href:"/dashboard/employees",badge:"124"},{name:"Payroll",icon:b.A,href:"/dashboard/payroll",badge:null},{name:"Performance",icon:j.A,href:"/dashboard/performance",badge:"5"},{name:"Leave",icon:N.A,href:"/dashboard/leave",badge:"8"},{name:"Settings",icon:A.A,href:"/dashboard/settings",badge:null}],W=[{name:"Search",icon:B.A,action:"search"},{name:"Notifications",icon:$.A,action:"notifications",badge:"3"},{name:"Profile",icon:x.A,action:"profile"}];function I(e){let{className:a}=e,[s,i]=(0,n.useState)(!1),[o,m]=(0,n.useState)("/dashboard"),h=(0,t.usePathname)();(0,n.useEffect)(()=>{m(h)},[h]),(0,n.useEffect)(()=>{i(!1)},[h]),(0,n.useEffect)(()=>(s?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[s]);let u=e=>{switch(e){case"search":console.log("Search triggered");break;case"notifications":console.log("Notifications triggered");break;case"profile":console.log("Profile triggered")}i(!1)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"lg:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(M.$,{variant:"ghost",size:"icon",onClick:()=>i(!0),className:"h-10 w-10",children:(0,r.jsx)(U.A,{className:"h-6 w-6"})}),(0,r.jsx)("div",{children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"})})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:W.map(e=>(0,r.jsxs)(M.$,{variant:"ghost",size:"icon",onClick:()=>u(e.action),className:"h-10 w-10 relative",children:[(0,r.jsx)(e.icon,{className:"h-5 w-5"}),e.badge&&(0,r.jsx)(C.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs",children:e.badge})]},e.name))})]})}),(0,r.jsx)(c.N,{children:s&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:()=>i(!1),className:"lg:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"}),(0,r.jsx)(d.P.div,{initial:{x:"-100%"},animate:{x:0},exit:{x:"-100%"},transition:{type:"spring",damping:30,stiffness:300},className:"lg:hidden fixed top-0 left-0 bottom-0 z-50 w-80 bg-card shadow-xl",children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-border",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-primary rounded-lg flex items-center justify-center",children:(0,r.jsx)(q.A,{className:"h-6 w-6 text-primary-foreground"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"HRMS Dashboard"})]})]}),(0,r.jsx)(M.$,{variant:"ghost",size:"icon",onClick:()=>i(!1),className:"h-10 w-10",children:(0,r.jsx)(J.A,{className:"h-6 w-6"})})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto py-6",children:(0,r.jsx)("nav",{className:"px-6 space-y-2",children:V.map((e,a)=>{let s=o===e.href;return(0,r.jsx)(d.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*a},children:(0,r.jsxs)(l(),{href:e.href,onClick:()=>{m(e.href)},className:"\n                              flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200\n                              ".concat(s?"bg-primary/10 text-primary border border-primary/20":"text-muted-foreground hover:bg-muted hover:text-foreground","\n                            "),children:[(0,r.jsx)(e.icon,{className:"h-5 w-5 ".concat(s?"text-primary":"text-muted-foreground")}),(0,r.jsx)("span",{className:"font-medium",children:e.name}),e.badge&&(0,r.jsx)(C.E,{variant:s?"default":"secondary",className:"ml-auto",children:e.badge})]})},e.name)})})}),(0,r.jsx)("div",{className:"p-6 border-t border-border",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-muted rounded-lg",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-muted-foreground/20 rounded-full flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-foreground",children:"John Doe"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"HR Manager"})]})]})})]})})]})}),(0,r.jsx)("div",{className:"lg:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 px-2 py-2",children:(0,r.jsx)("div",{className:"flex items-center justify-around",children:V.slice(0,4).map(e=>{let a=o===e.href;return(0,r.jsxs)(l(),{href:e.href,onClick:()=>{console.log("Bottom nav link clicked:",e.href),m(e.href)},className:"\n                  flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-all duration-200 relative\n                  ".concat(a?"text-primary":"text-muted-foreground hover:text-foreground","\n                "),children:[(0,r.jsx)(e.icon,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"text-xs font-medium",children:e.name}),e.badge&&(0,r.jsx)(C.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs",children:e.badge}),a&&(0,r.jsx)(d.P.div,{layoutId:"activeTab",className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"})]},e.name)})})})]})}function K(e){let{children:a}=e;return(0,r.jsx)(O.O,{children:(0,r.jsxs)("div",{className:"flex h-screen bg-background",children:[(0,r.jsx)(L,{className:"hidden lg:flex"}),(0,r.jsx)(I,{}),(0,r.jsx)("div",{className:"flex-1 flex flex-col overflow-hidden",children:(0,r.jsx)("main",{className:"flex-1 overflow-y-auto pt-16 lg:pt-0 pb-16 lg:pb-0",children:a})})]})})}},38068:(e,a,s)=>{"use strict";s.d(a,{LQ:()=>i,Sk:()=>l});var r=s(95155);s(12115);var n=s(94819),t=s(79323);function i(e){let{children:a,permission:s,permissions:i,requireAll:l=!1,role:d,roles:c,minRole:o,fallback:m=null,condition:h}=e,{user:x,isAuthenticated:u}=(0,n.A)();return u&&x?h?h(x)?(0,r.jsx)(r.Fragment,{children:a}):(0,r.jsx)(r.Fragment,{children:m}):s?(0,t._m)(x,s)?(0,r.jsx)(r.Fragment,{children:a}):(0,r.jsx)(r.Fragment,{children:m}):i&&i.length>0?(l?i.every(e=>(0,t._m)(x,e)):(0,t.sx)(x,i))?(0,r.jsx)(r.Fragment,{children:a}):(0,r.jsx)(r.Fragment,{children:m}):d?(0,t.hf)(x,d)?(0,r.jsx)(r.Fragment,{children:a}):(0,r.jsx)(r.Fragment,{children:m}):c&&c.length>0?(0,t.pX)(x,c)?(0,r.jsx)(r.Fragment,{children:a}):(0,r.jsx)(r.Fragment,{children:m}):o?(0,t.BU)(x,o)?(0,r.jsx)(r.Fragment,{children:a}):(0,r.jsx)(r.Fragment,{children:m}):(0,r.jsx)(r.Fragment,{children:a}):(0,r.jsx)(r.Fragment,{children:m})}function l(){let{user:e,isAuthenticated:a}=(0,n.A)();return{user:e,isAuthenticated:a,hasPermission:a=>(0,t._m)(e,a),hasAnyPermission:a=>(0,t.sx)(e,a),hasRole:a=>(0,t.hf)(e,a),hasAnyRole:a=>(0,t.pX)(e,a),isRoleAtLeast:a=>(0,t.BU)(e,a),canReadDepartments:()=>(0,t.sx)(e,["department_read","hr","hr_admin","super_admin"]),canWriteDepartments:()=>(0,t.sx)(e,["department_write","hr_admin","super_admin"]),canReadPositions:()=>(0,t.sx)(e,["position_read","hr","hr_admin","super_admin"]),canWritePositions:()=>(0,t.sx)(e,["position_write","hr_admin","super_admin"]),canReadAllEmployees:()=>(0,t.sx)(e,["employee_read_all","hr","hr_admin","super_admin"]),canWriteAllEmployees:()=>(0,t.sx)(e,["employee_update_all","hr","hr_admin","super_admin"]),canManageUsers:()=>(0,t.sx)(e,["user_management","hr_admin","super_admin"]),canAccessOrganizationModule:()=>(0,t.sx)(e,["department_read","position_read","hr","hr_admin","super_admin"]),canAccessHRModule:()=>(0,t.sx)(e,["hr","hr_admin","super_admin"]),canAccessAdminModule:()=>(0,t.sx)(e,["hr_admin","super_admin"])}}},90105:(e,a,s)=>{Promise.resolve().then(s.bind(s,14554))},91394:(e,a,s)=>{"use strict";s.d(a,{BK:()=>c,eu:()=>d,q5:()=>o});var r=s(95155),n=s(12115),t=s(54011),i=s(59434);let l=(0,s(74466).F)("relative flex shrink-0 overflow-hidden rounded-full",{variants:{size:{sm:"h-8 w-8",default:"h-10 w-10",lg:"h-12 w-12",xl:"h-16 w-16","2xl":"h-20 w-20"}},defaultVariants:{size:"default"}}),d=n.forwardRef((e,a)=>{let{className:s,size:n,src:d,alt:m,fallback:h,status:x,...u}=e;return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)(t.bL,{ref:a,className:(0,i.cn)(l({size:n}),s),...u,children:[(0,r.jsx)(c,{src:d,alt:m}),(0,r.jsx)(o,{children:h})]}),x&&(0,r.jsx)("div",{className:(0,i.cn)("absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-white",{"bg-green-500":"online"===x,"bg-muted-foreground":"offline"===x,"bg-yellow-500":"away"===x,"bg-red-500":"busy"===x})})]})});d.displayName=t.bL.displayName;let c=n.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,r.jsx)(t._V,{ref:a,className:(0,i.cn)("aspect-square h-full w-full",s),...n})});c.displayName=t._V.displayName;let o=n.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,r.jsx)(t.H4,{ref:a,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted text-sm font-medium",s),...n})});o.displayName=t.H4.displayName}},e=>{var a=a=>e(e.s=a);e.O(0,[3706,9380,6110,1008,1712,8441,1684,7358],()=>a(90105)),_N_E=e.O()}]);