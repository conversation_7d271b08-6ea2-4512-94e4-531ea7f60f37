(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5587],{1243:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4516:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},12318:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},12535:(e,s,a)=>{Promise.resolve().then(a.bind(a,95378))},13717:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},24944:(e,s,a)=>{"use strict";a.d(s,{k:()=>n});var t=a(95155),r=a(12115),l=a(55863),i=a(59434);let n=r.forwardRef((e,s)=>{let{className:a,value:r,...n}=e;return(0,t.jsx)(l.bL,{ref:s,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...n,children:(0,t.jsx)(l.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});n.displayName=l.bL.displayName},28883:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},40646:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},55863:(e,s,a)=>{"use strict";a.d(s,{C1:()=>N,bL:()=>y});var t=a(12115),r=a(46081),l=a(63655),i=a(95155),n="Progress",[c,d]=(0,r.A)(n),[o,m]=c(n),x=t.forwardRef((e,s)=>{var a,t,r,n;let{__scopeProgress:c,value:d=null,max:m,getValueLabel:x=h,...u}=e;(m||0===m)&&!f(m)&&console.error((a="".concat(m),t="Progress","Invalid prop `max` of value `".concat(a,"` supplied to `").concat(t,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=f(m)?m:100;null===d||v(d,p)||console.error((r="".concat(d),n="Progress","Invalid prop `value` of value `".concat(r,"` supplied to `").concat(n,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let y=v(d,p)?d:null,N=j(y)?x(y,p):void 0;return(0,i.jsx)(o,{scope:c,value:y,max:p,children:(0,i.jsx)(l.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":j(y)?y:void 0,"aria-valuetext":N,role:"progressbar","data-state":g(y,p),"data-value":null!=y?y:void 0,"data-max":p,...u,ref:s})})});x.displayName=n;var u="ProgressIndicator",p=t.forwardRef((e,s)=>{var a;let{__scopeProgress:t,...r}=e,n=m(u,t);return(0,i.jsx)(l.sG.div,{"data-state":g(n.value,n.max),"data-value":null!=(a=n.value)?a:void 0,"data-max":n.max,...r,ref:s})});function h(e,s){return"".concat(Math.round(e/s*100),"%")}function g(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function j(e){return"number"==typeof e}function f(e){return j(e)&&!isNaN(e)&&e>0}function v(e,s){return j(e)&&!isNaN(e)&&e<=s&&e>=0}p.displayName=u;var y=x,N=p},57434:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},69037:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},69074:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},69803:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},92657:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},95378:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>W});var t=a(95155),r=a(12115),l=a(17859),i=a(40646),n=a(14186),c=a(1243),d=a(57434),o=a(19946);let m=(0,o.A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]]);var x=a(69803);let u=(0,o.A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),p=(0,o.A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);var h=a(17580),g=a(4516),j=a(69037),f=a(69074),v=a(91788),y=a(84616),N=a(12318),w=a(47924),k=a(28883),A=a(92657),b=a(13717),T=a(81497),M=a(30285),C=a(62523),S=a(66695),D=a(26126),q=a(25409),z=a(91394),P=a(24944);let H=[{id:1,name:"Alex Thompson",position:"Senior Software Engineer",department:"Engineering",startDate:"2024-02-01",status:"in-progress",progress:75,completedTasks:9,totalTasks:12,avatar:"/avatars/alex.jpg",email:"<EMAIL>",phone:"+****************",manager:"Sarah Johnson",buddy:"Mike Chen"},{id:2,name:"Maria Garcia",position:"Product Manager",department:"Product",startDate:"2024-02-05",status:"pending",progress:25,completedTasks:3,totalTasks:12,avatar:"/avatars/maria.jpg",email:"<EMAIL>",phone:"+****************",manager:"David Wilson",buddy:"Emily Davis"},{id:3,name:"James Liu",position:"UX Designer",department:"Design",startDate:"2024-01-28",status:"completed",progress:100,completedTasks:12,totalTasks:12,avatar:"/avatars/james.jpg",email:"<EMAIL>",phone:"+****************",manager:"Lisa Park",buddy:"Tom Anderson"}],L=[{id:1,title:"Complete HR Documentation",category:"Documentation",required:!0,estimatedTime:"30 min"},{id:2,title:"IT Setup & Equipment Assignment",category:"IT Setup",required:!0,estimatedTime:"45 min"},{id:3,title:"Security Badge & Access Cards",category:"Security",required:!0,estimatedTime:"15 min"},{id:4,title:"Benefits Enrollment",category:"Benefits",required:!0,estimatedTime:"60 min"},{id:5,title:"Company Orientation Session",category:"Training",required:!0,estimatedTime:"2 hours"},{id:6,title:"Department Introduction",category:"Team",required:!0,estimatedTime:"1 hour"},{id:7,title:"Buddy System Assignment",category:"Team",required:!0,estimatedTime:"30 min"},{id:8,title:"Workspace Setup",category:"Workspace",required:!0,estimatedTime:"30 min"},{id:9,title:"Manager 1:1 Meeting",category:"Management",required:!0,estimatedTime:"45 min"},{id:10,title:"Company Policies Review",category:"Training",required:!0,estimatedTime:"45 min"},{id:11,title:"Role-Specific Training",category:"Training",required:!0,estimatedTime:"4 hours"},{id:12,title:"30-Day Check-in",category:"Follow-up",required:!0,estimatedTime:"30 min"}],E={totalNewHires:H.length,inProgress:H.filter(e=>"in-progress"===e.status).length,completed:H.filter(e=>"completed"===e.status).length,pending:H.filter(e=>"pending"===e.status).length,avgCompletionTime:14,avgSatisfactionScore:4.6};function W(){let[e,s]=(0,r.useState)(""),[a,o]=(0,r.useState)("All"),[W,Z]=(0,r.useState)("All"),[B,$]=(0,r.useState)("overview"),I=H.filter(s=>{let t=s.name.toLowerCase().includes(e.toLowerCase())||s.position.toLowerCase().includes(e.toLowerCase())||s.department.toLowerCase().includes(e.toLowerCase()),r="All"===a||s.status===a,l="All"===W||s.department===W;return t&&r&&l}),R=e=>{switch(e){case"completed":return"success";case"in-progress":return"warning";default:return"secondary"}},O=e=>{switch(e){case"completed":return(0,t.jsx)(i.A,{className:"h-4 w-4"});case"in-progress":default:return(0,t.jsx)(n.A,{className:"h-4 w-4"});case"pending":return(0,t.jsx)(c.A,{className:"h-4 w-4"})}},V=e=>{switch(e){case"Documentation":default:return(0,t.jsx)(d.A,{className:"h-4 w-4"});case"IT Setup":return(0,t.jsx)(m,{className:"h-4 w-4"});case"Security":return(0,t.jsx)(x.A,{className:"h-4 w-4"});case"Benefits":return(0,t.jsx)(u,{className:"h-4 w-4"});case"Training":return(0,t.jsx)(p,{className:"h-4 w-4"});case"Team":return(0,t.jsx)(h.A,{className:"h-4 w-4"});case"Workspace":return(0,t.jsx)(g.A,{className:"h-4 w-4"});case"Management":return(0,t.jsx)(j.A,{className:"h-4 w-4"});case"Follow-up":return(0,t.jsx)(f.A,{className:"h-4 w-4"})}};return(0,t.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,t.jsx)(q.Y,{title:"Onboarding Management",subtitle:"Managing onboarding for ".concat(E.totalNewHires," new hires"),actions:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(M.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Export Report"]}),(0,t.jsxs)(M.$,{size:"sm",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 mr-2"}),"Add New Hire"]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,t.jsx)(S.Zp,{children:(0,t.jsx)(S.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:E.totalNewHires}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total New Hires"})]})]})})}),(0,t.jsx)(S.Zp,{children:(0,t.jsx)(S.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.A,{className:"h-8 w-8 text-orange-600 dark:text-orange-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:E.inProgress}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"In Progress"})]})]})})}),(0,t.jsx)(S.Zp,{children:(0,t.jsx)(S.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(i.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:E.completed}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Completed"})]})]})})}),(0,t.jsx)(S.Zp,{children:(0,t.jsx)(S.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"h-8 w-8 text-yellow-600 dark:text-yellow-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:E.pending}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pending"})]})]})})}),(0,t.jsx)(S.Zp,{children:(0,t.jsx)(S.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:E.avgCompletionTime}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Days"})]})]})})}),(0,t.jsx)(S.Zp,{children:(0,t.jsx)(S.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"h-8 w-8 text-teal-600 dark:text-teal-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:E.avgSatisfactionScore}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Satisfaction"})]})]})})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(M.$,{variant:"overview"===B?"default":"outline",size:"sm",onClick:()=>$("overview"),children:"Overview"}),(0,t.jsx)(M.$,{variant:"tasks"===B?"default":"outline",size:"sm",onClick:()=>$("tasks"),children:"Tasks"}),(0,t.jsx)(M.$,{variant:"schedule"===B?"default":"outline",size:"sm",onClick:()=>$("schedule"),children:"Schedule"})]}),(0,t.jsx)(S.Zp,{children:(0,t.jsx)(S.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(w.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(C.p,{placeholder:"Search by name, position, or department...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("select",{value:a,onChange:e=>o(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"All",children:"All Status"}),(0,t.jsx)("option",{value:"pending",children:"Pending"}),(0,t.jsx)("option",{value:"in-progress",children:"In Progress"}),(0,t.jsx)("option",{value:"completed",children:"Completed"})]}),(0,t.jsxs)("select",{value:W,onChange:e=>Z(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"All",children:"All Departments"}),(0,t.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,t.jsx)("option",{value:"Product",children:"Product"}),(0,t.jsx)("option",{value:"Design",children:"Design"})]})]})]})})}),"overview"===B?(0,t.jsx)(S.Zp,{children:(0,t.jsx)(S.Wu,{className:"p-0",children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"New Hire"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Position"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Start Date"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Progress"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Manager"}),(0,t.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,t.jsx)("tbody",{children:I.map((e,s)=>(0,t.jsxs)(l.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*s},className:"border-b border-border hover:bg-muted/50",children:[(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(z.eu,{className:"h-10 w-10",children:[(0,t.jsx)(z.BK,{src:e.avatar,alt:e.name}),(0,t.jsx)(z.q5,{children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground flex items-center",children:[(0,t.jsx)(k.A,{className:"h-3 w-3 mr-1"}),e.email]})]})]})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:e.position}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.department})]})}),(0,t.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:new Date(e.startDate).toLocaleDateString()}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsxs)("span",{children:[e.completedTasks,"/",e.totalTasks," tasks"]}),(0,t.jsxs)("span",{children:[e.progress,"%"]})]}),(0,t.jsx)(P.k,{value:e.progress,className:"w-20"})]})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)(D.E,{variant:R(e.status),className:"flex items-center space-x-1 w-fit",children:[O(e.status),(0,t.jsx)("span",{className:"ml-1",children:e.status.charAt(0).toUpperCase()+e.status.slice(1).replace("-"," ")})]})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:e.manager}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Buddy: ",e.buddy]})]})}),(0,t.jsx)("td",{className:"py-4 px-6",children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(M.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(A.A,{className:"h-4 w-4"})}),(0,t.jsx)(M.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(b.A,{className:"h-4 w-4"})}),(0,t.jsx)(M.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,t.jsx)(T.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})}):"tasks"===B?(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:L.map((e,s)=>(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*s},children:(0,t.jsxs)(S.Zp,{children:[(0,t.jsx)(S.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[V(e.category),(0,t.jsxs)("div",{children:[(0,t.jsx)(S.ZB,{className:"text-sm",children:e.title}),(0,t.jsx)(S.BT,{className:"text-xs",children:e.category})]})]}),e.required&&(0,t.jsx)(D.E,{variant:"destructive",className:"text-xs",children:"Required"})]})}),(0,t.jsx)(S.Wu,{className:"pt-0",children:(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-muted-foreground",children:"Est. Time:"}),(0,t.jsx)("span",{className:"font-medium",children:e.estimatedTime})]})})]})},e.id))}):(0,t.jsxs)(S.Zp,{children:[(0,t.jsxs)(S.aR,{children:[(0,t.jsx)(S.ZB,{children:"Onboarding Schedule"}),(0,t.jsx)(S.BT,{children:"Upcoming onboarding activities and milestones"})]}),(0,t.jsx)(S.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium",children:"New Hire Orientation"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"February 5, 2024 at 9:00 AM"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Attendees: Maria Garcia"})]}),(0,t.jsx)(M.$,{variant:"outline",size:"sm",children:"View Details"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-green-50 dark:bg-green-950/20 rounded-lg",children:[(0,t.jsx)(h.A,{className:"h-8 w-8 text-green-600"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium",children:"Department Introduction"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"February 6, 2024 at 2:00 PM"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Attendees: Alex Thompson"})]}),(0,t.jsx)(M.$,{variant:"outline",size:"sm",children:"View Details"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg",children:[(0,t.jsx)(j.A,{className:"h-8 w-8 text-purple-600"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium",children:"30-Day Check-in"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"February 28, 2024 at 10:00 AM"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Attendees: James Liu"})]}),(0,t.jsx)(M.$,{variant:"outline",size:"sm",children:"View Details"})]})]})})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3706,9380,6110,1647,1712,5409,8441,1684,7358],()=>s(12535)),_N_E=e.O()}]);