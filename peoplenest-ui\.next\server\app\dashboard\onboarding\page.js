(()=>{var e={};e.id=587,e.ids=[587],e.modules={1929:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>W});var a=t(60687),r=t(43210),i=t(50371),n=t(5336),l=t(48730),d=t(43649),o=t(10022),c=t(62688);let m=(0,c.A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]]);var x=t(19959);let p=(0,c.A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),u=(0,c.A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);var h=t(41312),g=t(97992),j=t(86561),f=t(40228),v=t(31158),N=t(96474),y=t(23026),b=t(99270),w=t(41550),A=t(13861),k=t(63143),P=t(58887),T=t(29523),C=t(89667),q=t(44493),M=t(96834),S=t(96724),D=t(32584),z=t(46657);let $=[{id:1,name:"Alex Thompson",position:"Senior Software Engineer",department:"Engineering",startDate:"2024-02-01",status:"in-progress",progress:75,completedTasks:9,totalTasks:12,avatar:"/avatars/alex.jpg",email:"<EMAIL>",phone:"+****************",manager:"Sarah Johnson",buddy:"Mike Chen"},{id:2,name:"Maria Garcia",position:"Product Manager",department:"Product",startDate:"2024-02-05",status:"pending",progress:25,completedTasks:3,totalTasks:12,avatar:"/avatars/maria.jpg",email:"<EMAIL>",phone:"+****************",manager:"David Wilson",buddy:"Emily Davis"},{id:3,name:"James Liu",position:"UX Designer",department:"Design",startDate:"2024-01-28",status:"completed",progress:100,completedTasks:12,totalTasks:12,avatar:"/avatars/james.jpg",email:"<EMAIL>",phone:"+****************",manager:"Lisa Park",buddy:"Tom Anderson"}],E=[{id:1,title:"Complete HR Documentation",category:"Documentation",required:!0,estimatedTime:"30 min"},{id:2,title:"IT Setup & Equipment Assignment",category:"IT Setup",required:!0,estimatedTime:"45 min"},{id:3,title:"Security Badge & Access Cards",category:"Security",required:!0,estimatedTime:"15 min"},{id:4,title:"Benefits Enrollment",category:"Benefits",required:!0,estimatedTime:"60 min"},{id:5,title:"Company Orientation Session",category:"Training",required:!0,estimatedTime:"2 hours"},{id:6,title:"Department Introduction",category:"Team",required:!0,estimatedTime:"1 hour"},{id:7,title:"Buddy System Assignment",category:"Team",required:!0,estimatedTime:"30 min"},{id:8,title:"Workspace Setup",category:"Workspace",required:!0,estimatedTime:"30 min"},{id:9,title:"Manager 1:1 Meeting",category:"Management",required:!0,estimatedTime:"45 min"},{id:10,title:"Company Policies Review",category:"Training",required:!0,estimatedTime:"45 min"},{id:11,title:"Role-Specific Training",category:"Training",required:!0,estimatedTime:"4 hours"},{id:12,title:"30-Day Check-in",category:"Follow-up",required:!0,estimatedTime:"30 min"}],_={totalNewHires:$.length,inProgress:$.filter(e=>"in-progress"===e.status).length,completed:$.filter(e=>"completed"===e.status).length,pending:$.filter(e=>"pending"===e.status).length,avgCompletionTime:14,avgSatisfactionScore:4.6};function W(){let[e,s]=(0,r.useState)(""),[t,c]=(0,r.useState)("All"),[W,L]=(0,r.useState)("All"),[R,H]=(0,r.useState)("overview"),Z=$.filter(s=>{let a=s.name.toLowerCase().includes(e.toLowerCase())||s.position.toLowerCase().includes(e.toLowerCase())||s.department.toLowerCase().includes(e.toLowerCase()),r="All"===t||s.status===t,i="All"===W||s.department===W;return a&&r&&i}),B=e=>{switch(e){case"completed":return"success";case"in-progress":return"warning";default:return"secondary"}},I=e=>{switch(e){case"completed":return(0,a.jsx)(n.A,{className:"h-4 w-4"});case"in-progress":default:return(0,a.jsx)(l.A,{className:"h-4 w-4"});case"pending":return(0,a.jsx)(d.A,{className:"h-4 w-4"})}},G=e=>{switch(e){case"Documentation":default:return(0,a.jsx)(o.A,{className:"h-4 w-4"});case"IT Setup":return(0,a.jsx)(m,{className:"h-4 w-4"});case"Security":return(0,a.jsx)(x.A,{className:"h-4 w-4"});case"Benefits":return(0,a.jsx)(p,{className:"h-4 w-4"});case"Training":return(0,a.jsx)(u,{className:"h-4 w-4"});case"Team":return(0,a.jsx)(h.A,{className:"h-4 w-4"});case"Workspace":return(0,a.jsx)(g.A,{className:"h-4 w-4"});case"Management":return(0,a.jsx)(j.A,{className:"h-4 w-4"});case"Follow-up":return(0,a.jsx)(f.A,{className:"h-4 w-4"})}};return(0,a.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,a.jsx)(S.Y,{title:"Onboarding Management",subtitle:`Managing onboarding for ${_.totalNewHires} new hires`,actions:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(T.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Export Report"]}),(0,a.jsxs)(T.$,{size:"sm",children:[(0,a.jsx)(N.A,{className:"w-4 h-4 mr-2"}),"Add New Hire"]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4",children:[(0,a.jsx)(q.Zp,{children:(0,a.jsx)(q.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.totalNewHires}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total New Hires"})]})]})})}),(0,a.jsx)(q.Zp,{children:(0,a.jsx)(q.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.A,{className:"h-8 w-8 text-orange-600 dark:text-orange-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.inProgress}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"In Progress"})]})]})})}),(0,a.jsx)(q.Zp,{children:(0,a.jsx)(q.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.completed}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Completed"})]})]})})}),(0,a.jsx)(q.Zp,{children:(0,a.jsx)(q.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 text-yellow-600 dark:text-yellow-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.pending}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pending"})]})]})})}),(0,a.jsx)(q.Zp,{children:(0,a.jsx)(q.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.A,{className:"h-8 w-8 text-purple-600 dark:text-purple-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.avgCompletionTime}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Days"})]})]})})}),(0,a.jsx)(q.Zp,{children:(0,a.jsx)(q.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 text-teal-600 dark:text-teal-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-foreground",children:_.avgSatisfactionScore}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Satisfaction"})]})]})})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(T.$,{variant:"overview"===R?"default":"outline",size:"sm",onClick:()=>H("overview"),children:"Overview"}),(0,a.jsx)(T.$,{variant:"tasks"===R?"default":"outline",size:"sm",onClick:()=>H("tasks"),children:"Tasks"}),(0,a.jsx)(T.$,{variant:"schedule"===R?"default":"outline",size:"sm",onClick:()=>H("schedule"),children:"Schedule"})]}),(0,a.jsx)(q.Zp,{children:(0,a.jsx)(q.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(C.p,{placeholder:"Search by name, position, or department...",value:e,onChange:e=>s(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("select",{value:t,onChange:e=>c(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,a.jsx)("option",{value:"All",children:"All Status"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"in-progress",children:"In Progress"}),(0,a.jsx)("option",{value:"completed",children:"Completed"})]}),(0,a.jsxs)("select",{value:W,onChange:e=>L(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,a.jsx)("option",{value:"All",children:"All Departments"}),(0,a.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,a.jsx)("option",{value:"Product",children:"Product"}),(0,a.jsx)("option",{value:"Design",children:"Design"})]})]})]})})}),"overview"===R?(0,a.jsx)(q.Zp,{children:(0,a.jsx)(q.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-muted/50 border-b border-border",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"New Hire"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Position"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Start Date"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Progress"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Status"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Manager"}),(0,a.jsx)("th",{className:"text-left py-3 px-6 font-medium text-foreground",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:Z.map((e,s)=>(0,a.jsxs)(i.P.tr,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.05*s},className:"border-b border-border hover:bg-muted/50",children:[(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(D.eu,{className:"h-10 w-10",children:[(0,a.jsx)(D.BK,{src:e.avatar,alt:e.name}),(0,a.jsx)(D.q5,{children:e.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground flex items-center",children:[(0,a.jsx)(w.A,{className:"h-3 w-3 mr-1"}),e.email]})]})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:e.position}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.department})]})}),(0,a.jsx)("td",{className:"py-4 px-6 text-muted-foreground",children:new Date(e.startDate).toLocaleDateString()}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsxs)("span",{children:[e.completedTasks,"/",e.totalTasks," tasks"]}),(0,a.jsxs)("span",{children:[e.progress,"%"]})]}),(0,a.jsx)(z.k,{value:e.progress,className:"w-20"})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)(M.E,{variant:B(e.status),className:"flex items-center space-x-1 w-fit",children:[I(e.status),(0,a.jsx)("span",{className:"ml-1",children:e.status.charAt(0).toUpperCase()+e.status.slice(1).replace("-"," ")})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-foreground",children:e.manager}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Buddy: ",e.buddy]})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(T.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(A.A,{className:"h-4 w-4"})}),(0,a.jsx)(T.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(k.A,{className:"h-4 w-4"})}),(0,a.jsx)(T.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(P.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})}):"tasks"===R?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:E.map((e,s)=>(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*s},children:(0,a.jsxs)(q.Zp,{children:[(0,a.jsx)(q.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[G(e.category),(0,a.jsxs)("div",{children:[(0,a.jsx)(q.ZB,{className:"text-sm",children:e.title}),(0,a.jsx)(q.BT,{className:"text-xs",children:e.category})]})]}),e.required&&(0,a.jsx)(M.E,{variant:"destructive",className:"text-xs",children:"Required"})]})}),(0,a.jsx)(q.Wu,{className:"pt-0",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Est. Time:"}),(0,a.jsx)("span",{className:"font-medium",children:e.estimatedTime})]})})]})},e.id))}):(0,a.jsxs)(q.Zp,{children:[(0,a.jsxs)(q.aR,{children:[(0,a.jsx)(q.ZB,{children:"Onboarding Schedule"}),(0,a.jsx)(q.BT,{children:"Upcoming onboarding activities and milestones"})]}),(0,a.jsx)(q.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg",children:[(0,a.jsx)(f.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"New Hire Orientation"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"February 5, 2024 at 9:00 AM"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Attendees: Maria Garcia"})]}),(0,a.jsx)(T.$,{variant:"outline",size:"sm",children:"View Details"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-green-50 dark:bg-green-950/20 rounded-lg",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"Department Introduction"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"February 6, 2024 at 2:00 PM"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Attendees: Alex Thompson"})]}),(0,a.jsx)(T.$,{variant:"outline",size:"sm",children:"View Details"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 text-purple-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:"30-Day Check-in"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"February 28, 2024 at 10:00 AM"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Attendees: James Liu"})]}),(0,a.jsx)(T.$,{variant:"outline",size:"sm",children:"View Details"})]})]})})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6356:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o={children:["",{children:["dashboard",{children:["onboarding",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,55819)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\onboarding\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\onboarding\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/onboarding/page",pathname:"/dashboard/onboarding",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},23026:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},26313:(e,s,t)=>{Promise.resolve().then(t.bind(t,55819))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41550:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},43649:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},46657:(e,s,t)=>{"use strict";t.d(s,{k:()=>y});var a=t(60687),r=t(43210),i=t(11273),n=t(14163),l="Progress",[d,o]=(0,i.A)(l),[c,m]=d(l),x=r.forwardRef((e,s)=>{var t,r;let{__scopeProgress:i,value:l=null,max:d,getValueLabel:o=h,...m}=e;(d||0===d)&&!f(d)&&console.error((t=`${d}`,`Invalid prop \`max\` of value \`${t}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let x=f(d)?d:100;null===l||v(l,x)||console.error((r=`${l}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let p=v(l,x)?l:null,u=j(p)?o(p,x):void 0;return(0,a.jsx)(c,{scope:i,value:p,max:x,children:(0,a.jsx)(n.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":j(p)?p:void 0,"aria-valuetext":u,role:"progressbar","data-state":g(p,x),"data-value":p??void 0,"data-max":x,...m,ref:s})})});x.displayName=l;var p="ProgressIndicator",u=r.forwardRef((e,s)=>{let{__scopeProgress:t,...r}=e,i=m(p,t);return(0,a.jsx)(n.sG.div,{"data-state":g(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...r,ref:s})});function h(e,s){return`${Math.round(e/s*100)}%`}function g(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function j(e){return"number"==typeof e}function f(e){return j(e)&&!isNaN(e)&&e>0}function v(e,s){return j(e)&&!isNaN(e)&&e<=s&&e>=0}u.displayName=p;var N=t(4780);let y=r.forwardRef(({className:e,value:s,...t},r)=>(0,a.jsx)(x,{ref:r,className:(0,N.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:(0,a.jsx)(u,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));y.displayName=x.displayName},55819:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\onboarding\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\onboarding\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},68169:(e,s,t)=>{Promise.resolve().then(t.bind(t,1929))},79551:e=>{"use strict";e.exports=require("url")},97992:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,912,18,722,632,809,456],()=>t(6356));module.exports=a})();