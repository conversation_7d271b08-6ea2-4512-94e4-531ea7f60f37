"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/employees/page",{

/***/ "(app-pages-browser)/./src/lib/roleUtils.ts":
/*!******************************!*\
  !*** ./src/lib/roleUtils.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRole: () => (/* binding */ formatRole),\n/* harmony export */   getRoleBadgeProps: () => (/* binding */ getRoleBadgeProps),\n/* harmony export */   getRoleColor: () => (/* binding */ getRoleColor),\n/* harmony export */   getRoleLevel: () => (/* binding */ getRoleLevel),\n/* harmony export */   hasRoleLevel: () => (/* binding */ hasRoleLevel),\n/* harmony export */   isAdminRole: () => (/* binding */ isAdminRole)\n/* harmony export */ });\n// Role utility functions for formatting and display\n/**\n * Format role for display in UI\n * Converts technical role names to user-friendly display names\n */ function formatRole(role) {\n    const roleMap = {\n        'super_admin': 'Super Admin',\n        'system_admin': 'System Admin',\n        'hr_admin': 'HR Admin',\n        'hr': 'HR',\n        'manager': 'Manager',\n        'employee': 'Employee',\n        'recruiter': 'Recruiter',\n        'payroll': 'Payroll',\n        'it': 'IT'\n    };\n    if (!role) return 'Employee';\n    return roleMap[role] || 'Employee';\n}\n/**\n * Get role color for UI styling\n * Returns appropriate color classes for different roles\n */ function getRoleColor(role) {\n    const colorMap = {\n        'super_admin': 'text-red-600 bg-red-500/10 border-red-500/20 dark:text-red-400',\n        'system_admin': 'text-purple-600 bg-purple-500/10 border-purple-500/20 dark:text-purple-400',\n        'hr_admin': 'text-blue-600 bg-blue-500/10 border-blue-500/20 dark:text-blue-400',\n        'hr': 'text-green-600 bg-green-500/10 border-green-500/20 dark:text-green-400',\n        'manager': 'text-orange-600 bg-orange-500/10 border-orange-500/20 dark:text-orange-400',\n        'employee': 'text-muted-foreground bg-muted border-border',\n        'recruiter': 'text-indigo-600 bg-indigo-500/10 border-indigo-500/20 dark:text-indigo-400',\n        'payroll': 'text-yellow-600 bg-yellow-500/10 border-yellow-500/20 dark:text-yellow-400',\n        'it': 'text-cyan-600 bg-cyan-500/10 border-cyan-500/20 dark:text-cyan-400'\n    };\n    if (!role) return colorMap['employee'];\n    return colorMap[role] || colorMap['employee'];\n}\n/**\n * Get role badge component props\n * Returns formatted role name and styling for badge display\n */ function getRoleBadgeProps(role) {\n    return {\n        text: formatRole(role),\n        className: getRoleColor(role)\n    };\n}\n/**\n * Check if role is admin level (hr_admin, system_admin, super_admin)\n */ function isAdminRole(role) {\n    const adminRoles = [\n        'super_admin',\n        'system_admin',\n        'hr_admin'\n    ];\n    return role ? adminRoles.includes(role) : false;\n}\n/**\n * Get role hierarchy level (higher number = more permissions)\n */ function getRoleLevel(role) {\n    const roleLevels = {\n        'employee': 1,\n        'recruiter': 2,\n        'payroll': 2,\n        'it': 2,\n        'hr': 3,\n        'manager': 4,\n        'hr_admin': 5,\n        'system_admin': 6,\n        'super_admin': 7\n    };\n    if (!role) return 1;\n    return roleLevels[role] || 1;\n}\n/**\n * Compare if first role has higher or equal level than second role\n */ function hasRoleLevel(userRole, requiredRole) {\n    return getRoleLevel(userRole) >= getRoleLevel(requiredRole);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/roleUtils.ts\n"));

/***/ })

});