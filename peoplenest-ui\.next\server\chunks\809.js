exports.id=809,exports.ids=[809],exports.modules={3216:(e,t,r)=>{Promise.resolve().then(r.bind(r,5388))},4148:(e,t,r)=>{"use strict";r.d(t,{AZ:()=>a,OD:()=>s,fu:()=>o,zS:()=>n});function n(){return"system"}function o(e){}function s(){let e=n()}function a(e){}},4780:(e,t,r)=>{"use strict";r.d(t,{Ee:()=>i,cn:()=>s,vv:()=>a});var n=r(49384),o=r(82348);function s(...e){return(0,o.QP)((0,n.$)(e))}function a(e,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e)}function i(e){return new Intl.NumberFormat("en-US",{style:"percent",minimumFractionDigits:1,maximumFractionDigits:1}).format(e/100)}},5388:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var n=r(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\components\\providers\\theme-provider.tsx","useTheme");let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\components\\providers\\theme-provider.tsx","ThemeProvider")},12944:(e,t,r)=>{Promise.resolve().then(r.bind(r,35862))},19731:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var n=r(60687),o=r(43210),s=r(8730),a=r(24224),i=r(4780);let l=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700",warning:"bg-yellow-600 text-white hover:bg-yellow-700"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,loading:a=!1,leftIcon:d,rightIcon:c,children:u,disabled:m,...h},f)=>{let p=o?s.DX:"button";return(0,n.jsxs)(p,{className:(0,i.cn)(l({variant:t,size:r,className:e})),ref:f,disabled:m||a,...h,children:[a&&(0,n.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!a&&d&&(0,n.jsx)("span",{className:"mr-2",children:d}),u,!a&&c&&(0,n.jsx)("span",{className:"ml-2",children:c})]})});d.displayName="Button"},35862:(e,t,r)=>{"use strict";r.d(t,{D:()=>i,ThemeProvider:()=>l});var n=r(60687),o=r(43210),s=r(4148);let a=(0,o.createContext)(void 0);function i(){let e=(0,o.useContext)(a);return void 0===e?{theme:"system",setTheme:()=>{},toggleTheme:()=>{},systemTheme:"light",actualTheme:"light"}:e}function l({children:e,defaultTheme:t="light"}){let[r,i]=(0,o.useState)(t),[l,d]=(0,o.useState)("light"),[c,u]=(0,o.useState)(!1),m=e=>{i(e),(0,s.fu)(e),(0,s.AZ)(e)};return(0,n.jsx)(a.Provider,{value:{theme:r,setTheme:m,toggleTheme:()=>{m("light"===("system"===r?l:r)?"dark":"light")},systemTheme:l,actualTheme:"system"===r?l:r},children:e})}},40565:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,O:()=>l});var n=r(60687),o=r(43210),s=r(63523);let a=(0,o.createContext)(void 0);function i(){let e=(0,o.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l({children:e}){let[t,r]=(0,o.useState)({user:null,isAuthenticated:!1,isLoading:!0,token:null}),i=(0,o.useCallback)(async(e,t)=>{try{r(e=>({...e,isLoading:!0}));let n=await fetch(`http://localhost:3002${s.a7.LOGIN}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(n.ok){let{user:e,tokens:t,sessionId:o,deviceFingerprint:a}=await n.json(),i={accessToken:t.accessToken,refreshToken:t.refreshToken,expiresIn:t.expiresIn,tokenType:t.tokenType,sessionId:o,deviceFingerprint:a};return(0,s.do)(JSON.stringify(i)),(0,s.To)(e),r({user:e,isAuthenticated:!0,isLoading:!1,token:t.accessToken}),!0}{let e=await n.json().catch(()=>({}));return console.error("Login failed:",e),r(e=>({...e,isLoading:!1})),!1}}catch(e){return console.error("Login failed:",e),r(e=>({...e,isLoading:!1})),!1}},[]),l=(0,o.useCallback)(async()=>{try{let e=(0,s.Pt)(),t=null;if(e)try{t=JSON.parse(e)}catch{t={accessToken:e}}if(t?.accessToken)try{await fetch(`http://localhost:3002${s.a7.LOGOUT}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t.accessToken}`},body:JSON.stringify({refreshToken:t.refreshToken,sessionId:t.sessionId})})}catch(e){console.warn("Logout API call failed:",e)}}catch(e){console.warn("Error during logout:",e)}finally{(0,s.$g)(),r({user:null,isAuthenticated:!1,isLoading:!1,token:null})}},[]),d=(0,o.useCallback)(async()=>{try{let e,t=(0,s.Pt)();if(!t)return!1;try{e=JSON.parse(t)}catch{e={accessToken:t}}if(!e.refreshToken)return l(),!1;let n=await fetch(`http://localhost:3002${s.a7.REFRESH}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e.refreshToken,deviceFingerprint:e.deviceFingerprint})});if(!n.ok)return l(),!1;{let{tokens:t}=await n.json(),o={...e,accessToken:t.accessToken,refreshToken:t.refreshToken,expiresIn:t.expiresIn};return(0,s.do)(JSON.stringify(o)),r(e=>({...e,token:t.accessToken})),!0}}catch(e){return console.error("Token refresh failed:",e),l(),!1}},[l]),c=(0,o.useCallback)(e=>{(0,s.To)(e),r(t=>({...t,user:e}))},[]),u={...t,login:i,logout:l,refreshToken:d,updateUser:c};return(0,n.jsx)(a.Provider,{value:u,children:e})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>u,ZB:()=>d,Zp:()=>i,aR:()=>l});var n=r(60687),o=r(43210),s=r(4780);let a=(0,r(24224).F)("rounded-lg border bg-card text-card-foreground shadow-sm",{variants:{variant:{default:"border-border",elevated:"shadow-md",outlined:"border-2",ghost:"border-transparent shadow-none"},padding:{none:"",sm:"p-4",default:"p-6",lg:"p-8"}},defaultVariants:{variant:"default",padding:"default"}}),i=o.forwardRef(({className:e,variant:t,padding:r,hover:o=!1,...i},l)=>(0,n.jsx)("div",{ref:l,className:(0,s.cn)(a({variant:t,padding:r}),o&&"transition-shadow hover:shadow-md",e),...i}));i.displayName="Card";let l=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let d=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("h3",{ref:r,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let c=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let u=o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent",o.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},61135:()=>{},63523:(e,t,r)=>{"use strict";function n(){return null}function o(e){}function s(){}function a(){return null}function i(e){}function l(e,t){return!!e&&e.permissions.includes(t)}function d(e,t){return!!e&&t.some(t=>e.permissions.includes(t))}function c(e,t){return!!e&&e.role===t}function u(e,t){return!!e&&t.includes(e.role)}function m(e,t){if(!e)return!1;let r=["employee","manager","hr","hr_admin","super_admin"];return r.indexOf(e.role)>=r.indexOf(t)}r.d(t,{$g:()=>s,BU:()=>m,Pt:()=>n,Ti:()=>a,To:()=>i,_m:()=>l,a7:()=>h,do:()=>o,hf:()=>c,pX:()=>u,sx:()=>d});let h={LOGIN:"/api/auth/login",LOGOUT:"/api/auth/logout",REFRESH:"/api/auth/refresh",ME:"/api/auth/me"}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},82939:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(60687),o=r(43210),s=r(4780);let a=(0,r(24224).F)("flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"",error:"border-red-500 focus-visible:ring-red-500",success:"border-green-500 focus-visible:ring-green-500"},size:{default:"h-10",sm:"h-9 px-2 text-xs",lg:"h-11 px-4"}},defaultVariants:{variant:"default",size:"default"}}),i=o.forwardRef(({className:e,type:t,variant:r,size:i,leftIcon:l,rightIcon:d,error:c,label:u,helperText:m,id:h,...f},p)=>{let g=h||o.useId(),b=!!c;return(0,n.jsxs)("div",{className:"w-full",children:[u&&(0,n.jsx)("label",{htmlFor:g,className:"block text-sm font-medium text-foreground mb-1",children:u}),(0,n.jsxs)("div",{className:"relative",children:[l&&(0,n.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:l}),(0,n.jsx)("input",{type:t,className:(0,s.cn)(a({variant:b?"error":r,size:i,className:e}),l&&"pl-10",d&&"pr-10"),ref:p,id:g,...f}),d&&(0,n.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:d})]}),(c||m)&&(0,n.jsx)("p",{className:(0,s.cn)("mt-1 text-xs",b?"text-destructive":"text-muted-foreground"),children:c||m})]})});i.displayName="Input"},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>d,viewport:()=>c});var n=r(37413),o=r(58745),s=r.n(o),a=r(64066),i=r.n(a);r(61135);var l=r(5388);let d={title:"PeopleNest - Enterprise HRMS Platform",description:"Modern, AI-powered Human Resource Management System for enterprise organizations",keywords:["HRMS","HR","Human Resources","Employee Management","Payroll","Performance"],manifest:"/manifest.json",appleWebApp:{capable:!0,statusBarStyle:"default",title:"PeopleNest HRMS"},formatDetection:{telephone:!1},other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","apple-mobile-web-app-status-bar-style":"default","apple-mobile-web-app-title":"PeopleNest","application-name":"PeopleNest HRMS","msapplication-TileColor":"#3b82f6","msapplication-config":"/browserconfig.xml"}},c={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,viewportFit:"cover",themeColor:"#3b82f6"};function u({children:e}){return(0,n.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,n.jsx)("head",{children:(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' light';
                }
              })();
            `}})}),(0,n.jsxs)("body",{className:`${s().variable} ${i().variable} font-sans antialiased touch-manipulation`,children:[(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' light';
                }
              })();
            `}}),(0,n.jsx)(l.ThemeProvider,{children:e}),(0,n.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `}})]})]})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(60687);r(43210);var o=r(24224),s=r(4780);let a=(0,o.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500/10 text-green-600 hover:bg-green-500/20 dark:text-green-400",warning:"border-transparent bg-yellow-500/10 text-yellow-600 hover:bg-yellow-500/20 dark:text-yellow-400",info:"border-transparent bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 dark:text-blue-400",active:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",inactive:"border-transparent bg-muted text-muted-foreground",pending:"border-transparent bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",approved:"border-transparent bg-green-500/10 text-green-600 dark:text-green-400",rejected:"border-transparent bg-red-500/10 text-red-600 dark:text-red-400",draft:"border-transparent bg-muted text-muted-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i({className:e,variant:t,size:r,icon:o,removable:i,onRemove:l,children:d,...c}){return(0,n.jsxs)("div",{className:(0,s.cn)(a({variant:t,size:r}),e),...c,children:[o&&(0,n.jsx)("span",{className:"mr-1",children:o}),d,i&&(0,n.jsx)("button",{type:"button",className:"ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10",onClick:l,children:(0,n.jsx)("svg",{className:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}}};