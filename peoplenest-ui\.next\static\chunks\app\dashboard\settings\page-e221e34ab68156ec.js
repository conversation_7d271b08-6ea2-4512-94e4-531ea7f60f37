(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4631],{4229:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},18749:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>X});var t=a(95155),i=a(12115),c=a(17859),n=a(71007),l=a(23861),r=a(75525),d=a(19946);let o=(0,d.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]);var m=a(48136),h=a(19302);let x=(0,d.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var p=a(53904),u=a(4229),y=a(29869),f=a(62525),j=a(28883);let N=(0,d.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);var v=a(57434),g=a(69074),k=a(69803),w=a(78749),b=a(92657),A=a(30285),M=a(62523),C=a(66695),E=a(26126),S=a(25409),P=a(91394),z=a(85185),R=a(6101),T=a(46081),H=a(5845),D=a(45503),L=a(11275),q=a(63655),B="Switch",[Z,_]=(0,T.A)(B),[F,V]=Z(B),W=i.forwardRef((e,s)=>{let{__scopeSwitch:a,name:c,checked:n,defaultChecked:l,required:r,disabled:d,value:o="on",onCheckedChange:m,form:h,...x}=e,[p,u]=i.useState(null),y=(0,R.s)(s,e=>u(e)),f=i.useRef(!1),j=!p||h||!!p.closest("form"),[N,v]=(0,H.i)({prop:n,defaultProp:null!=l&&l,onChange:m,caller:B});return(0,t.jsxs)(F,{scope:a,checked:N,disabled:d,children:[(0,t.jsx)(q.sG.button,{type:"button",role:"switch","aria-checked":N,"aria-required":r,"data-state":O(N),"data-disabled":d?"":void 0,disabled:d,value:o,...x,ref:y,onClick:(0,z.m)(e.onClick,e=>{v(e=>!e),j&&(f.current=e.isPropagationStopped(),f.current||e.stopPropagation())})}),j&&(0,t.jsx)($,{control:p,bubbles:!f.current,name:c,value:o,checked:N,required:r,disabled:d,form:h,style:{transform:"translateX(-100%)"}})]})});W.displayName=B;var I="SwitchThumb",U=i.forwardRef((e,s)=>{let{__scopeSwitch:a,...i}=e,c=V(I,a);return(0,t.jsx)(q.sG.span,{"data-state":O(c.checked),"data-disabled":c.disabled?"":void 0,...i,ref:s})});U.displayName=I;var $=i.forwardRef((e,s)=>{let{__scopeSwitch:a,control:c,checked:n,bubbles:l=!0,...r}=e,d=i.useRef(null),o=(0,R.s)(d,s),m=(0,D.Z)(n),h=(0,L.X)(c);return i.useEffect(()=>{let e=d.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==n&&s){let a=new Event("click",{bubbles:l});s.call(e,n),e.dispatchEvent(a)}},[m,n,l]),(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...r,tabIndex:-1,ref:o,style:{...r.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function O(e){return e?"checked":"unchecked"}$.displayName="SwitchBubbleInput";var Y=a(59434);let G=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,t.jsx)(W,{className:(0,Y.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...i,ref:s,children:(0,t.jsx)(U,{className:(0,Y.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});G.displayName=W.displayName;let J={profile:{firstName:"John",lastName:"Doe",email:"<EMAIL>",phone:"+****************",department:"Engineering",position:"Senior Software Engineer",avatar:"/avatars/john.jpg",timezone:"America/New_York",language:"English"},notifications:{emailNotifications:!0,pushNotifications:!0,smsNotifications:!1,weeklyReports:!0,systemAlerts:!0,leaveApprovals:!0,payrollUpdates:!1,announcementUpdates:!0},security:{twoFactorEnabled:!0,sessionTimeout:30,passwordLastChanged:"2024-01-15",loginHistory:!0,deviceManagement:!0},appearance:{theme:"system",compactMode:!1,showAvatars:!0,animationsEnabled:!0,sidebarCollapsed:!1}},K={general:{companyName:"PeopleNest Inc.",companyEmail:"<EMAIL>",companyPhone:"+****************",address:"123 Business Ave, Suite 100, City, State 12345",website:"https://www.peoplenest.com",timezone:"America/New_York",fiscalYearStart:"January"},hrPolicies:{workingHours:"9:00 AM - 5:00 PM",workingDays:"Monday - Friday",leavePolicyEnabled:!0,overtimeEnabled:!0,remoteWorkEnabled:!0,flexibleHoursEnabled:!0},payroll:{payFrequency:"Bi-weekly",payrollCurrency:"USD",taxCalculationEnabled:!0,benefitsEnabled:!0,bonusEnabled:!0}};function X(){let[e,s]=(0,i.useState)("profile"),[a,d]=(0,i.useState)(!1),[z,R]=(0,i.useState)(J),[T,H]=(0,i.useState)(K),D=[{id:"profile",label:"Profile",icon:n.A},{id:"notifications",label:"Notifications",icon:l.A},{id:"security",label:"Security",icon:r.A},{id:"appearance",label:"Appearance",icon:o},{id:"company",label:"Company",icon:m.A},{id:"organization",label:"Organization",icon:h.A},{id:"system",label:"System",icon:x}],L=(e,s)=>{R(a=>({...a,notifications:{...a.notifications,[e]:s}}))},q=(e,s)=>{R(a=>({...a,appearance:{...a.appearance,[e]:s}}))};return(0,t.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,t.jsx)(S.Y,{title:"Settings",subtitle:"Manage your account, preferences, and system configuration",actions:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(A.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Reset to Default"]}),(0,t.jsxs)(A.$,{size:"sm",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Save Changes"]})]})}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,t.jsx)("div",{className:"lg:w-64",children:(0,t.jsx)(C.Zp,{children:(0,t.jsx)(C.Wu,{className:"p-4",children:(0,t.jsx)("nav",{className:"space-y-2",children:D.map(a=>{let i=a.icon;return(0,t.jsxs)("button",{onClick:()=>s(a.id),className:"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ".concat(e===a.id?"bg-primary text-primary-foreground":"hover:bg-muted text-muted-foreground"),children:[(0,t.jsx)(i,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:a.label})]},a.id)})})})})}),(0,t.jsxs)("div",{className:"flex-1",children:["profile"===e&&(0,t.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(C.Zp,{children:[(0,t.jsxs)(C.aR,{children:[(0,t.jsxs)(C.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Profile Information"})]}),(0,t.jsx)(C.BT,{children:"Update your personal information and contact details"})]}),(0,t.jsxs)(C.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,t.jsxs)(P.eu,{className:"h-20 w-20",children:[(0,t.jsx)(P.BK,{src:z.profile.avatar,alt:"Profile"}),(0,t.jsxs)(P.q5,{className:"text-lg",children:[z.profile.firstName[0],z.profile.lastName[0]]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(A.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Change Photo"]}),(0,t.jsxs)(A.$,{variant:"ghost",size:"sm",className:"text-destructive",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Remove Photo"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"First Name"}),(0,t.jsx)(M.p,{value:z.profile.firstName})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Last Name"}),(0,t.jsx)(M.p,{value:z.profile.lastName})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,t.jsx)(M.p,{value:z.profile.email,type:"email"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Phone"}),(0,t.jsx)(M.p,{value:z.profile.phone})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Department"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,t.jsx)("option",{value:"Product",children:"Product"}),(0,t.jsx)("option",{value:"Design",children:"Design"}),(0,t.jsx)("option",{value:"Sales",children:"Sales"}),(0,t.jsx)("option",{value:"Marketing",children:"Marketing"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Position"}),(0,t.jsx)(M.p,{value:z.profile.position})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Timezone"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"America/New_York",children:"Eastern Time"}),(0,t.jsx)("option",{value:"America/Chicago",children:"Central Time"}),(0,t.jsx)("option",{value:"America/Denver",children:"Mountain Time"}),(0,t.jsx)("option",{value:"America/Los_Angeles",children:"Pacific Time"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Language"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"English",children:"English"}),(0,t.jsx)("option",{value:"Spanish",children:"Spanish"}),(0,t.jsx)("option",{value:"French",children:"French"}),(0,t.jsx)("option",{value:"German",children:"German"})]})]})]})]})]})}),"notifications"===e&&(0,t.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(C.Zp,{children:[(0,t.jsxs)(C.aR,{children:[(0,t.jsxs)(C.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(l.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Notification Preferences"})]}),(0,t.jsx)(C.BT,{children:"Choose how you want to be notified about important updates"})]}),(0,t.jsx)(C.Wu,{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Email Notifications"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications via email"})]}),(0,t.jsx)(G,{checked:z.notifications.emailNotifications,onCheckedChange:e=>L("emailNotifications",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Push Notifications"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive browser push notifications"})]}),(0,t.jsx)(G,{checked:z.notifications.pushNotifications,onCheckedChange:e=>L("pushNotifications",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"SMS Notifications"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications via text message"})]}),(0,t.jsx)(G,{checked:z.notifications.smsNotifications,onCheckedChange:e=>L("smsNotifications",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Weekly Reports"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive weekly summary reports"})]}),(0,t.jsx)(G,{checked:z.notifications.weeklyReports,onCheckedChange:e=>L("weeklyReports",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(r.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"System Alerts"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Important system and security alerts"})]}),(0,t.jsx)(G,{checked:z.notifications.systemAlerts,onCheckedChange:e=>L("systemAlerts",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Leave Approvals"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Notifications for leave requests and approvals"})]}),(0,t.jsx)(G,{checked:z.notifications.leaveApprovals,onCheckedChange:e=>L("leaveApprovals",e)})]})]})})]})}),"security"===e&&(0,t.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(C.Zp,{children:[(0,t.jsxs)(C.aR,{children:[(0,t.jsxs)(C.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(r.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Security Settings"})]}),(0,t.jsx)(C.BT,{children:"Manage your account security and privacy settings"})]}),(0,t.jsx)(C.Wu,{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Two-Factor Authentication"}),(0,t.jsx)(E.E,{variant:"success",className:"text-xs",children:"Enabled"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add an extra layer of security to your account"})]}),(0,t.jsx)(G,{checked:z.security.twoFactorEnabled})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Change Password"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(M.p,{type:a?"text":"password",placeholder:"Current password"}),(0,t.jsx)(A.$,{variant:"ghost",size:"icon",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6",onClick:()=>d(!a),children:a?(0,t.jsx)(w.A,{className:"h-4 w-4"}):(0,t.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,t.jsx)(M.p,{type:"password",placeholder:"New password"}),(0,t.jsx)(M.p,{type:"password",placeholder:"Confirm new password"}),(0,t.jsx)(A.$,{size:"sm",children:"Update Password"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Session Timeout"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"15",children:"15 minutes"}),(0,t.jsx)("option",{value:"30",children:"30 minutes"}),(0,t.jsx)("option",{value:"60",children:"1 hour"}),(0,t.jsx)("option",{value:"240",children:"4 hours"}),(0,t.jsx)("option",{value:"480",children:"8 hours"})]})]}),(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Password Last Changed"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:new Date(z.security.passwordLastChanged).toLocaleDateString()})]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Login History"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Keep track of account access"})]}),(0,t.jsx)(G,{checked:z.security.loginHistory})]})]})})]})}),"appearance"===e&&(0,t.jsx)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(C.Zp,{children:[(0,t.jsxs)(C.aR,{children:[(0,t.jsxs)(C.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(o,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Appearance Settings"})]}),(0,t.jsx)(C.BT,{children:"Customize the look and feel of your interface"})]}),(0,t.jsx)(C.Wu,{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Theme"}),(0,t.jsxs)("select",{value:z.appearance.theme,onChange:e=>q("theme",e.target.value),className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"light",children:"Light"}),(0,t.jsx)("option",{value:"dark",children:"Dark"}),(0,t.jsx)("option",{value:"system",children:"System"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Compact Mode"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Use smaller spacing and elements"})]}),(0,t.jsx)(G,{checked:z.appearance.compactMode,onCheckedChange:e=>q("compactMode",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Show Avatars"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Display user profile pictures"})]}),(0,t.jsx)(G,{checked:z.appearance.showAvatars,onCheckedChange:e=>q("showAvatars",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Animations"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable smooth transitions and animations"})]}),(0,t.jsx)(G,{checked:z.appearance.animationsEnabled,onCheckedChange:e=>q("animationsEnabled",e)})]})]})})]})})]})]})]})}},19302:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]])},28883:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},29869:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},47931:(e,s,a)=>{Promise.resolve().then(a.bind(a,18749))},48136:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},57434:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},62525:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},69074:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},69803:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},78749:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},92657:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[3706,9380,6110,1647,1712,5409,8441,1684,7358],()=>s(47931)),_N_E=e.O()}]);