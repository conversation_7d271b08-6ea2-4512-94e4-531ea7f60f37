{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/analytics", "regex": "^/dashboard/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/analytics(?:/)?$"}, {"page": "/dashboard/announcements", "regex": "^/dashboard/announcements(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/announcements(?:/)?$"}, {"page": "/dashboard/attendance", "regex": "^/dashboard/attendance(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/attendance(?:/)?$"}, {"page": "/dashboard/compliance", "regex": "^/dashboard/compliance(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/compliance(?:/)?$"}, {"page": "/dashboard/documents", "regex": "^/dashboard/documents(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/documents(?:/)?$"}, {"page": "/dashboard/employees", "regex": "^/dashboard/employees(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/employees(?:/)?$"}, {"page": "/dashboard/help", "regex": "^/dashboard/help(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/help(?:/)?$"}, {"page": "/dashboard/leave", "regex": "^/dashboard/leave(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/leave(?:/)?$"}, {"page": "/dashboard/onboarding", "regex": "^/dashboard/onboarding(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/onboarding(?:/)?$"}, {"page": "/dashboard/organization/chart", "regex": "^/dashboard/organization/chart(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/organization/chart(?:/)?$"}, {"page": "/dashboard/organization/departments", "regex": "^/dashboard/organization/departments(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/organization/departments(?:/)?$"}, {"page": "/dashboard/organization/positions", "regex": "^/dashboard/organization/positions(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/organization/positions(?:/)?$"}, {"page": "/dashboard/payroll", "regex": "^/dashboard/payroll(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/payroll(?:/)?$"}, {"page": "/dashboard/performance", "regex": "^/dashboard/performance(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/performance(?:/)?$"}, {"page": "/dashboard/recruitment", "regex": "^/dashboard/recruitment(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/recruitment(?:/)?$"}, {"page": "/dashboard/settings", "regex": "^/dashboard/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/settings(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}