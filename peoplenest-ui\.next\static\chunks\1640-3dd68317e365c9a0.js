"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1640],{1243:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4516:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13717:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},33109:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},40646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},46561:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]])},54861:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},64683:(e,t,r)=>{r.d(t,{s:()=>I});var n=r(12115),a=r(47650),i=r(15679),l=r(52596),c=r(20241),o=r.n(c),u=r(72790),h=r(9795),p=r(43597);function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class f extends n.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,a=32/6,i=32/3,l=e.inactive?r:e.color,c=null!=t?t:e.type;if("none"===c)return null;if("plainline"===c)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:l,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===c)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:l,d:"M0,".concat(16,"h").concat(i,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(2*i,",").concat(16,"\n            H").concat(32,"M").concat(2*i,",").concat(16,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(i,",").concat(16),className:"recharts-legend-icon"});if("rect"===c)return n.createElement("path",{stroke:"none",fill:l,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(e.legendIcon)){var o=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete o.legendIcon,n.cloneElement(e.legendIcon,o)}return n.createElement(h.i,{fill:l,cx:16,cy:16,size:32,sizeType:"diameter",type:c})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:a,inactiveColor:i,iconType:c,itemSorter:h}=this.props,s={x:0,y:0,width:32,height:32},y={display:"horizontal"===r?"inline-block":"block",marginRight:10},f={display:"inline-block",verticalAlign:"middle",marginRight:4};return(h?o()(e,h):e).map((e,r)=>{var o=e.formatter||a,h=(0,l.$)({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var g=e.inactive?i:e.color,m=o?o(e.value,e,r):e.value;return n.createElement("li",d({className:h,style:y,key:"legend-item-".concat(r)},(0,p.XC)(this.props,e,r)),n.createElement(u.u,{width:t,height:t,viewBox:s,style:f,"aria-label":"".concat(m," legend icon")},this.renderIcon(e,c)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:g}},m))})}render(){var{payload:e,layout:t,align:r}=this.props;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?r:"left"}},this.renderItems()):null}}y(f,"displayName","Legend"),y(f,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",itemSorter:"value",layout:"horizontal",verticalAlign:"middle"});var g=r(16377),m=r(2494),v=r(81971),b=r(35803),O=r(77918),k=r(97238),w=r(32634),j=["contextPayload"];function P(){return(P=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?A(Object(r),!0).forEach(function(t){x(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function M(e){return e.value}function z(e){var{contextPayload:t}=e,r=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,j),a=(0,m.s)(t,e.payloadUniqBy,M),i=E(E({},r),{},{payload:a});return n.isValidElement(e.content)?n.cloneElement(e.content,i):"function"==typeof e.content?n.createElement(e.content,i):n.createElement(f,i)}function N(e){var t=(0,v.j)();return(0,n.useEffect)(()=>{t((0,w.h1)(e))},[t,e]),null}function S(e){var t=(0,v.j)();return(0,n.useEffect)(()=>(t((0,w.hx)(e)),()=>{t((0,w.hx)({width:0,height:0}))}),[t,e]),null}function D(e){var t=(0,v.G)(b.g0),r=(0,i.M)(),l=(0,k.Kp)(),{width:c,height:o,wrapperStyle:u,portal:h}=e,[p,d]=(0,O.V)([t]),s=(0,k.yi)(),y=(0,k.rY)(),f=s-(l.left||0)-(l.right||0),g=I.getWidthOrHeight(e.layout,o,c,f),m=h?u:E(E({position:"absolute",width:(null==g?void 0:g.width)||c||"auto",height:(null==g?void 0:g.height)||o||"auto"},function(e,t,r,n,a,i){var l,c,{layout:o,align:u,verticalAlign:h}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(l="center"===u&&"vertical"===o?{left:((n||0)-i.width)/2}:"right"===u?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(c="middle"===h?{top:((a||0)-i.height)/2}:"bottom"===h?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),E(E({},l),c)}(u,e,l,s,y,p)),u),w=null!=h?h:r;if(null==w)return null;var j=n.createElement("div",{className:"recharts-legend-wrapper",style:m,ref:d},n.createElement(N,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign}),n.createElement(S,{width:p.width,height:p.height}),n.createElement(z,P({},e,g,{margin:l,chartWidth:s,chartHeight:y,contextPayload:t})));return(0,a.createPortal)(j,w)}class I extends n.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&(0,g.Et)(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return n.createElement(D,this.props)}}x(I,"displayName","Legend"),x(I,"defaultProps",{align:"center",iconSize:14,layout:"horizontal",verticalAlign:"bottom"})},67312:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]])},69074:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},70306:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]])},92657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);