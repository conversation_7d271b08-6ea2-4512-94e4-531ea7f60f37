"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import {
  Briefcase,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  DollarSign,
  TrendingUp,
  Eye,
  Download,
  Building
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Header } from "@/components/layout/header"
import { PullToRefresh } from "@/components/ui/pull-to-refresh"
import { positionApi, handleApiResponse, withLoading, type Position, type PositionAnalytics } from "@/lib/api/positionApi"
import { PositionForm } from "@/components/forms/position-form"
import { PermissionGuard, usePermissions } from "@/components/auth/permission-guard"
import { RouteGuard } from "@/components/auth/route-guard"

// Loading and error states
interface PositionsState {
  positions: Position[]
  analytics: PositionAnalytics | null
  loading: boolean
  error: string | null
}
const levelColors = {
  entry: "bg-green-500/10 text-green-600 dark:text-green-400",
  junior: "bg-blue-500/10 text-blue-600 dark:text-blue-400",
  mid: "bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",
  senior: "bg-orange-500/10 text-orange-600 dark:text-orange-400",
  lead: "bg-purple-500/10 text-purple-600 dark:text-purple-400",
  manager: "bg-red-500/10 text-red-600 dark:text-red-400",
  director: "bg-muted text-muted-foreground",
  vp: "bg-indigo-500/10 text-indigo-600 dark:text-indigo-400",
  c_level: "bg-foreground text-background"
}

export default function PositionsPage() {
  const permissions = usePermissions()
  const [state, setState] = useState<PositionsState>({
    positions: [],
    analytics: null,
    loading: true,
    error: null
  })
  const [searchTerm, setSearchTerm] = useState("")
  const [showPositionForm, setShowPositionForm] = useState(false)
  const [editingPosition, setEditingPosition] = useState<Position | null>(null)
  const [selectedPosition, setSelectedPosition] = useState<Position | null>(null)
  const [showPositionDetails, setShowPositionDetails] = useState(false)

  // Load positions and analytics on component mount
  useEffect(() => {
    loadPositions()
    loadAnalytics()
  }, [])

  const loadPositions = async () => {
    await withLoading(async () => {
      const response = await positionApi.getPositions({
        includeInactive: true,
        sortBy: 'title',
        sortOrder: 'asc'
      })

      const positions = handleApiResponse(response, undefined, true)
      if (positions) {
        setState(prev => ({
          ...prev,
          positions: positions.positions,
          loading: false,
          error: null
        }))
      } else {
        setState(prev => ({
          ...prev,
          loading: false,
          error: 'Failed to load positions'
        }))
      }
    }, (loading) => setState(prev => ({ ...prev, loading })))
  }

  const loadAnalytics = async () => {
    const response = await positionApi.getPositionAnalytics()
    const analytics = handleApiResponse(response, undefined, false)
    if (analytics) {
      setState(prev => ({ ...prev, analytics }))
    }
  }

  const filteredPositions = state.positions.filter(position =>
    position.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (position.departmentName && position.departmentName.toLowerCase().includes(searchTerm.toLowerCase())) ||
    position.level.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleRefresh = async () => {
    await loadPositions()
    await loadAnalytics()
  }

  const handleAddPosition = () => {
    setEditingPosition(null)
    setShowPositionForm(true)
  }

  const handleEditPosition = (position: Position) => {
    setEditingPosition(position)
    setShowPositionForm(true)
  }

  const handleViewPosition = (position: Position) => {
    setSelectedPosition(position)
    setShowPositionDetails(true)
  }

  const handleDeletePosition = async (positionId: string) => {
    if (confirm('Are you sure you want to delete this position?')) {
      await withLoading(async () => {
        const response = await positionApi.deletePosition(positionId)
        const result = handleApiResponse(response, 'Position deleted successfully')
        if (result !== null) {
          await loadPositions()
          await loadAnalytics()
        }
      }, (loading) => setState(prev => ({ ...prev, loading })))
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatLevel = (level: string) => {
    return level.split('_').map((word: string) =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  return (
    <RouteGuard permissions={['position_read', 'hr', 'hr_admin', 'super_admin']}>
      <PullToRefresh onRefresh={handleRefresh} className="flex-1">
        <div className="space-y-6 p-6">
        <Header
          title="Position Management"
          subtitle={`Managing ${state.analytics?.totalPositions || 0} positions with ${state.analytics?.totalEmployees || 0} employees`}
          actions={
            <div className="flex items-center space-x-2">
              <PermissionGuard permissions={['position_read', 'hr', 'hr_admin', 'super_admin']}>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </PermissionGuard>
              <PermissionGuard permissions={['position_write', 'hr_admin', 'super_admin']}>
                <Button size="sm" onClick={handleAddPosition}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Position
                </Button>
              </PermissionGuard>
            </div>
          }
        />

        {/* Loading State */}
        {state.loading && (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-sm text-muted-foreground">Loading positions...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {state.error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
            <p className="text-destructive">{state.error}</p>
            <Button variant="outline" size="sm" onClick={handleRefresh} className="mt-2">
              Try Again
            </Button>
          </div>
        )}

        {/* Stats Cards */}
        {!state.loading && !state.error && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Positions</p>
                    <p className="text-2xl font-bold">{state.analytics?.totalPositions || 0}</p>
                  </div>
                  <Briefcase className="h-8 w-8 text-primary" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Active Positions</p>
                    <p className="text-2xl font-bold">{state.analytics?.activePositions || 0}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Employees</p>
                    <p className="text-2xl font-bold">{state.analytics?.totalEmployees || 0}</p>
                  </div>
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Remote Positions</p>
                    <p className="text-2xl font-bold">{state.analytics?.remotePositions || 0}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search positions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Positions Table */}
        <Card>
          <CardHeader>
            <CardTitle>Positions</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Position</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Level</TableHead>
                  <TableHead>Employees</TableHead>
                  <TableHead>Salary Range</TableHead>
                  <TableHead>Remote</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPositions.map((position) => (
                  <TableRow key={position.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{position.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {position.description || 'No description'}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Building className="h-4 w-4 mr-2 text-muted-foreground" />
                        {position.departmentName || 'No department'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={levelColors[position.level] || "bg-muted text-muted-foreground"}>
                        {formatLevel(position.level)}
                      </Badge>
                    </TableCell>
                    <TableCell>{position.employeeCount || 0}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {position.minSalary && position.maxSalary
                          ? `${formatCurrency(position.minSalary)} - ${formatCurrency(position.maxSalary)}`
                          : 'Not set'
                        }
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={position.remoteEligible ? "default" : "secondary"}>
                        {position.remoteEligible ? "Yes" : "No"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={position.isActive ? "default" : "secondary"}>
                        {position.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewPosition(position)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          {permissions.canWritePositions() && (
                            <DropdownMenuItem onClick={() => handleEditPosition(position)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                          )}
                          {permissions.canWritePositions() && (
                            <DropdownMenuItem
                              onClick={() => handleDeletePosition(position.id)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* Position Form Dialog */}
      <Dialog open={showPositionForm} onOpenChange={setShowPositionForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingPosition ? "Edit Position" : "Create New Position"}
            </DialogTitle>
          </DialogHeader>
          <PositionForm
            position={editingPosition}
            onSuccess={(position) => {
              setShowPositionForm(false)
              setEditingPosition(null)
              loadPositions()
              loadAnalytics()
            }}
            onCancel={() => {
              setShowPositionForm(false)
              setEditingPosition(null)
            }}
            className="border-0 shadow-none"
          />
        </DialogContent>
      </Dialog>

      {/* Position Details Modal - Placeholder */}
      <Dialog open={showPositionDetails} onOpenChange={setShowPositionDetails}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Position Details</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <p className="text-muted-foreground">Position details view will be implemented here.</p>
          </div>
        </DialogContent>
      </Dialog>
      </PullToRefresh>
    </RouteGuard>
  )
}
