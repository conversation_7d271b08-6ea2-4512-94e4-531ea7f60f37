(()=>{var e={};e.id=930,e.ids=[930],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7188:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=t(65239),i=t(48088),a=t(88170),n=t.n(a),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["dashboard",{children:["organization",{children:["positions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,7738)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\positions\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\positions\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/organization/positions/page",pathname:"/dashboard/organization/positions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7738:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\organization\\\\positions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\organization\\positions\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64537:(e,s,t)=>{Promise.resolve().then(t.bind(t,7738))},77689:(e,s,t)=>{Promise.resolve().then(t.bind(t,81339))},79551:e=>{"use strict";e.exports=require("url")},81339:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eN});var r=t(60687),i=t(43210),a=t(31158),n=t(96474),l=t(57800),o=t(25541),d=t(41312),c=t(23928),m=t(99270),u=t(80462),x=t(79410),h=t(93661),p=t(13861),j=t(63143),v=t(88233),g=t(29523),y=t(89667),f=t(44493),b=t(96834),N=t(6211),w=t(21342),P=t(63503),C=t(96724),k=t(35224),S=t(52581),A=t(63523);let R=t(66420).i3.API_BASE_URL;class E{async makeRequest(e,s={}){try{let t=(0,A.Pt)(),r=await fetch(`${R}${e}`,{headers:{"Content-Type":"application/json",...t&&{Authorization:`Bearer ${t}`},...s.headers},...s});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||`HTTP error! status: ${r.status}`)}return{data:await r.json()}}catch(e){return console.error("API request failed:",e),{error:e instanceof Error?e.message:"An unexpected error occurred"}}}async getPositions(e={}){let s=new URLSearchParams;Object.entries(e).forEach(([e,t])=>{null!=t&&""!==t&&s.append(e,t.toString())});let t=s.toString(),r=`/positions${t?`?${t}`:""}`;return this.makeRequest(r)}async getPositionById(e){return this.makeRequest(`/positions/${e}`)}async createPosition(e){return this.makeRequest("/positions",{method:"POST",body:JSON.stringify(e)})}async updatePosition(e,s){return this.makeRequest(`/positions/${e}`,{method:"PUT",body:JSON.stringify(s)})}async deletePosition(e){return this.makeRequest(`/positions/${e}`,{method:"DELETE"})}async getPositionEmployees(e){return this.makeRequest(`/positions/${e}/employees`)}async getPositionAnalytics(){return this.makeRequest("/positions/analytics")}async searchPositions(e){return this.getPositions({search:e,limit:50}).then(e=>({...e,data:e.data?.positions||[]}))}async getActivePositions(){return this.getPositions({includeInactive:!1}).then(e=>({...e,data:e.data?.positions||[]}))}async getPositionsByDepartment(e){return this.getPositions({departmentId:e}).then(e=>({...e,data:e.data?.positions||[]}))}async getPositionsByLevel(e){return this.getPositions({level:e}).then(e=>({...e,data:e.data?.positions||[]}))}async getRemotePositions(){return this.getPositions({remoteEligible:!0}).then(e=>({...e,data:e.data?.positions||[]}))}}let q=new E,_=(e,s,t=!0)=>e.error?(t&&S.oR.error(e.error),null):(s&&e.data&&S.oR.success(s),e.data||null),z=async(e,s)=>{try{return s(!0),await e()}catch(e){return console.error("Operation failed:",e),S.oR.error(e instanceof Error?e.message:"Operation failed"),null}finally{s(!1)}};var I=t(27605),$=t(57335),L=t(9275),M=t(34729),B=t(98599),V=t(11273),D=t(70569),T=t(65551),O=t(83721),J=t(18853),G=t(46059),Y=t(14163),F="Checkbox",[U,Z]=(0,V.A)(F),[W,K]=U(F);function H(e){let{__scopeCheckbox:s,checked:t,children:a,defaultChecked:n,disabled:l,form:o,name:d,onCheckedChange:c,required:m,value:u="on",internal_do_not_use_render:x}=e,[h,p]=(0,T.i)({prop:t,defaultProp:n??!1,onChange:c,caller:F}),[j,v]=i.useState(null),[g,y]=i.useState(null),f=i.useRef(!1),b=!j||!!o||!!j.closest("form"),N={checked:h,disabled:l,setChecked:p,control:j,setControl:v,name:d,form:o,value:u,hasConsumerStoppedPropagationRef:f,required:m,defaultChecked:!ea(n)&&n,isFormControl:b,bubbleInput:g,setBubbleInput:y};return(0,r.jsx)(W,{scope:s,...N,children:"function"==typeof x?x(N):a})}var X="CheckboxTrigger",Q=i.forwardRef(({__scopeCheckbox:e,onKeyDown:s,onClick:t,...a},n)=>{let{control:l,value:o,disabled:d,checked:c,required:m,setControl:u,setChecked:x,hasConsumerStoppedPropagationRef:h,isFormControl:p,bubbleInput:j}=K(X,e),v=(0,B.s)(n,u),g=i.useRef(c);return i.useEffect(()=>{let e=l?.form;if(e){let s=()=>x(g.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[l,x]),(0,r.jsx)(Y.sG.button,{type:"button",role:"checkbox","aria-checked":ea(c)?"mixed":c,"aria-required":m,"data-state":en(c),"data-disabled":d?"":void 0,disabled:d,value:o,...a,ref:v,onKeyDown:(0,D.m)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,D.m)(t,e=>{x(e=>!!ea(e)||!e),j&&p&&(h.current=e.isPropagationStopped(),h.current||e.stopPropagation())})})});Q.displayName=X;var ee=i.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:i,checked:a,defaultChecked:n,required:l,disabled:o,value:d,onCheckedChange:c,form:m,...u}=e;return(0,r.jsx)(H,{__scopeCheckbox:t,checked:a,defaultChecked:n,disabled:o,required:l,onCheckedChange:c,name:i,form:m,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Q,{...u,ref:s,__scopeCheckbox:t}),e&&(0,r.jsx)(ei,{__scopeCheckbox:t})]})})});ee.displayName=F;var es="CheckboxIndicator",et=i.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:i,...a}=e,n=K(es,t);return(0,r.jsx)(G.C,{present:i||ea(n.checked)||!0===n.checked,children:(0,r.jsx)(Y.sG.span,{"data-state":en(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:s,style:{pointerEvents:"none",...e.style}})})});et.displayName=es;var er="CheckboxBubbleInput",ei=i.forwardRef(({__scopeCheckbox:e,...s},t)=>{let{control:a,hasConsumerStoppedPropagationRef:n,checked:l,defaultChecked:o,required:d,disabled:c,name:m,value:u,form:x,bubbleInput:h,setBubbleInput:p}=K(er,e),j=(0,B.s)(t,p),v=(0,O.Z)(l),g=(0,J.X)(a);i.useEffect(()=>{if(!h)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!n.current;if(v!==l&&e){let t=new Event("click",{bubbles:s});h.indeterminate=ea(l),e.call(h,!ea(l)&&l),h.dispatchEvent(t)}},[h,v,l,n]);let y=i.useRef(!ea(l)&&l);return(0,r.jsx)(Y.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:o??y.current,required:d,disabled:c,name:m,value:u,form:x,...s,tabIndex:-1,ref:j,style:{...s.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function ea(e){return"indeterminate"===e}function en(e){return ea(e)?"indeterminate":e?"checked":"unchecked"}ei.displayName=er;var el=t(13964),eo=t(4780);let ed=i.forwardRef(({className:e,...s},t)=>(0,r.jsx)(ee,{ref:t,className:(0,eo.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:(0,r.jsx)(et,{className:(0,eo.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(el.A,{className:"h-4 w-4"})})}));ed.displayName=ee.displayName;var ec=t(71669),em=t(15079),eu=t(11860),ex=t(41862),eh=t(8819);t(91956);let ep=L.Ik({title:L.Yj().min(2,"Position title must be at least 2 characters").max(100,"Position title must be less than 100 characters"),description:L.Yj().optional(),departmentId:L.Yj().optional(),level:L.k5(["entry","junior","mid","senior","lead","manager","director","vp","c_level"]),type:L.k5(["full_time","part_time","contract","intern"]),minSalary:L.ai().min(0,"Minimum salary must be positive").optional(),maxSalary:L.ai().min(0,"Maximum salary must be positive").optional(),currency:L.Yj().default("USD"),requiredSkills:L.YO(L.Yj()).default([]),responsibilities:L.YO(L.Yj()).default([]),requirements:L.YO(L.Yj()).default([]),remoteEligible:L.zM().default(!1),travelRequired:L.zM().default(!1)}).refine(e=>!e.minSalary||!e.maxSalary||e.maxSalary>=e.minSalary,{message:"Maximum salary must be greater than or equal to minimum salary",path:["maxSalary"]}),ej=[{value:"entry",label:"Entry Level"},{value:"junior",label:"Junior"},{value:"mid",label:"Mid Level"},{value:"senior",label:"Senior"},{value:"lead",label:"Lead"},{value:"manager",label:"Manager"},{value:"director",label:"Director"},{value:"vp",label:"Vice President"},{value:"c_level",label:"C-Level"}],ev=[{value:"full_time",label:"Full Time"},{value:"part_time",label:"Part Time"},{value:"contract",label:"Contract"},{value:"intern",label:"Intern"}];function eg({position:e,onSuccess:s,onCancel:t,className:a}){let[l,o]=(0,i.useState)(!1),[d,c]=(0,i.useState)([]),[m,u]=(0,i.useState)(!1),[x,h]=(0,i.useState)(""),[p,j]=(0,i.useState)(""),[N,w]=(0,i.useState)(""),P=!!e,C=(0,I.mN)({resolver:(0,$.u)(ep),defaultValues:{title:e?.title||"",description:e?.description||"",departmentId:e?.departmentId||"",level:e?.level||"entry",type:e?.type||"full_time",minSalary:e?.minSalary||void 0,maxSalary:e?.maxSalary||void 0,currency:e?.currency||"USD",requiredSkills:e?.requiredSkills||[],responsibilities:e?.responsibilities||[],requirements:e?.requirements||[],remoteEligible:e?.remoteEligible||!1,travelRequired:e?.travelRequired||!1}}),k=async t=>{await z(async()=>{let r,i;if(P&&e){let s={...t,minSalary:t.minSalary||void 0,maxSalary:t.maxSalary||void 0};r=await q.updatePosition(e.id,s),i="Position updated successfully"}else{let e={...t,minSalary:t.minSalary||void 0,maxSalary:t.maxSalary||void 0};r=await q.createPosition(e),i="Position created successfully"}let a=_(r,i);a&&(s?.(a),P||(C.reset(),h(""),j(""),w("")))},o)},S=()=>{if(x.trim()){let e=C.getValues("requiredSkills");e.includes(x.trim())||(C.setValue("requiredSkills",[...e,x.trim()]),h(""))}},A=e=>{let s=C.getValues("requiredSkills");C.setValue("requiredSkills",s.filter(s=>s!==e))},R=()=>{if(p.trim()){let e=C.getValues("responsibilities");C.setValue("responsibilities",[...e,p.trim()]),j("")}},E=e=>{let s=C.getValues("responsibilities");C.setValue("responsibilities",s.filter((s,t)=>t!==e))},L=()=>{if(N.trim()){let e=C.getValues("requirements");C.setValue("requirements",[...e,N.trim()]),w("")}},B=e=>{let s=C.getValues("requirements");C.setValue("requirements",s.filter((s,t)=>t!==e))};return(0,r.jsxs)(f.Zp,{className:a,children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:P?"Edit Position":"Create New Position"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)(ec.lV,{...C,children:(0,r.jsxs)("form",{onSubmit:C.handleSubmit(k),className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(ec.zB,{control:C.control,name:"title",render:({field:e})=>(0,r.jsxs)(ec.eI,{children:[(0,r.jsx)(ec.lR,{children:"Position Title *"}),(0,r.jsx)(ec.MJ,{children:(0,r.jsx)(y.p,{placeholder:"e.g., Senior Software Engineer",...e})}),(0,r.jsx)(ec.Rr,{children:"The official job title"}),(0,r.jsx)(ec.C5,{})]})}),(0,r.jsx)(ec.zB,{control:C.control,name:"departmentId",render:({field:e})=>(0,r.jsxs)(ec.eI,{children:[(0,r.jsx)(ec.lR,{children:"Department"}),(0,r.jsxs)(em.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,r.jsx)(ec.MJ,{children:(0,r.jsx)(em.bq,{children:(0,r.jsx)(em.yv,{placeholder:"Select department"})})}),(0,r.jsxs)(em.gC,{children:[(0,r.jsx)(em.eb,{value:"",children:"No department"}),m?(0,r.jsx)(em.eb,{value:"",disabled:!0,children:"Loading departments..."}):d.map(e=>(0,r.jsxs)(em.eb,{value:e.id,children:[e.name," (",e.code,")"]},e.id))]})]}),(0,r.jsx)(ec.C5,{})]})})]}),(0,r.jsx)(ec.zB,{control:C.control,name:"description",render:({field:e})=>(0,r.jsxs)(ec.eI,{children:[(0,r.jsx)(ec.lR,{children:"Description"}),(0,r.jsx)(ec.MJ,{children:(0,r.jsx)(M.T,{placeholder:"Brief description of the position",className:"min-h-[100px]",...e})}),(0,r.jsx)(ec.C5,{})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(ec.zB,{control:C.control,name:"level",render:({field:e})=>(0,r.jsxs)(ec.eI,{children:[(0,r.jsx)(ec.lR,{children:"Level *"}),(0,r.jsxs)(em.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,r.jsx)(ec.MJ,{children:(0,r.jsx)(em.bq,{children:(0,r.jsx)(em.yv,{placeholder:"Select level"})})}),(0,r.jsx)(em.gC,{children:ej.map(e=>(0,r.jsx)(em.eb,{value:e.value,children:e.label},e.value))})]}),(0,r.jsx)(ec.C5,{})]})}),(0,r.jsx)(ec.zB,{control:C.control,name:"type",render:({field:e})=>(0,r.jsxs)(ec.eI,{children:[(0,r.jsx)(ec.lR,{children:"Employment Type *"}),(0,r.jsxs)(em.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,r.jsx)(ec.MJ,{children:(0,r.jsx)(em.bq,{children:(0,r.jsx)(em.yv,{placeholder:"Select type"})})}),(0,r.jsx)(em.gC,{children:ev.map(e=>(0,r.jsx)(em.eb,{value:e.value,children:e.label},e.value))})]}),(0,r.jsx)(ec.C5,{})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)(ec.zB,{control:C.control,name:"minSalary",render:({field:e})=>(0,r.jsxs)(ec.eI,{children:[(0,r.jsx)(ec.lR,{children:"Minimum Salary"}),(0,r.jsx)(ec.MJ,{children:(0,r.jsx)(y.p,{type:"number",placeholder:"e.g., 80000",...e,onChange:s=>e.onChange(s.target.value?parseFloat(s.target.value):void 0)})}),(0,r.jsx)(ec.C5,{})]})}),(0,r.jsx)(ec.zB,{control:C.control,name:"maxSalary",render:({field:e})=>(0,r.jsxs)(ec.eI,{children:[(0,r.jsx)(ec.lR,{children:"Maximum Salary"}),(0,r.jsx)(ec.MJ,{children:(0,r.jsx)(y.p,{type:"number",placeholder:"e.g., 120000",...e,onChange:s=>e.onChange(s.target.value?parseFloat(s.target.value):void 0)})}),(0,r.jsx)(ec.C5,{})]})}),(0,r.jsx)(ec.zB,{control:C.control,name:"currency",render:({field:e})=>(0,r.jsxs)(ec.eI,{children:[(0,r.jsx)(ec.lR,{children:"Currency"}),(0,r.jsx)(ec.MJ,{children:(0,r.jsx)(y.p,{placeholder:"USD",...e})}),(0,r.jsx)(ec.C5,{})]})})]}),(0,r.jsxs)("div",{className:"flex space-x-6",children:[(0,r.jsx)(ec.zB,{control:C.control,name:"remoteEligible",render:({field:e})=>(0,r.jsxs)(ec.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,r.jsx)(ec.MJ,{children:(0,r.jsx)(ed,{checked:e.value,onCheckedChange:e.onChange})}),(0,r.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,r.jsx)(ec.lR,{children:"Remote Eligible"}),(0,r.jsx)(ec.Rr,{children:"Position can be performed remotely"})]})]})}),(0,r.jsx)(ec.zB,{control:C.control,name:"travelRequired",render:({field:e})=>(0,r.jsxs)(ec.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,r.jsx)(ec.MJ,{children:(0,r.jsx)(ed,{checked:e.value,onCheckedChange:e.onChange})}),(0,r.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,r.jsx)(ec.lR,{children:"Travel Required"}),(0,r.jsx)(ec.Rr,{children:"Position requires business travel"})]})]})})]}),(0,r.jsx)(ec.zB,{control:C.control,name:"requiredSkills",render:({field:e})=>(0,r.jsxs)(ec.eI,{children:[(0,r.jsx)(ec.lR,{children:"Required Skills"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(y.p,{placeholder:"Add a skill",value:x,onChange:e=>h(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),S())}),(0,r.jsx)(g.$,{type:"button",onClick:S,size:"sm",children:(0,r.jsx)(n.A,{className:"w-4 h-4"})})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.value.map((e,s)=>(0,r.jsxs)(b.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,r.jsx)(g.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 text-muted-foreground hover:text-destructive",onClick:()=>A(e),children:(0,r.jsx)(eu.A,{className:"w-3 h-3"})})]},s))})]}),(0,r.jsx)(ec.C5,{})]})}),(0,r.jsx)(ec.zB,{control:C.control,name:"responsibilities",render:({field:e})=>(0,r.jsxs)(ec.eI,{children:[(0,r.jsx)(ec.lR,{children:"Key Responsibilities"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(y.p,{placeholder:"Add a responsibility",value:p,onChange:e=>j(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),R())}),(0,r.jsx)(g.$,{type:"button",onClick:R,size:"sm",children:(0,r.jsx)(n.A,{className:"w-4 h-4"})})]}),(0,r.jsx)("div",{className:"space-y-2",children:e.value.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-start space-x-2 p-2 bg-muted rounded",children:[(0,r.jsx)("span",{className:"flex-1 text-sm",children:e}),(0,r.jsx)(g.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1 text-muted-foreground hover:text-destructive",onClick:()=>E(s),children:(0,r.jsx)(v.A,{className:"w-3 h-3"})})]},s))})]}),(0,r.jsx)(ec.C5,{})]})}),(0,r.jsx)(ec.zB,{control:C.control,name:"requirements",render:({field:e})=>(0,r.jsxs)(ec.eI,{children:[(0,r.jsx)(ec.lR,{children:"Requirements"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(y.p,{placeholder:"Add a requirement",value:N,onChange:e=>w(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),L())}),(0,r.jsx)(g.$,{type:"button",onClick:L,size:"sm",children:(0,r.jsx)(n.A,{className:"w-4 h-4"})})]}),(0,r.jsx)("div",{className:"space-y-2",children:e.value.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-start space-x-2 p-2 bg-muted rounded",children:[(0,r.jsx)("span",{className:"flex-1 text-sm",children:e}),(0,r.jsx)(g.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1 text-muted-foreground hover:text-destructive",onClick:()=>B(s),children:(0,r.jsx)(v.A,{className:"w-3 h-3"})})]},s))})]}),(0,r.jsx)(ec.C5,{})]})}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 pt-6",children:[(0,r.jsxs)(g.$,{type:"button",variant:"outline",onClick:()=>{C.reset(),h(""),j(""),w(""),t?.()},disabled:l,children:[(0,r.jsx)(eu.A,{className:"w-4 h-4 mr-2"}),"Cancel"]}),(0,r.jsxs)(g.$,{type:"submit",disabled:l,children:[l?(0,r.jsx)(ex.A,{className:"w-4 h-4 mr-2 animate-spin"}):(0,r.jsx)(eh.A,{className:"w-4 h-4 mr-2"}),P?"Update Position":"Create Position"]})]})]})})})]})}var ey=t(78010),ef=t(28488);let eb={entry:"bg-green-500/10 text-green-600 dark:text-green-400",junior:"bg-blue-500/10 text-blue-600 dark:text-blue-400",mid:"bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",senior:"bg-orange-500/10 text-orange-600 dark:text-orange-400",lead:"bg-purple-500/10 text-purple-600 dark:text-purple-400",manager:"bg-red-500/10 text-red-600 dark:text-red-400",director:"bg-muted text-muted-foreground",vp:"bg-indigo-500/10 text-indigo-600 dark:text-indigo-400",c_level:"bg-foreground text-background"};function eN(){let e=(0,ey.Sk)(),[s,t]=(0,i.useState)({positions:[],analytics:null,loading:!0,error:null}),[S,A]=(0,i.useState)(""),[R,E]=(0,i.useState)(!1),[I,$]=(0,i.useState)(null),[L,M]=(0,i.useState)(null),[B,V]=(0,i.useState)(!1),D=async()=>{await z(async()=>{let e=_(await q.getPositions({includeInactive:!0,sortBy:"title",sortOrder:"asc"}),void 0,!0);e?t(s=>({...s,positions:e.positions,loading:!1,error:null})):t(e=>({...e,loading:!1,error:"Failed to load positions"}))},e=>t(s=>({...s,loading:e})))},T=async()=>{let e=_(await q.getPositionAnalytics(),void 0,!1);e&&t(s=>({...s,analytics:e}))},O=s.positions.filter(e=>e.title.toLowerCase().includes(S.toLowerCase())||e.departmentName&&e.departmentName.toLowerCase().includes(S.toLowerCase())||e.level.toLowerCase().includes(S.toLowerCase())),J=async()=>{await D(),await T()},G=e=>{$(e),E(!0)},Y=e=>{M(e),V(!0)},F=async e=>{confirm("Are you sure you want to delete this position?")&&await z(async()=>{null!==_(await q.deletePosition(e),"Position deleted successfully")&&(await D(),await T())},e=>t(s=>({...s,loading:e})))},U=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),Z=e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");return(0,r.jsx)(ef.$G,{permissions:["position_read","hr","hr_admin","super_admin"],children:(0,r.jsxs)(k.k,{onRefresh:J,className:"flex-1",children:[(0,r.jsxs)("div",{className:"space-y-6 p-6",children:[(0,r.jsx)(C.Y,{title:"Position Management",subtitle:`Managing ${s.analytics?.totalPositions||0} positions with ${s.analytics?.totalEmployees||0} employees`,actions:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(ey.LQ,{permissions:["position_read","hr","hr_admin","super_admin"],children:(0,r.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"Export"]})}),(0,r.jsx)(ey.LQ,{permissions:["position_write","hr_admin","super_admin"],children:(0,r.jsxs)(g.$,{size:"sm",onClick:()=>{$(null),E(!0)},children:[(0,r.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Add Position"]})})]})}),s.loading&&(0,r.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Loading positions..."})]})}),s.error&&(0,r.jsxs)("div",{className:"bg-destructive/10 border border-destructive/20 rounded-lg p-4",children:[(0,r.jsx)("p",{className:"text-destructive",children:s.error}),(0,r.jsx)(g.$,{variant:"outline",size:"sm",onClick:J,className:"mt-2",children:"Try Again"})]}),!s.loading&&!s.error&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(f.Zp,{children:(0,r.jsx)(f.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Positions"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:s.analytics?.totalPositions||0})]}),(0,r.jsx)(l.A,{className:"h-8 w-8 text-primary"})]})})}),(0,r.jsx)(f.Zp,{children:(0,r.jsx)(f.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Active Positions"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:s.analytics?.activePositions||0})]}),(0,r.jsx)(o.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,r.jsx)(f.Zp,{children:(0,r.jsx)(f.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Employees"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:s.analytics?.totalEmployees||0})]}),(0,r.jsx)(d.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,r.jsx)(f.Zp,{children:(0,r.jsx)(f.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Remote Positions"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:s.analytics?.remotePositions||0})]}),(0,r.jsx)(c.A,{className:"h-8 w-8 text-yellow-600"})]})})})]}),(0,r.jsx)(f.Zp,{children:(0,r.jsx)(f.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,r.jsx)(y.p,{placeholder:"Search positions...",value:S,onChange:e=>A(e.target.value),className:"pl-10"})]}),(0,r.jsxs)(g.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Filters"]})]})})}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{children:"Positions"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)(N.XI,{children:[(0,r.jsx)(N.A0,{children:(0,r.jsxs)(N.Hj,{children:[(0,r.jsx)(N.nd,{children:"Position"}),(0,r.jsx)(N.nd,{children:"Department"}),(0,r.jsx)(N.nd,{children:"Level"}),(0,r.jsx)(N.nd,{children:"Employees"}),(0,r.jsx)(N.nd,{children:"Salary Range"}),(0,r.jsx)(N.nd,{children:"Remote"}),(0,r.jsx)(N.nd,{children:"Status"}),(0,r.jsx)(N.nd,{className:"text-right",children:"Actions"})]})}),(0,r.jsx)(N.BF,{children:O.map(s=>(0,r.jsxs)(N.Hj,{children:[(0,r.jsx)(N.nA,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:s.title}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:s.description||"No description"})]})}),(0,r.jsx)(N.nA,{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2 text-muted-foreground"}),s.departmentName||"No department"]})}),(0,r.jsx)(N.nA,{children:(0,r.jsx)(b.E,{className:eb[s.level]||"bg-muted text-muted-foreground",children:Z(s.level)})}),(0,r.jsx)(N.nA,{children:s.employeeCount||0}),(0,r.jsx)(N.nA,{children:(0,r.jsx)("div",{className:"text-sm",children:s.minSalary&&s.maxSalary?`${U(s.minSalary)} - ${U(s.maxSalary)}`:"Not set"})}),(0,r.jsx)(N.nA,{children:(0,r.jsx)(b.E,{variant:s.remoteEligible?"default":"secondary",children:s.remoteEligible?"Yes":"No"})}),(0,r.jsx)(N.nA,{children:(0,r.jsx)(b.E,{variant:s.isActive?"default":"secondary",children:s.isActive?"Active":"Inactive"})}),(0,r.jsx)(N.nA,{className:"text-right",children:(0,r.jsxs)(w.rI,{children:[(0,r.jsx)(w.ty,{asChild:!0,children:(0,r.jsx)(g.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(w.SQ,{align:"end",children:[(0,r.jsxs)(w._2,{onClick:()=>Y(s),children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"View Details"]}),e.canWritePositions()&&(0,r.jsxs)(w._2,{onClick:()=>G(s),children:[(0,r.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Edit"]}),e.canWritePositions()&&(0,r.jsxs)(w._2,{onClick:()=>F(s.id),className:"text-destructive",children:[(0,r.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},s.id))})]})})]})]}),(0,r.jsx)(P.lG,{open:R,onOpenChange:E,children:(0,r.jsxs)(P.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)(P.c7,{children:(0,r.jsx)(P.L3,{children:I?"Edit Position":"Create New Position"})}),(0,r.jsx)(eg,{position:I,onSuccess:e=>{E(!1),$(null),D(),T()},onCancel:()=>{E(!1),$(null)},className:"border-0 shadow-none"})]})}),(0,r.jsx)(P.lG,{open:B,onOpenChange:V,children:(0,r.jsxs)(P.Cf,{className:"max-w-4xl",children:[(0,r.jsx)(P.c7,{children:(0,r.jsx)(P.L3,{children:"Position Details"})}),(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Position details view will be implemented here."})})]})})]})})}}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,912,18,722,632,913,780,809,456,725],()=>t(7188));module.exports=r})();