"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3706],{1335:(t,e,i)=>{i.d(e,{u:()=>n});var s=i(9064);let n={test:(0,i(55920).$)("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:s.B.transform}},4272:(t,e,i)=>{i.d(e,{y:()=>a});var s=i(1335),n=i(18476),r=i(9064);let a={test:t=>r.B.test(t)||s.u.test(t)||n.V.test(t),parse:t=>r.B.test(t)?r.B.parse(t):n.V.test(t)?n.V.parse(t):s.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?r.B.transform(t):n.V.transform(t),getAnimatableNone:t=>{let e=a.parse(t);return e.alpha=0,a.transform(e)}}},6775:(t,e,i)=>{i.d(e,{G:()=>u});var s=i(23387),n=i(19827),r=i(53191),a=i(54542),o=i(45818),l=i(53678),h=i(26087);function u(t,e,{clamp:i=!0,ease:d,mixer:c}={}){let p=t.length;if((0,a.V)(p===e.length,"Both input and output ranges must be the same length"),1===p)return()=>e[0];if(2===p&&e[0]===e[1])return()=>e[1];let m=t[0]===t[1];t[0]>t[p-1]&&(t=[...t].reverse(),e=[...e].reverse());let f=function(t,e,i){let a=[],o=i||s.W.mix||h.j,l=t.length-1;for(let i=0;i<l;i++){let s=o(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||n.l:e;s=(0,r.F)(t,s)}a.push(s)}return a}(e,d,c),v=f.length,y=i=>{if(m&&i<t[0])return e[0];let s=0;if(v>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=(0,o.q)(t[s],t[s+1],i);return f[s](n)};return i?e=>y((0,l.q)(t[0],t[p-1],e)):y}},6983:(t,e,i)=>{i.d(e,{G:()=>s});function s(t){return"object"==typeof t&&null!==t}},9064:(t,e,i)=>{i.d(e,{B:()=>h});var s=i(53678),n=i(57887),r=i(11557),a=i(55920);let o=t=>(0,s.q)(0,255,t),l={...n.ai,transform:t=>Math.round(o(t))},h={test:(0,a.$)("rgb","red"),parse:(0,a.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,r.a)(n.X4.transform(s))+")"}},11557:(t,e,i)=>{i.d(e,{a:()=>s});let s=t=>Math.round(1e5*t)/1e5},17859:(t,e,i)=>{function s(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function n(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function r(t,e,i,s){if("function"==typeof e){let[r,a]=n(s);e=e(void 0!==i?i:t.custom,r,a)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,a]=n(s);e=e(void 0!==i?i:t.custom,r,a)}return e}function a(t,e,i){let s=t.getProps();return r(s,e,void 0!==i?i:s.custom,t)}function o(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>nU});var l,h,u=i(69515);let d=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],c=new Set(d),p=new Set(["width","height","top","left","right","bottom",...d]);var m=i(60098);let f=t=>Array.isArray(t);var v=i(23387);let y=t=>!!(t&&t.getVelocity);function g(t,e){let i=t.getValue("willChange");if(y(i)&&i.add)return i.add(e);if(!i&&v.W.WillChange){let i=new v.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let x=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),w="data-"+x("framerAppearId");var T=i(53191),P=i(53678);let b=t=>1e3*t,S=t=>t/1e3;var A=i(74261);let V={layout:0,mainThread:0,waapi:0};var M=i(26087);let k=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>u.Gt.update(e,t),stop:()=>(0,u.WG)(e),now:()=>u.uv.isProcessing?u.uv.timestamp:A.k.now()}},E=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=Math.round(1e4*t(e/(n-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`};function C(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}var D=i(62923);function R(t,e,i){let s=Math.max(e-5,0);return(0,D.f)(i-t(s),e-s)}let j={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var L=i(54542);function F(t,e){return t*Math.sqrt(1-e*e)}let B=["duration","bounce"],O=["stiffness","damping","mass"];function I(t,e){return e.some(e=>void 0!==t[e])}function U(t=j.visualDuration,e=j.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:h,damping:u,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:j.velocity,stiffness:j.stiffness,damping:j.damping,mass:j.mass,isResolvedFromDuration:!1,...t};if(!I(t,O)&&I(t,B))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*(0,P.q)(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:j.mass,stiffness:s,damping:n}}else{let i=function({duration:t=j.duration,bounce:e=j.bounce,velocity:i=j.velocity,mass:s=j.mass}){let n,r;(0,L.$)(t<=b(j.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=(0,P.q)(j.minDamping,j.maxDamping,a),t=(0,P.q)(j.minDuration,j.maxDuration,S(t)),a<1?(n=e=>{let s=e*a,n=s*t;return .001-(s-i)/F(e,a)*Math.exp(-n)},r=e=>{let s=e*a*t,r=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-s),l=F(Math.pow(e,2),a);return(s*i+i-r)*o*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=b(t),isNaN(o))return{stiffness:j.stiffness,damping:j.damping,duration:t};{let e=Math.pow(o,2)*s;return{stiffness:e,damping:2*a*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:j.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-S(s.velocity||0)}),f=p||0,v=u/(2*Math.sqrt(h*d)),y=o-a,g=S(Math.sqrt(h/d)),x=5>Math.abs(y);if(n||(n=x?j.restSpeed.granular:j.restSpeed.default),r||(r=x?j.restDelta.granular:j.restDelta.default),v<1){let t=F(g,v);i=e=>o-Math.exp(-v*g*e)*((f+v*g*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===v)i=t=>o-Math.exp(-g*t)*(y+(f+g*y)*t);else{let t=g*Math.sqrt(v*v-1);i=e=>{let i=Math.exp(-v*g*e),s=Math.min(t*e,300);return o-i*((f+v*g*y)*Math.sinh(s)+t*y*Math.cosh(s))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0===t?f:0;v<1&&(s=0===t?b(f):R(i,t,e));let a=Math.abs(o-e)<=r;l.done=Math.abs(s)<=n&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(C(w),2e4),e=E(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function N({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:u}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,v=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,y=i*e,g=p+y,x=void 0===a?g:a(g);x!==g&&(y=x-p);let w=t=>-y*Math.exp(-t/s),T=t=>x+w(t),P=t=>{let e=w(t),i=T(t);m.done=Math.abs(e)<=h,m.value=m.done?x:i},b=t=>{f(m.value)&&(d=t,c=U({keyframes:[m.value,v(m.value)],velocity:R(T,t,m.value),damping:n,stiffness:r,restDelta:h,restSpeed:u}))};return b(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,P(t),b(t)),void 0!==d&&t>=d)?c.next(t-d):(e||P(t),m)}}}U.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),n=Math.min(C(s),2e4);return{type:"keyframes",ease:t=>s.next(n*t).value/e,duration:S(n)}}(t,100,U);return t.ease=e.ease,t.duration=b(e.duration),t.type="keyframes",t};var W=i(19827);let $=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function G(t,e,i,s){if(t===e&&i===s)return W.l;let n=e=>(function(t,e,i,s,n){let r,a,o=0;do(r=$(a=e+(i-e)/2,s,n)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:$(n(t),e,s)}let q=G(.42,0,1,1),X=G(0,0,.58,1),K=G(.42,0,.58,1),Y=t=>Array.isArray(t)&&"number"!=typeof t[0],z=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,H=t=>e=>1-t(1-e),Q=G(.33,1.53,.69,.99),_=H(Q),Z=z(_),J=t=>(t*=2)<1?.5*_(t):.5*(2-Math.pow(2,-10*(t-1))),tt=t=>1-Math.sin(Math.acos(t)),te=H(tt),ti=z(tt),ts=t=>Array.isArray(t)&&"number"==typeof t[0],tn={linear:W.l,easeIn:q,easeInOut:K,easeOut:X,circIn:tt,circInOut:ti,circOut:te,backIn:_,backInOut:Z,backOut:Q,anticipate:J},tr=t=>"string"==typeof t,ta=t=>{if(ts(t)){(0,L.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return G(e,i,s,n)}return tr(t)?((0,L.V)(void 0!==tn[t],`Invalid easing type '${t}'`),tn[t]):t};var to=i(6775),tl=i(45818),th=i(33210);function tu({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var n;let r=Y(s)?s.map(ta):ta(s),a={done:!1,value:e[0]},o=(n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=(0,tl.q)(0,e,s);t.push((0,th.k)(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),l=(0,to.G)(o,e,{ease:Array.isArray(r)?r:e.map(()=>r||K).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=l(e),a.done=e>=t,a)}}let td=t=>null!==t;function tc(t,{repeat:e,repeatType:i="loop"},s,n=1){let r=t.filter(td),a=n<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==s?s:r[a]}let tp={decay:N,inertia:N,tween:tu,keyframes:tu,spring:U};function tm(t){"string"==typeof t.type&&(t.type=tp[t.type])}class tf{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let tv=t=>t/100;class ty extends tf{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==A.k.now()&&this.tick(A.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},V.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;tm(t);let{type:e=tu,repeat:i=0,repeatDelay:s=0,repeatType:n,velocity:r=0}=t,{keyframes:a}=t,o=e||tu;o!==tu&&"number"!=typeof a[0]&&(this.mixKeyframes=(0,T.F)(tv,(0,M.j)(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=C(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:s,mixKeyframes:n,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:u,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let v=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?v<0:v>s;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let g=this.currentTime,x=i;if(u){let t=Math.min(this.currentTime,s)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,u+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/a)):"mirror"===d&&(x=r)),g=(0,P.q)(0,1,i)*a}let w=y?{done:!1,value:h[0]}:x.next(g);n&&(w.value=n(w.value));let{done:T}=w;y||null===o||(T=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let b=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return b&&p!==N&&(w.value=tc(h,this.options,f,this.speed)),m&&m(w.value),b&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return S(this.calculatedDuration)}get time(){return S(this.currentTime)}set time(t){t=b(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(A.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=S(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=k,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,V.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let tg=t=>180*t/Math.PI,tx=t=>tT(tg(Math.atan2(t[1],t[0]))),tw={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:tx,rotateZ:tx,skewX:t=>tg(Math.atan(t[1])),skewY:t=>tg(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},tT=t=>((t%=360)<0&&(t+=360),t),tP=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),tb=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),tS={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tP,scaleY:tb,scale:t=>(tP(t)+tb(t))/2,rotateX:t=>tT(tg(Math.atan2(t[6],t[5]))),rotateY:t=>tT(tg(Math.atan2(-t[2],t[0]))),rotateZ:tx,rotate:tx,skewX:t=>tg(Math.atan(t[4])),skewY:t=>tg(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function tA(t){return+!!t.includes("scale")}function tV(t,e){let i,s;if(!t||"none"===t)return tA(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=tS,s=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tw,s=e}if(!s)return tA(e);let r=i[e],a=s[1].split(",").map(tk);return"function"==typeof r?r(a):a[r]}let tM=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return tV(i,e)};function tk(t){return parseFloat(t.trim())}var tE=i(57887),tC=i(34158);let tD=t=>t===tE.ai||t===tC.px,tR=new Set(["x","y","z"]),tj=d.filter(t=>!tR.has(t)),tL={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>tV(e,"x"),y:(t,{transform:e})=>tV(e,"y")};tL.translateX=tL.x,tL.translateY=tL.y;let tF=new Set,tB=!1,tO=!1,tI=!1;function tU(){if(tO){let t=Array.from(tF).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return tj.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tO=!1,tB=!1,tF.forEach(t=>t.complete(tI)),tF.clear()}function tN(){tF.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tO=!0)})}class tW{constructor(t,e,i,s,n,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(tF.add(this),tB||(tB=!0,u.Gt.read(tN),u.Gt.resolveKeyframes(tU))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){let n=s?.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tF.delete(this)}cancel(){"scheduled"===this.state&&(tF.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let t$=t=>t.startsWith("--");function tG(t){let e;return()=>(void 0===e&&(e=t()),e)}let tq=tG(()=>void 0!==window.ScrollTimeline);var tX=i(24744);let tK={},tY=function(t,e){let i=tG(t);return()=>tK[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),tz=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,tH={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tz([0,.65,.55,1]),circOut:tz([.55,0,1,.45]),backIn:tz([.31,.01,.66,-.59]),backOut:tz([.33,1.53,.69,.99])};function tQ(t){return"function"==typeof t&&"applyToOptions"in t}class t_ extends tf{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:s,pseudoElement:n,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!n,this.allowFlatten=r,this.options=t,(0,L.V)("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return tQ(t)&&tY()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?tY()?E(e,i):"ease-out":ts(e)?tz(e):Array.isArray(e)?e.map(e=>t(e,i)||tH.easeOut):tH[e]}(o,n);Array.isArray(d)&&(u.easing=d),tX.Q.value&&V.waapi++;let c={delay:s,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};h&&(c.pseudoElement=h);let p=t.animate(u,c);return tX.Q.value&&p.finished.finally(()=>{V.waapi--}),p}(e,i,s,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=tc(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){t$(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return S(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return S(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=b(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&tq())?(this.animation.timeline=t,W.l):e(this)}}let tZ={anticipate:J,backInOut:Z,circInOut:ti};class tJ extends t_{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in tZ&&(t.ease=tZ[t.ease])}(t),tm(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:s,element:n,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new ty({...r,autoplay:!1}),o=b(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}var t0=i(60010);let t1=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t0.f.test(t)||"0"===t)&&!t.startsWith("url("));var t5=i(27351);let t2=new Set(["opacity","clipPath","filter","transform"]),t3=tG(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class t4 extends tf{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=A.k.now();let d={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,name:o,motionValue:l,element:h,...u},c=h?.KeyframeResolver||tW;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,s){this.keyframeResolver=void 0;let{name:n,type:r,velocity:a,delay:o,isHandoff:l,onUpdate:h}=i;this.resolvedAt=A.k.now(),!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=t1(n,e),o=t1(r,e);return(0,L.$)(a===o,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||tQ(i))&&s)}(t,n,r,a)&&((v.W.instantAnimations||!o)&&h?.(tc(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let u={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:a}=t;if(!(0,t5.s)(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return t3()&&i&&t2.has(i)&&("transform"!==i||!l)&&!o&&!s&&"mirror"!==n&&0!==r&&"inertia"!==a}(u)?new tJ({...u,element:u.motionValue.owner.current}):new ty(u);d.finished.then(()=>this.notifyFinished()).catch(W.l),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tI=!0,tN(),tU(),tI=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t6=t=>null!==t,t8={type:"spring",stiffness:500,damping:25,restSpeed:10},t9=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),t7={type:"keyframes",duration:.8},et={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ee=(t,{keyframes:e})=>e.length>2?t7:c.has(t)?t.startsWith("scale")?t9(e[1]):t8:et,ei=(t,e,i,s={},n,r)=>a=>{let l=o(s,t)||{},h=l.delay||s.delay||0,{elapsed:d=0}=s;d-=b(h);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-d,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(l)&&Object.assign(c,ee(t,c)),c.duration&&(c.duration=b(c.duration)),c.repeatDelay&&(c.repeatDelay=b(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(p=!0)),(v.W.instantAnimations||v.W.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!l.type&&!l.ease,p&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(t6),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[r]}(c.keyframes,l);if(void 0!==t)return void u.Gt.update(()=>{c.onUpdate(t),c.onComplete()})}return l.isSync?new ty(c):new t4(c)};function es(t,e,{delay:i=0,transitionOverride:s,type:n}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:l,...h}=e;s&&(r=s);let d=[],c=n&&t.animationState&&t.animationState.getState()[n];for(let e in h){let s=t.getValue(e,t.latestValues[e]??null),n=h[e];if(void 0===n||c&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(c,e))continue;let a={delay:i,...o(r||{},e)},l=s.get();if(void 0!==l&&!s.isAnimating&&!Array.isArray(n)&&n===l&&!a.velocity)continue;let m=!1;if(window.MotionHandoffAnimation){let i=t.props[w];if(i){let t=window.MotionHandoffAnimation(i,e,u.Gt);null!==t&&(a.startTime=t,m=!0)}}g(t,e),s.start(ei(e,s,n,t.shouldReduceMotion&&p.has(e)?{type:!1}:a,t,m));let f=s.animation;f&&d.push(f)}return l&&Promise.all(d).then(()=>{u.Gt.update(()=>{l&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=a(t,e)||{};for(let e in n={...n,...i}){var r;let i=f(r=n[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,m.OQ)(i))}}(t,l)})}),d}function en(t,e,i={}){let s=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);let r=s?()=>Promise.all(es(t,s,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=n;return function(t,e,i=0,s=0,n=1,r){let a=[],o=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>o-t*s;return Array.from(t.variantChildren).sort(er).forEach((t,s)=>{t.notify("AnimationStart",e),a.push(en(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r+s,a,o,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([r(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[r,o]:[o,r];return t().then(()=>e())}}function er(t,e){return t.sortNodePosition(e)}function ea(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function eo(t){return"string"==typeof t||Array.isArray(t)}let el=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],eh=["initial",...el],eu=eh.length,ed=[...el].reverse(),ec=el.length;function ep(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function em(){return{animate:ep(!0),whileInView:ep(),whileHover:ep(),whileTap:ep(),whileDrag:ep(),whileFocus:ep(),exit:ep()}}class ef{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ev extends ef{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>en(t,e,i)));else if("string"==typeof e)s=en(t,e,i);else{let n="function"==typeof e?a(t,e,i.custom):e;s=Promise.all(es(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=em(),n=!0,r=e=>(i,s)=>{let n=a(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function o(o){let{props:l}=t,h=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<eu;t++){let s=eh[t],n=e.props[s];(eo(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},u=[],d=new Set,c={},p=1/0;for(let e=0;e<ec;e++){var m,v;let a=ed[e],y=i[a],g=void 0!==l[a]?l[a]:h[a],x=eo(g),w=a===o?y.isActive:null;!1===w&&(p=e);let T=g===h[a]&&g!==l[a]&&x;if(T&&n&&t.manuallyAnimateOnMount&&(T=!1),y.protectedKeys={...c},!y.isActive&&null===w||!g&&!y.prevProp||s(g)||"boolean"==typeof g)continue;let P=(m=y.prevProp,"string"==typeof(v=g)?v!==m:!!Array.isArray(v)&&!ea(v,m)),b=P||a===o&&y.isActive&&!T&&x||e>p&&x,S=!1,A=Array.isArray(g)?g:[g],V=A.reduce(r(a),{});!1===w&&(V={});let{prevResolvedValues:M={}}=y,k={...M,...V},E=e=>{b=!0,d.has(e)&&(S=!0,d.delete(e)),y.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in k){let e=V[t],i=M[t];if(c.hasOwnProperty(t))continue;let s=!1;(f(e)&&f(i)?ea(e,i):e===i)?void 0!==e&&d.has(t)?E(t):y.protectedKeys[t]=!0:null!=e?E(t):d.add(t)}y.prevProp=g,y.prevResolvedValues=V,y.isActive&&(c={...c,...V}),n&&t.blockInitialAnimation&&(b=!1);let C=!(T&&P)||S;b&&C&&u.push(...A.map(t=>({animation:t,options:{type:a}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=s??null}),u.push({animation:e})}let y=!!u.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(y=!1),n=!1,y?e(u):Promise.resolve()}return{animateChanges:o,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let n=o(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=em(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();s(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ey=0;class eg extends ef{constructor(){super(...arguments),this.id=ey++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let ex={x:!1,y:!1};function ew(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let eT=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function eP(t){return{point:{x:t.pageX,y:t.pageY}}}let eb=t=>e=>eT(e)&&t(e,eP(e));function eS(t,e,i,s){return ew(t,e,eb(i),s)}function eA({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function eV(t){return t.max-t.min}function eM(t,e,i,s=.5){t.origin=s,t.originPoint=(0,th.k)(e.min,e.max,t.origin),t.scale=eV(i)/eV(e),t.translate=(0,th.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function ek(t,e,i,s){eM(t.x,e.x,i.x,s?s.originX:void 0),eM(t.y,e.y,i.y,s?s.originY:void 0)}function eE(t,e,i){t.min=i.min+e.min,t.max=t.min+eV(e)}function eC(t,e,i){t.min=e.min-i.min,t.max=t.min+eV(e)}function eD(t,e,i){eC(t.x,e.x,i.x),eC(t.y,e.y,i.y)}let eR=()=>({translate:0,scale:1,origin:0,originPoint:0}),ej=()=>({x:eR(),y:eR()}),eL=()=>({min:0,max:0}),eF=()=>({x:eL(),y:eL()});function eB(t){return[t("x"),t("y")]}function eO(t){return void 0===t||1===t}function eI({scale:t,scaleX:e,scaleY:i}){return!eO(t)||!eO(e)||!eO(i)}function eU(t){return eI(t)||eN(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function eN(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function eW(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function e$(t,e=0,i=1,s,n){t.min=eW(t.min,e,i,s,n),t.max=eW(t.max,e,i,s,n)}function eG(t,{x:e,y:i}){e$(t.x,e.translate,e.scale,e.originPoint),e$(t.y,i.translate,i.scale,i.originPoint)}function eq(t,e){t.min=t.min+e,t.max=t.max+e}function eX(t,e,i,s,n=.5){let r=(0,th.k)(t.min,t.max,n);e$(t,e,i,r,s)}function eK(t,e){eX(t.x,e.x,e.scaleX,e.scale,e.originX),eX(t.y,e.y,e.scaleY,e.scale,e.originY)}function eY(t,e){return eA(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let ez=({current:t})=>t?t.ownerDocument.defaultView:null;function eH(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let eQ=(t,e)=>Math.abs(t-e);class e_{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=e0(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(eQ(t.x,e.x)**2+eQ(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=u.uv;this.history.push({...s,timestamp:n});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=eZ(e,this.transformPagePoint),u.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=e0("pointercancel"===t.type?this.lastMoveEventInfo:eZ(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!eT(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=eZ(eP(t),this.transformPagePoint),{point:a}=r,{timestamp:o}=u.uv;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,e0(r,this.history)),this.removeListeners=(0,T.F)(eS(this.contextWindow,"pointermove",this.handlePointerMove),eS(this.contextWindow,"pointerup",this.handlePointerUp),eS(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,u.WG)(this.updatePoint)}}function eZ(t,e){return e?{point:e(t.point)}:t}function eJ(t,e){return{x:t.x-e.x,y:t.y-e.y}}function e0({point:t},e){return{point:t,delta:eJ(t,e1(e)),offset:eJ(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=e1(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>b(.1)));)i--;if(!s)return{x:0,y:0};let r=S(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let a={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function e1(t){return t[t.length-1]}function e5(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function e2(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function e3(t,e,i){return{min:e4(t,e),max:e4(t,i)}}function e4(t,e){return"number"==typeof t?t:t[e]||0}let e6=new WeakMap;class e8{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=eF(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new e_(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(eP(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(ex[t])return null;else return ex[t]=!0,()=>{ex[t]=!1};return ex.x||ex.y?null:(ex.x=ex.y=!0,()=>{ex.x=ex.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),eB(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tC.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=eV(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&u.Gt.postRender(()=>n(t,e)),g(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>eB(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:ez(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&u.Gt.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!e9(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?(0,th.k)(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?(0,th.k)(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&eH(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:e5(t.x,i,n),y:e5(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:e3(t,"left","right"),y:e3(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&eB(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!eH(e))return!1;let s=e.current;(0,L.V)(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=eY(t,i),{scroll:n}=e;return n&&(eq(s.x,n.offset.x),eq(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),a=(t=n.layout.layoutBox,{x:e2(t.x,r.x),y:e2(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=eA(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(eB(a=>{if(!e9(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return g(this.visualElement,t),i.start(ei(t,i,0,e,this.visualElement,!1))}stopAnimation(){eB(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){eB(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){eB(e=>{let{drag:i}=this.getProps();if(!e9(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-(0,th.k)(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!eH(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};eB(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=eV(t),n=eV(e);return n>s?i=(0,tl.q)(e.min,e.max-s,t.min):s>n&&(i=(0,tl.q)(t.min,t.max-n,e.min)),(0,P.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),eB(e=>{if(!e9(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set((0,th.k)(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;e6.set(this.visualElement,this);let t=eS(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();eH(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),u.Gt.read(e);let n=ew(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(eB(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:a}}}function e9(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class e7 extends ef{constructor(t){super(t),this.removeGroupControls=W.l,this.removeListeners=W.l,this.controls=new e8(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||W.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let it=t=>(e,i)=>{t&&u.Gt.postRender(()=>t(e,i))};class ie extends ef{constructor(){super(...arguments),this.removePointerDownListener=W.l}onPointerDown(t){this.session=new e_(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ez(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:it(t),onStart:it(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&u.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=eS(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var ii=i(95155);let{schedule:is}=(0,i(58437).I)(queueMicrotask,!1);var ir=i(12115),ia=i(32082),io=i(90869);let il=(0,ir.createContext)({}),ih={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function iu(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let id={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tC.px.test(t))return t;else t=parseFloat(t);let i=iu(t,e.target.x),s=iu(t,e.target.y);return`${i}% ${s}%`}};var ic=i(78606);let ip={};class im extends ir.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;for(let t in iy)ip[t]=iy[t],(0,ic.j)(t)&&(ip[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),ih.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,{projection:r}=i;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||u.Gt.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),is.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function iv(t){let[e,i]=(0,ia.xQ)(),s=(0,ir.useContext)(io.L);return(0,ii.jsx)(im,{...t,layoutGroup:s,switchLayoutGroup:(0,ir.useContext)(il),isPresent:e,safeToRemove:i})}let iy={borderRadius:{...id,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:id,borderTopRightRadius:id,borderBottomLeftRadius:id,borderBottomRightRadius:id,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=t0.f.parse(t);if(s.length>5)return t;let n=t0.f.createTransformer(t),r=+("number"!=typeof s[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;s[0+r]/=a,s[1+r]/=o;let l=(0,th.k)(a,o,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}};var ig=i(6983);function ix(t){return(0,ig.G)(t)&&"ownerSVGElement"in t}var iw=i(75626),iT=i(56668);let iP=(t,e)=>t.depth-e.depth;class ib{constructor(){this.children=[],this.isDirty=!1}add(t){(0,iT.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,iT.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(iP),this.isDirty=!1,this.children.forEach(t)}}function iS(t){return y(t)?t.get():t}let iA=["TopLeft","TopRight","BottomLeft","BottomRight"],iV=iA.length,iM=t=>"string"==typeof t?parseFloat(t):t,ik=t=>"number"==typeof t||tC.px.test(t);function iE(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let iC=iR(0,.5,te),iD=iR(.5,.95,W.l);function iR(t,e,i){return s=>s<t?0:s>e?1:i((0,tl.q)(t,e,s))}function ij(t,e){t.min=e.min,t.max=e.max}function iL(t,e){ij(t.x,e.x),ij(t.y,e.y)}function iF(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function iB(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function iO(t,e,[i,s,n],r,a){!function(t,e=0,i=1,s=.5,n,r=t,a=t){if(tC.KN.test(e)&&(e=parseFloat(e),e=(0,th.k)(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=(0,th.k)(r.min,r.max,s);t===r&&(o-=e),t.min=iB(t.min,e,i,o,n),t.max=iB(t.max,e,i,o,n)}(t,e[i],e[s],e[n],e.scale,r,a)}let iI=["x","scaleX","originX"],iU=["y","scaleY","originY"];function iN(t,e,i,s){iO(t.x,e,iI,i?i.x:void 0,s?s.x:void 0),iO(t.y,e,iU,i?i.y:void 0,s?s.y:void 0)}function iW(t){return 0===t.translate&&1===t.scale}function i$(t){return iW(t.x)&&iW(t.y)}function iG(t,e){return t.min===e.min&&t.max===e.max}function iq(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function iX(t,e){return iq(t.x,e.x)&&iq(t.y,e.y)}function iK(t){return eV(t.x)/eV(t.y)}function iY(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class iz{constructor(){this.members=[]}add(t){(0,iT.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,iT.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let iH={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iQ=["","X","Y","Z"],i_={visibility:"hidden"},iZ=0;function iJ(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function i0({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=iZ++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tX.Q.value&&(iH.nodes=iH.calculatedTargetDeltas=iH.calculatedProjections=0),this.nodes.forEach(i2),this.nodes.forEach(st),this.nodes.forEach(se),this.nodes.forEach(i3),tX.Q.addProjectionMetrics&&tX.Q.addProjectionMetrics(iH)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new ib)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new iw.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=ix(e)&&!(ix(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:s,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=A.k.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&((0,u.WG)(s),t(r-e))};return u.Gt.setup(s,!0),()=>(0,u.WG)(s)}(s,250),ih.hasAnimatedSinceResize&&(ih.hasAnimatedSinceResize=!1,this.nodes.forEach(i7))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||so,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=n.getProps(),h=!this.targetLayout||!iX(this.targetLayout,s),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...o(r,"layout"),onPlay:a,onComplete:l};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||i7(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,u.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(si),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[w];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",u.Gt,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(i6);return}this.isUpdating||this.nodes.forEach(i8),this.isUpdating=!1,this.nodes.forEach(i9),this.nodes.forEach(i1),this.nodes.forEach(i5),this.clearAllSnapshots();let t=A.k.now();u.uv.delta=(0,P.q)(0,1e3/60,t-u.uv.timestamp),u.uv.timestamp=t,u.uv.isProcessing=!0,u.PP.update.process(u.uv),u.PP.preRender.process(u.uv),u.PP.render.process(u.uv),u.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,is.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(i4),this.sharedNodes.forEach(ss)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,u.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){u.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||eV(this.snapshot.measuredBox.x)||eV(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=eF(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!i$(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||eU(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),su((e=s).x),su(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return eF();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(sc))){let{scroll:t}=this.root;t&&(eq(e.x,t.offset.x),eq(e.y,t.offset.y))}return e}removeElementScroll(t){let e=eF();if(iL(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&iL(e,t),eq(e.x,n.offset.x),eq(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=eF();iL(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&eK(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),eU(s.latestValues)&&eK(i,s.latestValues)}return eU(this.latestValues)&&eK(i,this.latestValues),i}removeTransform(t){let e=eF();iL(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!eU(i.latestValues))continue;eI(i.latestValues)&&i.updateSnapshot();let s=eF();iL(s,i.measurePageBox()),iN(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return eU(this.latestValues)&&iN(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==u.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:n}=this.options;if(this.layout&&(s||n)){if(this.resolvedRelativeTargetAt=u.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=eF(),this.relativeTargetOrigin=eF(),eD(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),iL(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=eF(),this.targetWithTransforms=eF()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,eE(r.x,a.x,o.x),eE(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iL(this.target,this.layout.layoutBox),eG(this.target,this.targetDelta)):iL(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=eF(),this.relativeTargetOrigin=eF(),eD(this.relativeTargetOrigin,this.target,t.target),iL(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tX.Q.value&&iH.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||eI(this.parent.latestValues)||eN(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===u.uv.timestamp&&(i=!1),i)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;iL(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let n,r,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(n=i[o]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&eK(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,eG(t,r)),s&&eU(n.latestValues)&&eK(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=eF());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(iF(this.prevProjectionDelta.x,this.projectionDelta.x),iF(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),ek(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&iY(this.projectionDelta.x,this.prevProjectionDelta.x)&&iY(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),tX.Q.value&&iH.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ej(),this.projectionDelta=ej(),this.projectionDeltaWithTransform=ej()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},a=ej();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=eF(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(sa));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(sn(a.x,t.x,s),sn(a.y,t.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,v;eD(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,v=s,sr(p.x,m.x,f.x,v),sr(p.y,m.y,f.y,v),i&&(h=this.relativeTarget,c=i,iG(h.x,c.x)&&iG(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=eF()),iL(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=(0,th.k)(0,i.opacity??1,iC(s)),t.opacityExit=(0,th.k)(e.opacity??1,0,iD(s))):r&&(t.opacity=(0,th.k)(e.opacity??1,i.opacity??1,s));for(let n=0;n<iV;n++){let r=`border${iA[n]}Radius`,a=iE(e,r),o=iE(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||ik(a)===ik(o)?(t[r]=Math.max((0,th.k)(iM(a),iM(o),s),0),(tC.KN.test(o)||tC.KN.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=(0,th.k)(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,u.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=u.Gt.update(()=>{ih.hasAnimatedSinceResize=!0,V.layout++,this.motionValue||(this.motionValue=(0,m.OQ)(0)),this.currentAnimation=function(t,e,i){let s=y(t)?t:(0,m.OQ)(t);return s.start(ei("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{V.layout--},onComplete:()=>{V.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&sd(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||eF();let e=eV(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=eV(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}iL(e,i),eK(e,n),ek(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new iz),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&iJ("z",t,s,this.animationValues);for(let e=0;e<iQ.length;e++)iJ(`rotate${iQ[e]}`,t,s,this.animationValues),iJ(`skew${iQ[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return i_;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=iS(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=iS(t?.pointerEvents)||""),this.hasProjected&&!eU(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((n||r||a)&&(s=`translate3d(${n}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:a,skewY:o}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),a&&(s+=`skewX(${a}deg) `),o&&(s+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:r,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,s.animationValues?e.opacity=s===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=s===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,ip){if(void 0===n[t])continue;let{correct:i,applyTo:r,isCSSVariable:a}=ip[t],o="none"===e.transform?n[t]:i(n[t],s);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=s===this?iS(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(i6),this.root.sharedNodes.clear()}}}function i1(t){t.updateLayout()}function i5(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:n}=t.options,r=e.source!==t.layout.source;"size"===n?eB(t=>{let s=r?e.measuredBox[t]:e.layoutBox[t],n=eV(s);s.min=i[t].min,s.max=s.min+n}):sd(n,e.layoutBox,i)&&eB(s=>{let n=r?e.measuredBox[s]:e.layoutBox[s],a=eV(i[s]);n.max=n.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+a)});let a=ej();ek(a,i,e.layoutBox);let o=ej();r?ek(o,t.applyTransform(s,!0),e.measuredBox):ek(o,i,e.layoutBox);let l=!i$(a),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let a=eF();eD(a,e.layoutBox,n.layoutBox);let o=eF();eD(o,i,r.layoutBox),iX(a,o)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function i2(t){tX.Q.value&&iH.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function i3(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function i4(t){t.clearSnapshot()}function i6(t){t.clearMeasurements()}function i8(t){t.isLayoutDirty=!1}function i9(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function i7(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function st(t){t.resolveTargetDelta()}function se(t){t.calcProjection()}function si(t){t.resetSkewAndRotation()}function ss(t){t.removeLeadSnapshot()}function sn(t,e,i){t.translate=(0,th.k)(e.translate,0,i),t.scale=(0,th.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function sr(t,e,i,s){t.min=(0,th.k)(e.min,i.min,s),t.max=(0,th.k)(e.max,i.max,s)}function sa(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let so={duration:.45,ease:[.4,0,.1,1]},sl=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),sh=sl("applewebkit/")&&!sl("chrome/")?Math.round:W.l;function su(t){t.min=sh(t.min),t.max=sh(t.max)}function sd(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(iK(e)-iK(i)))}function sc(t){return t!==t.root&&t.scroll?.wasRoot}let sp=i0({attachResizeListener:(t,e)=>ew(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),sm={current:void 0},sf=i0({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!sm.current){let t=new sp({});t.mount(window),t.setOptions({layoutScroll:!0}),sm.current=t}return sm.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function sv(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function sy(t){return!("touch"===t.pointerType||ex.x||ex.y)}function sg(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&u.Gt.postRender(()=>n(e,eP(e)))}class sx extends ef{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=sv(t,i),a=t=>{if(!sy(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{sy(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",a,n)}),r}(t,(t,e)=>(sg(this.node,e,"Start"),t=>sg(this.node,t,"End"))))}unmount(){}}class sw extends ef{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,T.F)(ew(this.node.current,"focus",()=>this.onFocus()),ew(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let sT=(t,e)=>!!e&&(t===e||sT(t,e.parentElement)),sP=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),sb=new WeakSet;function sS(t){return e=>{"Enter"===e.key&&t(e)}}function sA(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let sV=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=sS(()=>{if(sb.has(i))return;sA(i,"down");let t=sS(()=>{sA(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>sA(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function sM(t){return eT(t)&&!(ex.x||ex.y)}function sk(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&u.Gt.postRender(()=>n(e,eP(e)))}class sE extends ef{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=sv(t,i),a=t=>{let s=t.currentTarget;if(!sM(t))return;sb.add(s);let r=e(s,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),sb.has(s)&&sb.delete(s),sM(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,s===window||s===document||i.useGlobalTarget||sT(s,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,n),(0,t5.s)(t))&&(t.addEventListener("focus",t=>sV(t,n)),sP.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(sk(this.node,e,"Start"),(t,{success:e})=>sk(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sC=new WeakMap,sD=new WeakMap,sR=t=>{let e=sC.get(t.target);e&&e(t)},sj=t=>{t.forEach(sR)},sL={some:0,all:1};class sF extends ef{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:sL[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;sD.has(i)||sD.set(i,{});let s=sD.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(sj,{root:t,...e})),s[n]}(e);return sC.set(t,i),s.observe(t),()=>{sC.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let sB=(0,ir.createContext)({strict:!1});var sO=i(51508);let sI=(0,ir.createContext)({});function sU(t){return s(t.animate)||eh.some(e=>eo(t[e]))}function sN(t){return!!(sU(t)||t.variants)}function sW(t){return Array.isArray(t)?t.join(" "):t}var s$=i(68972);let sG={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},sq={};for(let t in sG)sq[t]={isEnabled:e=>sG[t].some(t=>!!e[t])};let sX=Symbol.for("motionComponentSymbol");var sK=i(80845),sY=i(97494);function sz(t,{layout:e,layoutId:i}){return c.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!ip[t]||"opacity"===t)}let sH=(t,e)=>e&&"number"==typeof t?e.transform(t):t,sQ={...tE.ai,transform:Math.round},s_={rotate:tC.uj,rotateX:tC.uj,rotateY:tC.uj,rotateZ:tC.uj,scale:tE.hs,scaleX:tE.hs,scaleY:tE.hs,scaleZ:tE.hs,skew:tC.uj,skewX:tC.uj,skewY:tC.uj,distance:tC.px,translateX:tC.px,translateY:tC.px,translateZ:tC.px,x:tC.px,y:tC.px,z:tC.px,perspective:tC.px,transformPerspective:tC.px,opacity:tE.X4,originX:tC.gQ,originY:tC.gQ,originZ:tC.px},sZ={borderWidth:tC.px,borderTopWidth:tC.px,borderRightWidth:tC.px,borderBottomWidth:tC.px,borderLeftWidth:tC.px,borderRadius:tC.px,radius:tC.px,borderTopLeftRadius:tC.px,borderTopRightRadius:tC.px,borderBottomRightRadius:tC.px,borderBottomLeftRadius:tC.px,width:tC.px,maxWidth:tC.px,height:tC.px,maxHeight:tC.px,top:tC.px,right:tC.px,bottom:tC.px,left:tC.px,padding:tC.px,paddingTop:tC.px,paddingRight:tC.px,paddingBottom:tC.px,paddingLeft:tC.px,margin:tC.px,marginTop:tC.px,marginRight:tC.px,marginBottom:tC.px,marginLeft:tC.px,backgroundPositionX:tC.px,backgroundPositionY:tC.px,...s_,zIndex:sQ,fillOpacity:tE.X4,strokeOpacity:tE.X4,numOctaves:sQ},sJ={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},s0=d.length;function s1(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(c.has(t)){a=!0;continue}if((0,ic.j)(t)){n[t]=i;continue}{let e=sH(i,sZ[t]);t.startsWith("origin")?(o=!0,r[t]=e):s[t]=e}}if(!e.transform&&(a||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<s0;r++){let a=d[r],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=sH(o,sZ[a]);if(!l){n=!1;let e=sJ[a]||a;s+=`${e}(${t}) `}i&&(e[a]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let s5=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function s2(t,e,i){for(let s in e)y(e[s])||sz(s,i)||(t[s]=e[s])}let s3={offset:"stroke-dashoffset",array:"stroke-dasharray"},s4={offset:"strokeDashoffset",array:"strokeDasharray"};function s6(t,{attrX:e,attrY:i,attrScale:s,pathLength:n,pathSpacing:r=1,pathOffset:a=0,...o},l,h,u){if(s1(t,o,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==s&&(d.scale=s),void 0!==n&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?s3:s4;t[r.offset]=tC.px.transform(-s);let a=tC.px.transform(e),o=tC.px.transform(i);t[r.array]=`${a} ${o}`}(d,n,r,a,!1)}let s8=()=>({...s5(),attrs:{}}),s9=t=>"string"==typeof t&&"svg"===t.toLowerCase(),s7=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nt(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||s7.has(t)}let ne=t=>!nt(t);try{!function(t){"function"==typeof t&&(ne=e=>e.startsWith("on")?!nt(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let ni=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ns(t){if("string"!=typeof t||t.includes("-"));else if(ni.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var nn=i(82885);let nr=t=>(e,i)=>{let n=(0,ir.useContext)(sI),a=(0,ir.useContext)(sK.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,a){return{latestValues:function(t,e,i,n){let a={},o=n(t,{});for(let t in o)a[t]=iS(o[t]);let{initial:l,animate:h}=t,u=sU(t),d=sN(t);e&&d&&!u&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===h&&(h=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?h:l;if(p&&"boolean"!=typeof p&&!s(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=r(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(a[t]=e)}for(let e in t)a[e]=t[e]}}}return a}(i,n,a,t),renderState:e()}})(t,e,n,a);return i?o():(0,nn.M)(o)};function na(t,e,i){let{style:s}=t,n={};for(let r in s)(y(s[r])||e.style&&y(e.style[r])||sz(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(n[r]=s[r]);return n}let no={useVisualState:nr({scrapeMotionValuesFromProps:na,createRenderState:s5})};function nl(t,e,i){let s=na(t,e,i);for(let i in t)(y(t[i])||y(e[i]))&&(s[-1!==d.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let nh={useVisualState:nr({scrapeMotionValuesFromProps:nl,createRenderState:s8})},nu=t=>e=>e.test(t),nd=[tE.ai,tC.px,tC.KN,tC.uj,tC.vw,tC.vh,{test:t=>"auto"===t,parse:t=>t}],nc=t=>nd.find(nu(t)),np=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),nm=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,nf=t=>/^0[^.\s]+$/u.test(t);var nv=i(30614);let ny=new Set(["brightness","contrast","saturate","opacity"]);function ng(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(nv.S)||[];if(!s)return t;let n=i.replace(s,""),r=+!!ny.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let nx=/\b([a-z-]*)\(.*?\)/gu,nw={...t0.f,getAnimatableNone:t=>{let e=t.match(nx);return e?e.map(ng).join(" "):t}};var nT=i(4272);let nP={...sZ,color:nT.y,backgroundColor:nT.y,outlineColor:nT.y,fill:nT.y,stroke:nT.y,borderColor:nT.y,borderTopColor:nT.y,borderRightColor:nT.y,borderBottomColor:nT.y,borderLeftColor:nT.y,filter:nw,WebkitFilter:nw},nb=t=>nP[t];function nS(t,e){let i=nb(t);return i!==nw&&(i=t0.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let nA=new Set(["auto","none","0"]);class nV extends tW{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&(s=s.trim(),(0,ic.p)(s))){let n=function t(e,i,s=1){(0,L.V)(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=nm.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${i??s}`,n]}(e);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let t=a.trim();return np(t)?parseFloat(t):t}return(0,ic.p)(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!p.has(i)||2!==t.length)return;let[s,n]=t,r=nc(s),a=nc(n);if(r!==a)if(tD(r)&&tD(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tL[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||nf(s)))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!nA.has(e)&&(0,t0.V)(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=nS(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tL[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let n=i.length-1,r=i[n];i[n]=tL[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let nM=[...nd,nT.y,t0.f],nk=t=>nM.find(nu(t)),nE={current:null},nC={current:!1},nD=new WeakMap,nR=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nj{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tW,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,u.Gt.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=sU(e),this.isVariantNode=sN(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==o[t]&&y(e)&&e.set(o[t],!1)}}mount(t){this.current=t,nD.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nC.current||function(){if(nC.current=!0,s$.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>nE.current=t.matches;t.addListener(e),e()}else nE.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nE.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,u.WG)(this.notifyUpdate),(0,u.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=c.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&u.Gt.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in sq){let e=sq[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):eF()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nR.length;e++){let i=nR[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(y(n))t.addValue(s,n);else if(y(r))t.addValue(s,(0,m.OQ)(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,(0,m.OQ)(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,m.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(np(i)||nf(i))?i=parseFloat(i):!nk(i)&&t0.f.test(e)&&(i=nS(t,e)),this.setBaseTarget(t,y(i)?i.get():i)),y(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=r(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||y(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new iw.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nL extends nj{constructor(){super(...arguments),this.KeyframeResolver=nV}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;y(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function nF(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}class nB extends nL{constructor(){super(...arguments),this.type="html",this.renderInstance=nF}readValueFromInstance(t,e){if(c.has(e))return this.projection?.isProjecting?tA(e):tM(t,e);{let i=window.getComputedStyle(t),s=((0,ic.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return eY(t,e)}build(t,e,i){s1(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return na(t,e,i)}}let nO=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nI extends nL{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=eF}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(c.has(e)){let t=nb(e);return t&&t.default||0}return e=nO.has(e)?e:x(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return nl(t,e,i)}build(t,e,i){s6(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){for(let i in nF(t,e,void 0,s),e.attrs)t.setAttribute(nO.has(i)?i:x(i),e.attrs[i])}mount(t){this.isSVGTag=s9(t.tagName),super.mount(t)}}let nU=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((l={animation:{Feature:ev},exit:{Feature:eg},inView:{Feature:sF},tap:{Feature:sE},focus:{Feature:sw},hover:{Feature:sx},pan:{Feature:ie},drag:{Feature:e7,ProjectionNode:sf,MeasureLayout:iv},layout:{ProjectionNode:sf,MeasureLayout:iv}},h=(t,e)=>ns(t)?new nI(e):new nB(e,{allowProjection:t!==ir.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:s,createVisualElement:n,useRender:r,useVisualState:a,Component:o}=t;function l(t,e){var i,s,l;let h,u={...(0,ir.useContext)(sO.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,ir.useContext)(io.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=u,c=function(t){let{initial:e,animate:i}=function(t,e){if(sU(t)){let{initial:e,animate:i}=t;return{initial:!1===e||eo(e)?e:void 0,animate:eo(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,ir.useContext)(sI));return(0,ir.useMemo)(()=>({initial:e,animate:i}),[sW(e),sW(i)])}(t),p=a(t,d);if(!d&&s$.B){s=0,l=0,(0,ir.useContext)(sB).strict;let t=function(t){let{drag:e,layout:i}=sq;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(u);h=t.MeasureLayout,c.visualElement=function(t,e,i,s,n){let{visualElement:r}=(0,ir.useContext)(sI),a=(0,ir.useContext)(sB),o=(0,ir.useContext)(sK.t),l=(0,ir.useContext)(sO.Q).reducedMotion,h=(0,ir.useRef)(null);s=s||a.renderer,!h.current&&s&&(h.current=s(t,{visualState:e,parent:r,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let u=h.current,d=(0,ir.useContext)(il);u&&!u.projection&&n&&("html"===u.type||"svg"===u.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!a||o&&eH(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:h})}(h.current,i,n,d);let c=(0,ir.useRef)(!1);(0,ir.useInsertionEffect)(()=>{u&&c.current&&u.update(i,o)});let p=i[w],m=(0,ir.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,sY.E)(()=>{u&&(c.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),is.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,ir.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),u}(o,p,u,n,t.ProjectionNode)}return(0,ii.jsxs)(sI.Provider,{value:c,children:[h&&c.visualElement?(0,ii.jsx)(h,{visualElement:c.visualElement,...u}):null,r(o,t,(i=c.visualElement,(0,ir.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):eH(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}s&&function(t){for(let e in t)sq[e]={...sq[e],...t[e]}}(s),l.displayName="motion.".concat("string"==typeof o?o:"create(".concat(null!=(i=null!=(e=o.displayName)?e:o.name)?i:"",")"));let h=(0,ir.forwardRef)(l);return h[sX]=o,h}({...ns(t)?nh:no,preloadedFeatures:l,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let a=(ns(e)?function(t,e,i,s){let n=(0,ir.useMemo)(()=>{let i=s8();return s6(i,e,s9(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};s2(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return s2(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,ir.useMemo)(()=>{let i=s5();return s1(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),o=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(ne(n)||!0===i&&nt(n)||!e&&!nt(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==ir.Fragment?{...o,...a,ref:s}:{},{children:h}=i,u=(0,ir.useMemo)(()=>y(h)?h.get():h,[h]);return(0,ir.createElement)(e,{...l,children:u})}}(e),createVisualElement:h,Component:t})}))},18476:(t,e,i)=>{i.d(e,{V:()=>o});var s=i(57887),n=i(34158),r=i(11557),a=i(55920);let o={test:(0,a.$)("hsl","hue"),parse:(0,a.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:a=1})=>"hsla("+Math.round(t)+", "+n.KN.transform((0,r.a)(e))+", "+n.KN.transform((0,r.a)(i))+", "+(0,r.a)(s.X4.transform(a))+")"}},19827:(t,e,i)=>{i.d(e,{l:()=>s});let s=t=>t},19946:(t,e,i)=>{i.d(e,{A:()=>d});var s=i(12115);let n=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),a=t=>{let e=r(t);return e.charAt(0).toUpperCase()+e.slice(1)},o=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()},l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var h={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,s.forwardRef)((t,e)=>{let{color:i="currentColor",size:n=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:u="",children:d,iconNode:c,...p}=t;return(0,s.createElement)("svg",{ref:e,...h,width:n,height:n,stroke:i,strokeWidth:a?24*Number(r)/Number(n):r,className:o("lucide",u),...!d&&!l(p)&&{"aria-hidden":"true"},...p},[...c.map(t=>{let[e,i]=t;return(0,s.createElement)(e,i)}),...Array.isArray(d)?d:[d]])}),d=(t,e)=>{let i=(0,s.forwardRef)((i,r)=>{let{className:l,...h}=i;return(0,s.createElement)(u,{ref:r,iconNode:e,className:o("lucide-".concat(n(a(t))),"lucide-".concat(t),l),...h})});return i.displayName=a(t),i}},23387:(t,e,i)=>{i.d(e,{W:()=>s});let s={}},24744:(t,e,i)=>{i.d(e,{Q:()=>s});let s={value:null,addProjectionMetrics:null}},26087:(t,e,i)=>{i.d(e,{j:()=>A});var s=i(53191),n=i(54542),r=i(78606),a=i(4272),o=i(60010),l=i(1335),h=i(18476);function u(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var d=i(9064);function c(t,e){return i=>i>0?e:t}var p=i(33210);let m=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},f=[l.u,d.B,h.V],v=t=>f.find(e=>e.test(t));function y(t){let e=v(t);if((0,n.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===h.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,a=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,o=2*i-s;n=u(o,s,t+1/3),r=u(o,s,t),a=u(o,s,t-1/3)}else n=r=a=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*a),alpha:s}}(i)),i}let g=(t,e)=>{let i=y(t),s=y(e);if(!i||!s)return c(t,e);let n={...i};return t=>(n.red=m(i.red,s.red,t),n.green=m(i.green,s.green,t),n.blue=m(i.blue,s.blue,t),n.alpha=(0,p.k)(i.alpha,s.alpha,t),d.B.transform(n))},x=new Set(["none","hidden"]);function w(t,e){return i=>(0,p.k)(t,e,i)}function T(t){return"number"==typeof t?w:"string"==typeof t?(0,r.p)(t)?c:a.y.test(t)?g:S:Array.isArray(t)?P:"object"==typeof t?a.y.test(t)?g:b:c}function P(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>T(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function b(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=T(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let S=(t,e)=>{let i=o.f.createTransformer(e),r=(0,o.V)(t),a=(0,o.V)(e);return r.indexes.var.length===a.indexes.var.length&&r.indexes.color.length===a.indexes.color.length&&r.indexes.number.length>=a.indexes.number.length?x.has(t)&&!a.values.length||x.has(e)&&!r.values.length?function(t,e){return x.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,s.F)(P(function(t,e){let i=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let r=e.types[n],a=t.indexes[r][s[r]],o=t.values[a]??0;i[n]=o,s[r]++}return i}(r,a),a.values),i):((0,n.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),c(t,e))};function A(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,p.k)(t,e,i):T(t)(t,e)}},27351:(t,e,i)=>{i.d(e,{s:()=>n});var s=i(6983);function n(t){return(0,s.G)(t)&&"offsetHeight"in t}},30614:(t,e,i)=>{i.d(e,{S:()=>s});let s=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},32082:(t,e,i)=>{i.d(e,{xQ:()=>r});var s=i(12115),n=i(80845);function r(t=!0){let e=(0,s.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:a,register:o}=e,l=(0,s.useId)();(0,s.useEffect)(()=>{if(t)return o(l)},[t]);let h=(0,s.useCallback)(()=>t&&a&&a(l),[l,a,t]);return!i&&a?[!1,h]:[!0]}},33210:(t,e,i)=>{i.d(e,{k:()=>s});let s=(t,e,i)=>t+(e-t)*i},34158:(t,e,i)=>{i.d(e,{KN:()=>r,gQ:()=>h,px:()=>a,uj:()=>n,vh:()=>o,vw:()=>l});let s=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),n=s("deg"),r=s("%"),a=s("px"),o=s("vh"),l=s("vw"),h={...r,parse:t=>r.parse(t)/100,transform:t=>r.transform(100*t)}},45818:(t,e,i)=>{i.d(e,{q:()=>s});let s=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s}},51508:(t,e,i)=>{i.d(e,{Q:()=>s});let s=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},53191:(t,e,i)=>{i.d(e,{F:()=>n});let s=(t,e)=>i=>e(t(i)),n=(...t)=>t.reduce(s)},53678:(t,e,i)=>{i.d(e,{q:()=>s});let s=(t,e,i)=>i>e?e:i<t?t:i},54542:(t,e,i)=>{i.d(e,{$:()=>s,V:()=>n});let s=()=>{},n=()=>{}},55920:(t,e,i)=>{i.d(e,{$:()=>r,q:()=>a});var s=i(30614);let n=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,r=(t,e)=>i=>!!("string"==typeof i&&n.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),a=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,a,o,l]=n.match(s.S);return{[t]:parseFloat(r),[e]:parseFloat(a),[i]:parseFloat(o),alpha:void 0!==l?parseFloat(l):1}}},56668:(t,e,i)=>{function s(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>n,Kq:()=>s})},57887:(t,e,i)=>{i.d(e,{X4:()=>r,ai:()=>n,hs:()=>a});var s=i(53678);let n={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},r={...n,transform:t=>(0,s.q)(0,1,t)},a={...n,default:1}},58437:(t,e,i)=>{i.d(e,{I:()=>a});var s=i(23387);let n=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var r=i(24744);function a(t,e){let i=!1,a=!0,o={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,h=n.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,n=!1,a=!1,o=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},h=0;function u(e){o.has(e)&&(d.schedule(e),t()),h++,e(l)}let d={schedule:(t,e=!1,r=!1)=>{let a=r&&n?i:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{if(l=t,n){a=!0;return}n=!0,[i,s]=[s,i],i.forEach(u),e&&r.Q.value&&r.Q.value.frameloop[e].push(h),h=0,i.clear(),n=!1,a&&(a=!1,d.process(t))}};return d}(l,e?i:void 0),t),{}),{setup:u,read:d,resolveKeyframes:c,preUpdate:p,update:m,preRender:f,render:v,postRender:y}=h,g=()=>{let n=s.W.useManualTiming?o.timestamp:performance.now();i=!1,s.W.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(n-o.timestamp,40),1)),o.timestamp=n,o.isProcessing=!0,u.process(o),d.process(o),c.process(o),p.process(o),m.process(o),f.process(o),v.process(o),y.process(o),o.isProcessing=!1,i&&e&&(a=!1,t(g))},x=()=>{i=!0,a=!0,o.isProcessing||t(g)};return{schedule:n.reduce((t,e)=>{let s=h[e];return t[e]=(t,e=!1,n=!1)=>(i||x(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<n.length;e++)h[n[e]].cancel(t)},state:o,steps:h}}},60010:(t,e,i)=>{i.d(e,{V:()=>u,f:()=>m});var s=i(4272);let n=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var r=i(30614),a=i(11557);let o="number",l="color",h=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function u(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],a=0,u=e.replace(h,t=>(s.y.test(t)?(n.color.push(a),r.push(l),i.push(s.y.parse(t))):t.startsWith("var(")?(n.var.push(a),r.push("var"),i.push(t)):(n.number.push(a),r.push(o),i.push(parseFloat(t))),++a,"${}")).split("${}");return{values:i,split:u,indexes:n,types:r}}function d(t){return u(t).values}function c(t){let{split:e,types:i}=u(t),n=e.length;return t=>{let r="";for(let h=0;h<n;h++)if(r+=e[h],void 0!==t[h]){let e=i[h];e===o?r+=(0,a.a)(t[h]):e===l?r+=s.y.transform(t[h]):r+=t[h]}return r}}let p=t=>"number"==typeof t?0:s.y.test(t)?s.y.getAnimatableNone(t):t,m={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(r.S)?.length||0)+(t.match(n)?.length||0)>0},parse:d,createTransformer:c,getAnimatableNone:function(t){let e=d(t);return c(t)(e.map(p))}}},60098:(t,e,i)=>{i.d(e,{OQ:()=>u,bt:()=>l});var s=i(75626),n=i(62923),r=i(74261),a=i(69515);let o=t=>!isNaN(parseFloat(t)),l={current:void 0};class h{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=r.k.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=r.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=o(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new s.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),a.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=r.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,n.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function u(t,e){return new h(t,e)}},62923:(t,e,i)=>{i.d(e,{f:()=>s});function s(t,e){return e?1e3/e*t:0}},68972:(t,e,i)=>{i.d(e,{B:()=>s});let s="undefined"!=typeof window},69515:(t,e,i)=>{i.d(e,{Gt:()=>n,PP:()=>o,WG:()=>r,uv:()=>a});var s=i(19827);let{schedule:n,cancel:r,state:a,steps:o}=(0,i(58437).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:s.l,!0)},74261:(t,e,i)=>{let s;i.d(e,{k:()=>o});var n=i(23387),r=i(69515);function a(){s=void 0}let o={now:()=>(void 0===s&&o.set(r.uv.isProcessing||n.W.useManualTiming?r.uv.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(a)}}},75626:(t,e,i)=>{i.d(e,{v:()=>n});var s=i(56668);class n{constructor(){this.subscriptions=[]}add(t){return(0,s.Kq)(this.subscriptions,t),()=>(0,s.Ai)(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},78606:(t,e,i)=>{i.d(e,{j:()=>n,p:()=>a});let s=t=>e=>"string"==typeof e&&e.startsWith(t),n=s("--"),r=s("var(--"),a=t=>!!r(t)&&o.test(t.split("/*")[0].trim()),o=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},80845:(t,e,i)=>{i.d(e,{t:()=>s});let s=(0,i(12115).createContext)(null)},82885:(t,e,i)=>{i.d(e,{M:()=>n});var s=i(12115);function n(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},90869:(t,e,i)=>{i.d(e,{L:()=>s});let s=(0,i(12115).createContext)({})},97494:(t,e,i)=>{i.d(e,{E:()=>n});var s=i(12115);let n=i(68972).B?s.useLayoutEffect:s.useEffect}}]);