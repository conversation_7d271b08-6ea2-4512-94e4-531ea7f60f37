(()=>{var e={};e.id=631,e.ids=[631],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12492:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>r.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>o});var t=a(65239),i=a(48088),n=a(88170),r=a.n(n),l=a(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(s,c);let o={children:["",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,62623)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,63144)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\settings\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/settings/page",pathname:"/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},12597:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15905:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>J});var t=a(60687),i=a(43210),n=a(50371),r=a(58869),l=a(97051),c=a(99891),o=a(62688);let d=(0,o.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]);var m=a(79410),p=a(1994);let x=(0,o.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var h=a(78122),u=a(8819),f=a(16023),j=a(88233),y=a(41550);let v=(0,o.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);var N=a(10022),g=a(40228),b=a(19959),w=a(12597),k=a(13861),A=a(29523),C=a(89667),P=a(44493),E=a(96834),M=a(96724),S=a(32584),R=a(70569),z=a(98599),_=a(11273),q=a(65551),T=a(83721),D=a(18853),L=a(14163),B="Switch",[G,H]=(0,_.A)(B),[Z,F]=G(B),I=i.forwardRef((e,s)=>{let{__scopeSwitch:a,name:n,checked:r,defaultChecked:l,required:c,disabled:o,value:d="on",onCheckedChange:m,form:p,...x}=e,[h,u]=i.useState(null),f=(0,z.s)(s,e=>u(e)),j=i.useRef(!1),y=!h||p||!!h.closest("form"),[v,N]=(0,q.i)({prop:r,defaultProp:l??!1,onChange:m,caller:B});return(0,t.jsxs)(Z,{scope:a,checked:v,disabled:o,children:[(0,t.jsx)(L.sG.button,{type:"button",role:"switch","aria-checked":v,"aria-required":c,"data-state":V(v),"data-disabled":o?"":void 0,disabled:o,value:d,...x,ref:f,onClick:(0,R.m)(e.onClick,e=>{N(e=>!e),y&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),y&&(0,t.jsx)(U,{control:h,bubbles:!j.current,name:n,value:d,checked:v,required:c,disabled:o,form:p,style:{transform:"translateX(-100%)"}})]})});I.displayName=B;var W="SwitchThumb",$=i.forwardRef((e,s)=>{let{__scopeSwitch:a,...i}=e,n=F(W,a);return(0,t.jsx)(L.sG.span,{"data-state":V(n.checked),"data-disabled":n.disabled?"":void 0,...i,ref:s})});$.displayName=W;var U=i.forwardRef(({__scopeSwitch:e,control:s,checked:a,bubbles:n=!0,...r},l)=>{let c=i.useRef(null),o=(0,z.s)(c,l),d=(0,T.Z)(a),m=(0,D.X)(s);return i.useEffect(()=>{let e=c.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==a&&s){let t=new Event("click",{bubbles:n});s.call(e,a),e.dispatchEvent(t)}},[d,a,n]),(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...r,tabIndex:-1,ref:o,style:{...r.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function V(e){return e?"checked":"unchecked"}U.displayName="SwitchBubbleInput";var Y=a(4780);let O=i.forwardRef(({className:e,...s},a)=>(0,t.jsx)(I,{className:(0,Y.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:a,children:(0,t.jsx)($,{className:(0,Y.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));O.displayName=I.displayName;let K={profile:{firstName:"John",lastName:"Doe",email:"<EMAIL>",phone:"+****************",department:"Engineering",position:"Senior Software Engineer",avatar:"/avatars/john.jpg",timezone:"America/New_York",language:"English"},notifications:{emailNotifications:!0,pushNotifications:!0,smsNotifications:!1,weeklyReports:!0,systemAlerts:!0,leaveApprovals:!0,payrollUpdates:!1,announcementUpdates:!0},security:{twoFactorEnabled:!0,sessionTimeout:30,passwordLastChanged:"2024-01-15",loginHistory:!0,deviceManagement:!0},appearance:{theme:"system",compactMode:!1,showAvatars:!0,animationsEnabled:!0,sidebarCollapsed:!1}},X={general:{companyName:"PeopleNest Inc.",companyEmail:"<EMAIL>",companyPhone:"+****************",address:"123 Business Ave, Suite 100, City, State 12345",website:"https://www.peoplenest.com",timezone:"America/New_York",fiscalYearStart:"January"},hrPolicies:{workingHours:"9:00 AM - 5:00 PM",workingDays:"Monday - Friday",leavePolicyEnabled:!0,overtimeEnabled:!0,remoteWorkEnabled:!0,flexibleHoursEnabled:!0},payroll:{payFrequency:"Bi-weekly",payrollCurrency:"USD",taxCalculationEnabled:!0,benefitsEnabled:!0,bonusEnabled:!0}};function J(){let[e,s]=(0,i.useState)("profile"),[a,o]=(0,i.useState)(!1),[R,z]=(0,i.useState)(K),[_,q]=(0,i.useState)(X),T=[{id:"profile",label:"Profile",icon:r.A},{id:"notifications",label:"Notifications",icon:l.A},{id:"security",label:"Security",icon:c.A},{id:"appearance",label:"Appearance",icon:d},{id:"company",label:"Company",icon:m.A},{id:"organization",label:"Organization",icon:p.A},{id:"system",label:"System",icon:x}],D=(e,s)=>{z(a=>({...a,notifications:{...a.notifications,[e]:s}}))},L=(e,s)=>{z(a=>({...a,appearance:{...a.appearance,[e]:s}}))};return(0,t.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,t.jsx)(M.Y,{title:"Settings",subtitle:"Manage your account, preferences, and system configuration",actions:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(A.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Reset to Default"]}),(0,t.jsxs)(A.$,{size:"sm",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Save Changes"]})]})}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,t.jsx)("div",{className:"lg:w-64",children:(0,t.jsx)(P.Zp,{children:(0,t.jsx)(P.Wu,{className:"p-4",children:(0,t.jsx)("nav",{className:"space-y-2",children:T.map(a=>{let i=a.icon;return(0,t.jsxs)("button",{onClick:()=>s(a.id),className:`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${e===a.id?"bg-primary text-primary-foreground":"hover:bg-muted text-muted-foreground"}`,children:[(0,t.jsx)(i,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:a.label})]},a.id)})})})})}),(0,t.jsxs)("div",{className:"flex-1",children:["profile"===e&&(0,t.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(P.Zp,{children:[(0,t.jsxs)(P.aR,{children:[(0,t.jsxs)(P.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(r.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Profile Information"})]}),(0,t.jsx)(P.BT,{children:"Update your personal information and contact details"})]}),(0,t.jsxs)(P.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,t.jsxs)(S.eu,{className:"h-20 w-20",children:[(0,t.jsx)(S.BK,{src:R.profile.avatar,alt:"Profile"}),(0,t.jsxs)(S.q5,{className:"text-lg",children:[R.profile.firstName[0],R.profile.lastName[0]]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(A.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Change Photo"]}),(0,t.jsxs)(A.$,{variant:"ghost",size:"sm",className:"text-destructive",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Remove Photo"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"First Name"}),(0,t.jsx)(C.p,{value:R.profile.firstName})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Last Name"}),(0,t.jsx)(C.p,{value:R.profile.lastName})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,t.jsx)(C.p,{value:R.profile.email,type:"email"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Phone"}),(0,t.jsx)(C.p,{value:R.profile.phone})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Department"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"Engineering",children:"Engineering"}),(0,t.jsx)("option",{value:"Product",children:"Product"}),(0,t.jsx)("option",{value:"Design",children:"Design"}),(0,t.jsx)("option",{value:"Sales",children:"Sales"}),(0,t.jsx)("option",{value:"Marketing",children:"Marketing"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Position"}),(0,t.jsx)(C.p,{value:R.profile.position})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Timezone"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"America/New_York",children:"Eastern Time"}),(0,t.jsx)("option",{value:"America/Chicago",children:"Central Time"}),(0,t.jsx)("option",{value:"America/Denver",children:"Mountain Time"}),(0,t.jsx)("option",{value:"America/Los_Angeles",children:"Pacific Time"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Language"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"English",children:"English"}),(0,t.jsx)("option",{value:"Spanish",children:"Spanish"}),(0,t.jsx)("option",{value:"French",children:"French"}),(0,t.jsx)("option",{value:"German",children:"German"})]})]})]})]})]})}),"notifications"===e&&(0,t.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(P.Zp,{children:[(0,t.jsxs)(P.aR,{children:[(0,t.jsxs)(P.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(l.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Notification Preferences"})]}),(0,t.jsx)(P.BT,{children:"Choose how you want to be notified about important updates"})]}),(0,t.jsx)(P.Wu,{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Email Notifications"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications via email"})]}),(0,t.jsx)(O,{checked:R.notifications.emailNotifications,onCheckedChange:e=>D("emailNotifications",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Push Notifications"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive browser push notifications"})]}),(0,t.jsx)(O,{checked:R.notifications.pushNotifications,onCheckedChange:e=>D("pushNotifications",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(v,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"SMS Notifications"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications via text message"})]}),(0,t.jsx)(O,{checked:R.notifications.smsNotifications,onCheckedChange:e=>D("smsNotifications",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Weekly Reports"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive weekly summary reports"})]}),(0,t.jsx)(O,{checked:R.notifications.weeklyReports,onCheckedChange:e=>D("weeklyReports",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"System Alerts"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Important system and security alerts"})]}),(0,t.jsx)(O,{checked:R.notifications.systemAlerts,onCheckedChange:e=>D("systemAlerts",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Leave Approvals"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Notifications for leave requests and approvals"})]}),(0,t.jsx)(O,{checked:R.notifications.leaveApprovals,onCheckedChange:e=>D("leaveApprovals",e)})]})]})})]})}),"security"===e&&(0,t.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(P.Zp,{children:[(0,t.jsxs)(P.aR,{children:[(0,t.jsxs)(P.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Security Settings"})]}),(0,t.jsx)(P.BT,{children:"Manage your account security and privacy settings"})]}),(0,t.jsx)(P.Wu,{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"font-medium",children:"Two-Factor Authentication"}),(0,t.jsx)(E.E,{variant:"success",className:"text-xs",children:"Enabled"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Add an extra layer of security to your account"})]}),(0,t.jsx)(O,{checked:R.security.twoFactorEnabled})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Change Password"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(C.p,{type:a?"text":"password",placeholder:"Current password"}),(0,t.jsx)(A.$,{variant:"ghost",size:"icon",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6",onClick:()=>o(!a),children:a?(0,t.jsx)(w.A,{className:"h-4 w-4"}):(0,t.jsx)(k.A,{className:"h-4 w-4"})})]}),(0,t.jsx)(C.p,{type:"password",placeholder:"New password"}),(0,t.jsx)(C.p,{type:"password",placeholder:"Confirm new password"}),(0,t.jsx)(A.$,{size:"sm",children:"Update Password"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Session Timeout"}),(0,t.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"15",children:"15 minutes"}),(0,t.jsx)("option",{value:"30",children:"30 minutes"}),(0,t.jsx)("option",{value:"60",children:"1 hour"}),(0,t.jsx)("option",{value:"240",children:"4 hours"}),(0,t.jsx)("option",{value:"480",children:"8 hours"})]})]}),(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Password Last Changed"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:new Date(R.security.passwordLastChanged).toLocaleDateString()})]})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Login History"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Keep track of account access"})]}),(0,t.jsx)(O,{checked:R.security.loginHistory})]})]})})]})}),"appearance"===e&&(0,t.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:(0,t.jsxs)(P.Zp,{children:[(0,t.jsxs)(P.aR,{children:[(0,t.jsxs)(P.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(d,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Appearance Settings"})]}),(0,t.jsx)(P.BT,{children:"Customize the look and feel of your interface"})]}),(0,t.jsx)(P.Wu,{className:"space-y-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Theme"}),(0,t.jsxs)("select",{value:R.appearance.theme,onChange:e=>L("theme",e.target.value),className:"w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"light",children:"Light"}),(0,t.jsx)("option",{value:"dark",children:"Dark"}),(0,t.jsx)("option",{value:"system",children:"System"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Compact Mode"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Use smaller spacing and elements"})]}),(0,t.jsx)(O,{checked:R.appearance.compactMode,onCheckedChange:e=>L("compactMode",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Show Avatars"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Display user profile pictures"})]}),(0,t.jsx)(O,{checked:R.appearance.showAvatars,onCheckedChange:e=>L("showAvatars",e)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"font-medium",children:"Animations"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable smooth transitions and animations"})]}),(0,t.jsx)(O,{checked:R.appearance.animationsEnabled,onCheckedChange:e=>L("animationsEnabled",e)})]})]})})]})})]})]})]})}},16023:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19581:(e,s,a)=>{Promise.resolve().then(a.bind(a,15905))},19959:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},24317:(e,s,a)=>{Promise.resolve().then(a.bind(a,62623))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41550:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},62623:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\settings\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},88233:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[447,912,18,722,632,809,456],()=>a(12492));module.exports=t})();