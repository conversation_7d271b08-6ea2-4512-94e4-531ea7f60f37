"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5409],{25409:(e,a,s)=>{s.d(a,{Y:()=>V});var t=s(95155),r=s(12115),l=s(60760),n=s(17859),i=s(84616),d=s(91788),o=s(53904),c=s(66932),m=s(47924),u=s(23861),x=s(81497),f=s(93509),h=s(62098),p=s(66474),g=s(71007),b=s(75525),N=s(381),j=s(34869),v=s(34835),y=s(30285),w=s(62523),k=s(91394),A=s(26126),C=s(66695),R=s(59434);function S(e){return e&&({super_admin:"Super Admin",system_admin:"System Admin",hr_admin:"HR Admin",hr:"HR",manager:"Manager",employee:"Employee",recruiter:"Recruiter",payroll:"Payroll",it:"IT"})[e]||"Employee"}function z(e){return{text:S(e),className:function(e){let a={super_admin:"text-red-600 bg-red-500/10 border-red-500/20 dark:text-red-400",system_admin:"text-purple-600 bg-purple-500/10 border-purple-500/20 dark:text-purple-400",hr_admin:"text-blue-600 bg-blue-500/10 border-blue-500/20 dark:text-blue-400",hr:"text-green-600 bg-green-500/10 border-green-500/20 dark:text-green-400",manager:"text-orange-600 bg-orange-500/10 border-orange-500/20 dark:text-orange-400",employee:"text-muted-foreground bg-muted border-border",recruiter:"text-indigo-600 bg-indigo-500/10 border-indigo-500/20 dark:text-indigo-400",payroll:"text-yellow-600 bg-yellow-500/10 border-yellow-500/20 dark:text-yellow-400",it:"text-cyan-600 bg-cyan-500/10 border-cyan-500/20 dark:text-cyan-400"};return e&&a[e]||a.employee}(e)}}var E=s(77890),L=s(94819),_=s(54165),P=s(59409),T=s(17951),q=s(17580),F=s(17576);let H=[{value:"super_admin",label:"Super Admin",description:"Full system access with all permissions",icon:(0,t.jsx)(T.A,{className:"h-4 w-4"})},{value:"system_admin",label:"System Admin",description:"System administration and configuration",icon:(0,t.jsx)(N.A,{className:"h-4 w-4"})},{value:"hr_admin",label:"HR Admin",description:"HR management and employee administration",icon:(0,t.jsx)(b.A,{className:"h-4 w-4"})},{value:"hr",label:"HR",description:"Human resources operations",icon:(0,t.jsx)(q.A,{className:"h-4 w-4"})},{value:"manager",label:"Manager",description:"Team and department management",icon:(0,t.jsx)(F.A,{className:"h-4 w-4"})},{value:"employee",label:"Employee",description:"Standard employee access",icon:(0,t.jsx)(g.A,{className:"h-4 w-4"})}];function U(e){var a;let{isOpen:s,onClose:l}=e,{user:i,updateUser:d}=(0,L.A)(),[o,c]=(0,r.useState)((null==i?void 0:i.role)||"employee"),[m,u]=(0,r.useState)(!1),x=async()=>{if(!i||o===i.role)return void l();u(!0);try{let e={...i,role:o};d(e),await new Promise(e=>setTimeout(e,1e3)),l()}catch(e){console.error("Failed to change role:",e)}finally{u(!1)}},f=z(null==i?void 0:i.role),h=z(o);return(0,t.jsx)(_.lG,{open:s,onOpenChange:l,children:(0,t.jsxs)(_.Cf,{className:"sm:max-w-md",children:[(0,t.jsxs)(_.c7,{children:[(0,t.jsxs)(_.L3,{className:"flex items-center space-x-2",children:[(0,t.jsx)(b.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Role Management"})]}),(0,t.jsx)(_.rr,{children:"Change your current role for testing different permission levels"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Current Role"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(A.E,{variant:"outline",className:"px-3 py-1 ".concat(f.className),children:f.text}),!!(a=null==i?void 0:i.role)&&["super_admin","system_admin","hr_admin"].includes(a)&&(0,t.jsx)(A.E,{variant:"secondary",className:"text-xs",children:"Admin Level"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Select New Role"}),(0,t.jsxs)(P.l6,{value:o,onValueChange:e=>c(e),children:[(0,t.jsx)(P.bq,{children:(0,t.jsx)(P.yv,{placeholder:"Select a role"})}),(0,t.jsx)(P.gC,{children:H.map(e=>(0,t.jsx)(P.eb,{value:e.value,children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[e.icon,(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.label}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})]})},e.value))})]})]}),o!==(null==i?void 0:i.role)&&(0,t.jsxs)(n.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"p-3 bg-muted rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium mb-2",children:"Preview:"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm",children:"You will appear as:"}),(0,t.jsx)(A.E,{variant:"outline",className:"px-2 py-1 ".concat(h.className),children:h.text})]})]})]}),(0,t.jsxs)(_.Es,{children:[(0,t.jsx)(y.$,{variant:"outline",onClick:l,disabled:m,children:"Cancel"}),(0,t.jsx)(y.$,{onClick:x,disabled:m||o===(null==i?void 0:i.role),children:m?"Changing...":"Change Role"})]})]})})}function V(e){var a,s,z,_;let{title:P,subtitle:T,actions:q,className:F}=e,[H,V]=(0,r.useState)(!1),[D,M]=(0,r.useState)(!1),[Z,$]=(0,r.useState)(!1),[I,J]=(0,r.useState)(""),{theme:B,toggleTheme:Y,actualTheme:O}=(0,E.D)(),{logout:W,user:K}=(0,L.A)(),G=[{id:1,title:"New Employee Onboarding",message:"Sarah Johnson has completed her onboarding checklist",time:"2 minutes ago",unread:!0,type:"success"},{id:2,title:"Leave Request Pending",message:"Mike Chen has requested 3 days of annual leave",time:"1 hour ago",unread:!0,type:"warning"},{id:3,title:"Performance Review Due",message:"5 performance reviews are due this week",time:"3 hours ago",unread:!1,type:"info"}],Q=[{name:"Add Employee",icon:i.A,action:()=>{}},{name:"Export Data",icon:d.A,action:()=>{}},{name:"Refresh",icon:o.A,action:()=>{}},{name:"Filter",icon:c.A,action:()=>{}}];return(0,t.jsxs)("header",{className:(0,R.cn)("sticky top-0 z-40 w-full border-b border-border bg-background/80 backdrop-blur-sm",F),children:[(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between px-6",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("div",{children:[P&&(0,t.jsx)("h1",{className:"text-xl font-semibold text-foreground",children:P}),T&&(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:T})]})}),(0,t.jsx)("div",{className:"flex-1 max-w-md mx-8",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(w.p,{type:"text",placeholder:"Search employees, documents, or anything...",value:I,onChange:e=>J(e.target.value),className:"pl-10 pr-4 w-full bg-muted border-border focus:bg-background transition-colors"})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[q&&(0,t.jsx)("div",{className:"flex items-center space-x-2 mr-4",children:q}),(0,t.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:Q.map(e=>(0,t.jsx)(y.$,{variant:"ghost",size:"icon",onClick:e.action,className:"h-9 w-9 text-muted-foreground hover:text-foreground",title:e.name,children:(0,t.jsx)(e.icon,{className:"h-4 w-4"})},e.name))}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(y.$,{variant:"ghost",size:"icon",onClick:()=>V(!H),className:"h-9 w-9 text-muted-foreground hover:text-foreground relative",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),G.some(e=>e.unread)&&(0,t.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full"})]}),(0,t.jsx)(l.N,{children:H&&(0,t.jsx)(n.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute right-0 mt-2 w-80 z-50",children:(0,t.jsx)(C.Zp,{className:"shadow-lg border-border",children:(0,t.jsxs)(C.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"p-4 border-b border-border",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"font-semibold text-foreground",children:"Notifications"}),(0,t.jsxs)(A.E,{variant:"secondary",className:"text-xs",children:[G.filter(e=>e.unread).length," new"]})]})}),(0,t.jsx)("div",{className:"max-h-80 overflow-y-auto",children:G.map(e=>(0,t.jsx)("div",{className:(0,R.cn)("p-4 border-b border-border hover:bg-muted cursor-pointer transition-colors",e.unread&&"bg-primary/5"),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:(0,R.cn)("w-2 h-2 rounded-full mt-2 flex-shrink-0","success"===e.type&&"bg-green-500","warning"===e.type&&"bg-yellow-500","info"===e.type&&"bg-primary")}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-foreground",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.message}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground/70 mt-2",children:e.time})]})]})},e.id))}),(0,t.jsx)("div",{className:"p-3 border-t border-border",children:(0,t.jsx)(y.$,{variant:"ghost",className:"w-full text-sm",children:"View all notifications"})})]})})})})]}),(0,t.jsx)(y.$,{variant:"ghost",size:"icon",className:"h-9 w-9 text-muted-foreground hover:text-foreground",children:(0,t.jsx)(x.A,{className:"h-4 w-4"})}),(0,t.jsx)(y.$,{variant:"ghost",size:"icon",onClick:Y,className:"h-9 w-9 text-muted-foreground hover:text-foreground",title:"Switch to ".concat("light"===O?"dark":"light"," mode"),children:"light"===O?(0,t.jsx)(f.A,{className:"h-4 w-4"}):(0,t.jsx)(h.A,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(y.$,{variant:"ghost",onClick:()=>M(!D),className:"flex items-center space-x-2 h-9 px-3 text-foreground hover:text-foreground/80",children:[(0,t.jsxs)(k.eu,{className:"h-7 w-7",children:[(0,t.jsx)(k.BK,{src:"/avatars/user.jpg",alt:(null==K?void 0:K.firstName)||"User"}),(0,t.jsxs)(k.q5,{children:[null==K||null==(a=K.firstName)?void 0:a[0],(null==K||null==(s=K.lastName)?void 0:s[0])||"U"]})]}),(0,t.jsx)(p.A,{className:"h-3 w-3"})]}),(0,t.jsx)(l.N,{children:D&&(0,t.jsx)(n.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute right-0 mt-2 w-56 z-50",children:(0,t.jsx)(C.Zp,{className:"shadow-lg border-border",children:(0,t.jsxs)(C.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"p-4 border-b border-border",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(k.eu,{className:"h-10 w-10",children:[(0,t.jsx)(k.BK,{src:"/avatars/user.jpg",alt:(null==K?void 0:K.firstName)||"User"}),(0,t.jsxs)(k.q5,{children:[null==K||null==(z=K.firstName)?void 0:z[0],(null==K||null==(_=K.lastName)?void 0:_[0])||"U"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"font-medium text-foreground",children:[null==K?void 0:K.firstName," ",null==K?void 0:K.lastName]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:S(null==K?void 0:K.role)})]})]})}),(0,t.jsxs)("div",{className:"py-2",children:[[{icon:g.A,label:"Profile",href:"/dashboard",action:()=>{}},{icon:b.A,label:"Change Role",href:"#",action:()=>$(!0)},{icon:N.A,label:"Settings",href:"/dashboard/settings",action:()=>{}},{icon:j.A,label:"Language",href:"#",action:()=>{}}].map(e=>(0,t.jsxs)("button",{type:"button",onClick:e.action,className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors",children:[(0,t.jsx)(e.icon,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.label})]},e.label)),(0,t.jsxs)("button",{type:"button",onClick:Y,className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors",children:["light"===O?(0,t.jsx)(f.A,{className:"h-4 w-4"}):(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"light"===O?"Dark Mode":"Light Mode"})]}),(0,t.jsx)("hr",{className:"my-2"}),(0,t.jsxs)("button",{type:"button",onClick:W,className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Sign out"})]})]})]})})})})]})]})]}),(0,t.jsx)(U,{isOpen:Z,onClose:()=>$(!1)})]})}},33118:(e,a,s)=>{s.d(a,{AZ:()=>n,OD:()=>l,fu:()=>r,zS:()=>t});function t(){let e=localStorage.getItem("theme-mode");return"light"===e||"dark"===e?e:"system"}function r(e){"system"===e?localStorage.removeItem("theme-mode"):localStorage.setItem("theme-mode",e)}function l(){r("light"===t()?"dark":"light")}function n(e){let a=document.documentElement;if(a.classList.remove("light","dark"),"system"===e){let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";a.classList.add(e)}else a.classList.add(e)}},54165:(e,a,s)=>{s.d(a,{Cf:()=>m,Es:()=>x,L3:()=>f,c7:()=>u,lG:()=>d,rr:()=>h});var t=s(95155),r=s(12115),l=s(15452),n=s(54416),i=s(59434);let d=l.bL;l.l9;let o=l.ZL;l.bm;let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.hJ,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});c.displayName=l.hJ.displayName;let m=r.forwardRef((e,a)=>{let{className:s,children:r,...d}=e;return(0,t.jsxs)(o,{children:[(0,t.jsx)(c,{}),(0,t.jsxs)(l.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...d,children:[r,(0,t.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=l.UC.displayName;let u=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...s})};u.displayName="DialogHeader";let x=e=>{let{className:a,...s}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...s})};x.displayName="DialogFooter";let f=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});f.displayName=l.hE.displayName;let h=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",s),...r})});h.displayName=l.VY.displayName},59409:(e,a,s)=>{s.d(a,{bq:()=>u,eb:()=>p,gC:()=>h,l6:()=>c,yv:()=>m});var t=s(95155),r=s(12115),l=s(22697),n=s(66474),i=s(47863),d=s(5196),o=s(59434);let c=l.bL;l.YJ;let m=l.WT,u=r.forwardRef((e,a)=>{let{className:s,children:r,...i}=e;return(0,t.jsxs)(l.l9,{ref:a,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...i,children:[r,(0,t.jsx)(l.In,{asChild:!0,children:(0,t.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let x=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.PP,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})})});x.displayName=l.PP.displayName;let f=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.wn,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})})});f.displayName=l.wn.displayName;let h=r.forwardRef((e,a)=>{let{className:s,children:r,position:n="popper",...i}=e;return(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{ref:a,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...i,children:[(0,t.jsx)(x,{}),(0,t.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,t.jsx)(f,{})]})})});h.displayName=l.UC.displayName,r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.JU,{ref:a,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=l.JU.displayName;let p=r.forwardRef((e,a)=>{let{className:s,children:r,...n}=e;return(0,t.jsxs)(l.q7,{ref:a,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(l.p4,{children:r})]})});p.displayName=l.q7.displayName,r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.wv,{ref:a,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=l.wv.displayName},62523:(e,a,s)=>{s.d(a,{p:()=>i});var t=s(95155),r=s(12115),l=s(59434);let n=(0,s(74466).F)("flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"",error:"border-red-500 focus-visible:ring-red-500",success:"border-green-500 focus-visible:ring-green-500"},size:{default:"h-10",sm:"h-9 px-2 text-xs",lg:"h-11 px-4"}},defaultVariants:{variant:"default",size:"default"}}),i=r.forwardRef((e,a)=>{let{className:s,type:i,variant:d,size:o,leftIcon:c,rightIcon:m,error:u,label:x,helperText:f,id:h,...p}=e,g=h||r.useId(),b=!!u;return(0,t.jsxs)("div",{className:"w-full",children:[x&&(0,t.jsx)("label",{htmlFor:g,className:"block text-sm font-medium text-foreground mb-1",children:x}),(0,t.jsxs)("div",{className:"relative",children:[c&&(0,t.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:c}),(0,t.jsx)("input",{type:i,className:(0,l.cn)(n({variant:b?"error":d,size:o,className:s}),c&&"pl-10",m&&"pr-10"),ref:a,id:g,...p}),m&&(0,t.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:m})]}),(u||f)&&(0,t.jsx)("p",{className:(0,l.cn)("mt-1 text-xs",b?"text-destructive":"text-muted-foreground"),children:u||f})]})});i.displayName="Input"},66695:(e,a,s)=>{s.d(a,{BT:()=>c,Wu:()=>m,ZB:()=>o,Zp:()=>i,aR:()=>d});var t=s(95155),r=s(12115),l=s(59434);let n=(0,s(74466).F)("rounded-lg border bg-card text-card-foreground shadow-sm",{variants:{variant:{default:"border-border",elevated:"shadow-md",outlined:"border-2",ghost:"border-transparent shadow-none"},padding:{none:"",sm:"p-4",default:"p-6",lg:"p-8"}},defaultVariants:{variant:"default",padding:"default"}}),i=r.forwardRef((e,a)=>{let{className:s,variant:r,padding:i,hover:d=!1,...o}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)(n({variant:r,padding:i}),d&&"transition-shadow hover:shadow-md",s),...o})});i.displayName="Card";let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})});d.displayName="CardHeader";let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("h3",{ref:a,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});o.displayName="CardTitle";let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("p",{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let m=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("p-6 pt-0",s),...r})});m.displayName="CardContent",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter"},77890:(e,a,s)=>{s.d(a,{D:()=>i,ThemeProvider:()=>d});var t=s(95155),r=s(12115),l=s(33118);let n=(0,r.createContext)(void 0);function i(){let e=(0,r.useContext)(n);return void 0===e?{theme:"system",setTheme:()=>{},toggleTheme:()=>{},systemTheme:"light",actualTheme:"light"}:e}function d(e){let{children:a,defaultTheme:s="light"}=e,[i,d]=(0,r.useState)(s),[o,c]=(0,r.useState)("light"),[m,u]=(0,r.useState)(!1);(0,r.useEffect)(()=>{d((0,l.zS)());let e=window.matchMedia("(prefers-color-scheme: dark)");c(e.matches?"dark":"light"),u(!0);let a=e=>{c(e.matches?"dark":"light"),"system"===(0,l.zS)()&&(0,l.AZ)("system")};return e.addEventListener("change",a),()=>{e.removeEventListener("change",a)}},[]);let x=e=>{d(e),(0,l.fu)(e),(0,l.AZ)(e)};return(0,t.jsx)(n.Provider,{value:{theme:i,setTheme:x,toggleTheme:()=>{x("light"===("system"===i?o:i)?"dark":"light")},systemTheme:o,actualTheme:"system"===i?o:i},children:a})}},91394:(e,a,s)=>{s.d(a,{BK:()=>o,eu:()=>d,q5:()=>c});var t=s(95155),r=s(12115),l=s(54011),n=s(59434);let i=(0,s(74466).F)("relative flex shrink-0 overflow-hidden rounded-full",{variants:{size:{sm:"h-8 w-8",default:"h-10 w-10",lg:"h-12 w-12",xl:"h-16 w-16","2xl":"h-20 w-20"}},defaultVariants:{size:"default"}}),d=r.forwardRef((e,a)=>{let{className:s,size:r,src:d,alt:m,fallback:u,status:x,...f}=e;return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(l.bL,{ref:a,className:(0,n.cn)(i({size:r}),s),...f,children:[(0,t.jsx)(o,{src:d,alt:m}),(0,t.jsx)(c,{children:u})]}),x&&(0,t.jsx)("div",{className:(0,n.cn)("absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-white",{"bg-green-500":"online"===x,"bg-muted-foreground":"offline"===x,"bg-yellow-500":"away"===x,"bg-red-500":"busy"===x})})]})});d.displayName=l.bL.displayName;let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l._V,{ref:a,className:(0,n.cn)("aspect-square h-full w-full",s),...r})});o.displayName=l._V.displayName;let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.H4,{ref:a,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted text-sm font-medium",s),...r})});c.displayName=l.H4.displayName}}]);