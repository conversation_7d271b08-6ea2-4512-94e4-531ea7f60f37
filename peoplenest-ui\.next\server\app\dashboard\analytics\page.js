(()=>{var e={};e.id=754,e.ids=[754],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4080:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\analytics\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28336:(e,s,r)=>{Promise.resolve().then(r.bind(r,4080))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67726:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d={children:["",{children:["dashboard",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4080)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\analytics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\analytics\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/analytics/page",pathname:"/dashboard/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79526:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>D});var t=r(60687),a=r(43210),n=r(50371),i=r(60),o=r(22737),l=r(80462),d=r(78122),c=r(31158),p=r(25541),m=r(53411),x=r(23928),h=r(86561),u=r(48730),j=r(29523),y=r(44493),g=r(96724),f=r(48482),v=r(61678),b=r(85168),N=r(27747),w=r(19598),P=r(18969),A=r(15202),k=r(66424),C=r(56651),E=r(56988),R=r(25679),_=r(61855),F=r(77814),M=r(2041),T=r(515);let W=[{month:"Jan",employees:1150,newHires:25,departures:8},{month:"Feb",employees:1167,newHires:22,departures:5},{month:"Mar",employees:1184,newHires:28,departures:11},{month:"Apr",employees:1201,newHires:30,departures:13},{month:"May",employees:1218,newHires:27,departures:10},{month:"Jun",employees:1235,newHires:32,departures:15}],B=[{name:"Engineering",employees:485,percentage:39.3,color:"#0088FE"},{name:"Sales",employees:247,percentage:20,color:"#00C49F"},{name:"Marketing",employees:148,percentage:12,color:"#FFBB28"},{name:"Operations",employees:123,percentage:10,color:"#FF8042"},{name:"HR",employees:86,percentage:7,color:"#8884D8"},{name:"Finance",employees:74,percentage:6,color:"#82CA9D"},{name:"Other",employees:72,percentage:5.7,color:"#FFC658"}],K=[{metric:"Employee Satisfaction",current:4.6,previous:4.4,trend:"up"},{metric:"Retention Rate",current:94.2,previous:92.8,trend:"up"},{metric:"Average Performance",current:4.3,previous:4.1,trend:"up"},{metric:"Training Completion",current:87.5,previous:89.2,trend:"down"}],q=[{month:"Jan",totalPayroll:845e4,avgSalary:7348},{month:"Feb",totalPayroll:852e4,avgSalary:7305},{month:"Mar",totalPayroll:868e4,avgSalary:7329},{month:"Apr",totalPayroll:875e4,avgSalary:7287},{month:"May",totalPayroll:889e4,avgSalary:7301},{month:"Jun",totalPayroll:902e4,avgSalary:7306}];function D(){let[e,s]=(0,a.useState)("6months"),r=e=>"up"===e?(0,t.jsx)(i.A,{className:"h-4 w-4 text-green-600"}):(0,t.jsx)(o.A,{className:"h-4 w-4 text-red-600"}),D=e=>"up"===e?"text-green-600":"text-red-600";return(0,t.jsxs)("div",{className:"flex-1 space-y-6 p-6",children:[(0,t.jsx)(g.Y,{title:"Analytics Dashboard",subtitle:"Comprehensive insights into your organization's performance and trends",actions:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("select",{value:e,onChange:e=>s(e.target.value),className:"px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary",children:[(0,t.jsx)("option",{value:"1month",children:"Last Month"}),(0,t.jsx)("option",{value:"3months",children:"Last 3 Months"}),(0,t.jsx)("option",{value:"6months",children:"Last 6 Months"}),(0,t.jsx)("option",{value:"1year",children:"Last Year"})]}),(0,t.jsxs)(j.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"Filter"]}),(0,t.jsxs)(j.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Refresh"]}),(0,t.jsxs)(j.$,{size:"sm",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Export Report"]})]})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:K.map((e,s)=>(0,t.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},children:(0,t.jsx)(y.Zp,{children:(0,t.jsx)(y.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.metric}),(0,t.jsx)("p",{className:"text-2xl font-bold text-foreground",children:e.metric.includes("Rate")||e.metric.includes("Completion")?`${e.current}%`:e.current})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[r(e.trend),(0,t.jsx)("span",{className:`text-sm font-medium ${D(e.trend)}`,children:Math.abs(e.current-e.previous).toFixed(1)})]})]})})})},e.metric))}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)(y.Zp,{children:[(0,t.jsxs)(y.aR,{children:[(0,t.jsxs)(y.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Employee Growth Trends"})]}),(0,t.jsx)(y.BT,{children:"Monthly employee count, new hires, and departures"})]}),(0,t.jsx)(y.Wu,{children:(0,t.jsx)(f.u,{width:"100%",height:300,children:(0,t.jsxs)(v.b,{data:W,children:[(0,t.jsx)(b.d,{strokeDasharray:"3 3"}),(0,t.jsx)(N.W,{dataKey:"month"}),(0,t.jsx)(w.h,{}),(0,t.jsx)(P.m,{}),(0,t.jsx)(A.s,{}),(0,t.jsx)(k.N,{type:"monotone",dataKey:"employees",stroke:"#8884d8",strokeWidth:2,name:"Total Employees"}),(0,t.jsx)(k.N,{type:"monotone",dataKey:"newHires",stroke:"#82ca9d",strokeWidth:2,name:"New Hires"}),(0,t.jsx)(k.N,{type:"monotone",dataKey:"departures",stroke:"#ffc658",strokeWidth:2,name:"Departures"})]})})})]}),(0,t.jsxs)(y.Zp,{children:[(0,t.jsxs)(y.aR,{children:[(0,t.jsxs)(y.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Department Distribution"})]}),(0,t.jsx)(y.BT,{children:"Employee distribution across departments"})]}),(0,t.jsx)(y.Wu,{children:(0,t.jsx)(f.u,{width:"100%",height:300,children:(0,t.jsxs)(C.r,{children:[(0,t.jsx)(E.F,{data:B,cx:"50%",cy:"50%",labelLine:!1,label:({name:e,percentage:s})=>`${e} ${s}%`,outerRadius:80,fill:"#8884d8",dataKey:"employees",children:B.map((e,s)=>(0,t.jsx)(R.f,{fill:e.color},`cell-${s}`))}),(0,t.jsx)(P.m,{})]})})})]}),(0,t.jsxs)(y.Zp,{children:[(0,t.jsxs)(y.aR,{children:[(0,t.jsxs)(y.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Payroll Analytics"})]}),(0,t.jsx)(y.BT,{children:"Total payroll and average salary trends"})]}),(0,t.jsx)(y.Wu,{children:(0,t.jsx)(f.u,{width:"100%",height:300,children:(0,t.jsxs)(_.Q,{data:q,children:[(0,t.jsx)(b.d,{strokeDasharray:"3 3"}),(0,t.jsx)(N.W,{dataKey:"month"}),(0,t.jsx)(w.h,{yAxisId:"left"}),(0,t.jsx)(w.h,{yAxisId:"right",orientation:"right"}),(0,t.jsx)(P.m,{}),(0,t.jsx)(A.s,{}),(0,t.jsx)(F.Gk,{yAxisId:"left",type:"monotone",dataKey:"totalPayroll",stackId:"1",stroke:"#8884d8",fill:"#8884d8",name:"Total Payroll"}),(0,t.jsx)(k.N,{yAxisId:"right",type:"monotone",dataKey:"avgSalary",stroke:"#82ca9d",strokeWidth:2,name:"Avg Salary"})]})})})]}),(0,t.jsxs)(y.Zp,{children:[(0,t.jsxs)(y.aR,{children:[(0,t.jsxs)(y.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Department Performance"})]}),(0,t.jsx)(y.BT,{children:"Performance metrics by department"})]}),(0,t.jsx)(y.Wu,{children:(0,t.jsx)(f.u,{width:"100%",height:300,children:(0,t.jsxs)(M.E,{data:B,children:[(0,t.jsx)(b.d,{strokeDasharray:"3 3"}),(0,t.jsx)(N.W,{dataKey:"name"}),(0,t.jsx)(w.h,{}),(0,t.jsx)(P.m,{}),(0,t.jsx)(T.y,{dataKey:"employees",fill:"#8884d8"})]})})})]})]}),(0,t.jsxs)(y.Zp,{children:[(0,t.jsxs)(y.aR,{children:[(0,t.jsx)(y.ZB,{children:"Key Insights"}),(0,t.jsx)(y.BT,{children:"AI-powered insights from your data"})]}),(0,t.jsx)(y.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg",children:[(0,t.jsx)(p.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:"Employee Growth Acceleration"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Your hiring rate has increased by 23% compared to last quarter, with Engineering leading the growth."})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-4 bg-green-50 dark:bg-green-950/20 rounded-lg",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-green-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:"Improved Retention"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Employee retention has improved by 1.4% this quarter, indicating better job satisfaction."})]})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-4 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-foreground",children:"Training Completion Decline"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Training completion rates have decreased by 1.7%. Consider reviewing training programs."})]})]})]})})]})]})}},79551:e=>{"use strict";e.exports=require("url")},86480:(e,s,r)=>{Promise.resolve().then(r.bind(r,79526))}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,912,18,722,632,251,203,835,650,94,809,456],()=>r(67726));module.exports=t})();