import { DatabaseService } from '../services/databaseService';

async function clearSessions() {
  const db = new DatabaseService();
  
  try {
    console.log('Clearing all user sessions...');
    
    const result = await db.query('DELETE FROM user_sessions');
    console.log(`✅ Cleared ${result.rowCount} sessions`);
    
    // Also clear any refresh tokens stored by JWT service
    const refreshTokenResult = await db.query('DELETE FROM refresh_tokens');
    console.log(`✅ Cleared ${refreshTokenResult.rowCount} refresh tokens`);
    
  } catch (error) {
    console.error('Error clearing sessions:', error.message);
  }
}

clearSessions();
