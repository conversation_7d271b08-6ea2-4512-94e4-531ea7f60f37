
// Role utility functions for formatting and display

import { UserRole } from './auth'

/**
 * Format role for display in UI
 * Converts technical role names to user-friendly display names
 */
export function formatRole(role: UserRole | string | null | undefined): string {
  const roleMap: Record<string, string> = {
    'super_admin': 'Super Admin',
    'system_admin': 'System Admin',
    'hr_admin': 'HR Admin',
    'hr': 'HR',
    'manager': 'Manager',
    'employee': 'Employee',
    'recruiter': 'Recruiter',
    'payroll': 'Payroll',
    'it': 'IT'
  }
  
  if (!role) return 'Employee'
  return roleMap[role] || 'Employee'
}

/**
 * Get role color for UI styling
 * Returns appropriate color classes for different roles
 */
export function getRoleColor(role: UserRole | string | null | undefined): string {
  const colorMap: Record<string, string> = {
    'super_admin': 'text-red-600 bg-red-500/10 border-red-500/20 dark:text-red-400',
    'system_admin': 'text-purple-600 bg-purple-500/10 border-purple-500/20 dark:text-purple-400',
    'hr_admin': 'text-blue-600 bg-blue-500/10 border-blue-500/20 dark:text-blue-400',
    'hr': 'text-green-600 bg-green-500/10 border-green-500/20 dark:text-green-400',
    'manager': 'text-orange-600 bg-orange-500/10 border-orange-500/20 dark:text-orange-400',
    'employee': 'text-muted-foreground bg-muted border-border',
    'recruiter': 'text-indigo-600 bg-indigo-500/10 border-indigo-500/20 dark:text-indigo-400',
    'payroll': 'text-yellow-600 bg-yellow-500/10 border-yellow-500/20 dark:text-yellow-400',
    'it': 'text-cyan-600 bg-cyan-500/10 border-cyan-500/20 dark:text-cyan-400'
  }
  
  if (!role) return colorMap['employee']
  return colorMap[role] || colorMap['employee']
}

/**
 * Get role badge component props
 * Returns formatted role name and styling for badge display
 */
export function getRoleBadgeProps(role: UserRole | string | null | undefined) {
  return {
    text: formatRole(role),
    className: getRoleColor(role)
  }
}

/**
 * Check if role is admin level (hr_admin, system_admin, super_admin)
 */
export function isAdminRole(role: UserRole | string | null | undefined): boolean {
  const adminRoles = ['super_admin', 'system_admin', 'hr_admin']
  return role ? adminRoles.includes(role) : false
}

/**
 * Get role hierarchy level (higher number = more permissions)
 */
export function getRoleLevel(role: UserRole | string | null | undefined): number {
  const roleLevels: Record<string, number> = {
    'employee': 1,
    'recruiter': 2,
    'payroll': 2,
    'it': 2,
    'hr': 3,
    'manager': 4,
    'hr_admin': 5,
    'system_admin': 6,
    'super_admin': 7
  }
  
  if (!role) return 1
  return roleLevels[role] || 1
}

/**
 * Compare if first role has higher or equal level than second role
 */
export function hasRoleLevel(userRole: UserRole | string | null | undefined, requiredRole: UserRole | string): boolean {
  return getRoleLevel(userRole) >= getRoleLevel(requiredRole)
}

