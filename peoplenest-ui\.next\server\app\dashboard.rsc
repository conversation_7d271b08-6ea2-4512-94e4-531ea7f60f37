1:"$Sreact.fragment"
2:I[77890,["7177","static/chunks/app/layout-c92ee0b72061c2db.js"],"ThemeProvider"]
3:I[87555,[],""]
4:I[31295,[],""]
5:I[94970,[],"ClientSegmentRoot"]
6:I[14554,["3706","static/chunks/3706-cf1b45900489dd08.js","9380","static/chunks/9380-7613ed5e11498760.js","6110","static/chunks/6110-751ccb77ac99a062.js","1008","static/chunks/1008-532cfd310dc87f4b.js","1712","static/chunks/1712-af7c6ff02313415f.js","1954","static/chunks/app/dashboard/layout-06c4410ec1687c19.js"],"default"]
8:I[90894,[],"ClientPageRoot"]
9:I[44517,["3706","static/chunks/3706-cf1b45900489dd08.js","9380","static/chunks/9380-7613ed5e11498760.js","6110","static/chunks/6110-751ccb77ac99a062.js","1647","static/chunks/1647-5e550e615631e3f8.js","7463","static/chunks/7463-9d8b07efdea11f37.js","8162","static/chunks/8162-32bd8a54d101c0a4.js","5504","static/chunks/5504-4cd4575415ab9dd0.js","1157","static/chunks/1157-16ae416dd5c89949.js","1712","static/chunks/1712-af7c6ff02313415f.js","5409","static/chunks/5409-5caae92974b57487.js","5105","static/chunks/app/dashboard/page-55169c5e3678819c.js"],"default"]
c:I[59665,[],"OutletBoundary"]
f:I[74911,[],"AsyncMetadataOutlet"]
11:I[59665,[],"ViewportBoundary"]
13:I[59665,[],"MetadataBoundary"]
15:I[26614,[],""]
:HL["/_next/static/css/56cc0dd3233d2018.css","style"]
0:{"P":null,"b":"pDhezjYFuvgxkHqbZem0i","p":"","c":["","dashboard"],"i":false,"f":[[["",{"children":["dashboard",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/56cc0dd3233d2018.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":[["$","head",null,{"children":["$","script",null,{"dangerouslySetInnerHTML":{"__html":"\n              (function() {\n                try {\n                  var theme = localStorage.getItem('theme-mode') || 'light';\n                  var root = document.documentElement;\n\n                  // Apply theme immediately to prevent hydration mismatch\n                  if (theme === 'system') {\n                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + systemTheme;\n                  } else {\n                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + theme;\n                  }\n                } catch (e) {\n                  // Fallback to light theme if anything goes wrong\n                  document.documentElement.className = document.documentElement.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' light';\n                }\n              })();\n            "}}]}],["$","body",null,{"className":"__variable_e8ce0c __variable_3c557b font-sans antialiased touch-manipulation","children":[["$","script",null,{"dangerouslySetInnerHTML":{"__html":"\n              (function() {\n                try {\n                  var theme = localStorage.getItem('theme-mode') || 'light';\n                  var root = document.documentElement;\n\n                  // Apply theme immediately to prevent hydration mismatch\n                  if (theme === 'system') {\n                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + systemTheme;\n                  } else {\n                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + theme;\n                  }\n                } catch (e) {\n                  // Fallback to light theme if anything goes wrong\n                  document.documentElement.className = document.documentElement.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' light';\n                }\n              })();\n            "}}],["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","script",null,{"dangerouslySetInnerHTML":{"__html":"\n              if ('serviceWorker' in navigator) {\n                window.addEventListener('load', function() {\n                  navigator.serviceWorker.register('/sw.js')\n                    .then(function(registration) {\n                      console.log('SW registered: ', registration);\n                    })\n                    .catch(function(registrationError) {\n                      console.log('SW registration failed: ', registrationError);\n                    });\n                });\n              }\n            "}}]]}]]}]]}],{"children":["dashboard",["$","$1","c",{"children":[null,["$","$L5",null,{"Component":"$6","slots":{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]},"params":{},"promise":"$@7"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L8",null,{"Component":"$9","searchParams":{},"params":"$0:f:0:1:2:children:1:props:children:1:props:params","promises":["$@a","$@b"]}],null,["$","$Lc",null,{"children":["$Ld","$Le",["$","$Lf",null,{"promise":"$@10"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","AqQwWgYfkaLK_DPVZztWFv",{"children":[["$","$L11",null,{"children":"$L12"}],null]}],["$","$L13",null,{"children":"$L14"}]]}],false]],"m":"$undefined","G":["$15","$undefined"],"s":false,"S":true}
16:"$Sreact.suspense"
17:I[74911,[],"AsyncMetadata"]
7:{}
a:{}
b:{}
14:["$","div",null,{"hidden":true,"children":["$","$16",null,{"fallback":null,"children":["$","$L17",null,{"promise":"$@18"}]}]}]
e:null
12:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1, maximum-scale=1, viewport-fit=cover, user-scalable=no"}],["$","meta","2",{"name":"theme-color","content":"#3b82f6"}]]
d:null
10:{"metadata":[["$","title","0",{"children":"PeopleNest - Enterprise HRMS Platform"}],["$","meta","1",{"name":"description","content":"Modern, AI-powered Human Resource Management System for enterprise organizations"}],["$","link","2",{"rel":"manifest","href":"/manifest.json","crossOrigin":"$undefined"}],["$","meta","3",{"name":"keywords","content":"HRMS,HR,Human Resources,Employee Management,Payroll,Performance"}],["$","meta","4",{"name":"mobile-web-app-capable","content":"yes"}],["$","meta","5",{"name":"apple-mobile-web-app-capable","content":"yes"}],["$","meta","6",{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","meta","7",{"name":"apple-mobile-web-app-title","content":"PeopleNest"}],["$","meta","8",{"name":"application-name","content":"PeopleNest HRMS"}],["$","meta","9",{"name":"msapplication-TileColor","content":"#3b82f6"}],["$","meta","10",{"name":"msapplication-config","content":"/browserconfig.xml"}],["$","meta","11",{"name":"format-detection","content":"telephone=no"}],["$","meta","12",{"name":"mobile-web-app-capable","content":"yes"}],["$","meta","13",{"name":"apple-mobile-web-app-title","content":"PeopleNest HRMS"}],["$","meta","14",{"name":"apple-mobile-web-app-status-bar-style","content":"default"}],["$","link","15",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
18:{"metadata":"$10:metadata","error":null,"digest":"$undefined"}
