"use strict";exports.id=94,exports.ids=[94],exports.modules={60:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},15202:(e,t,r)=>{r.d(t,{s:()=>D});var n=r(43210),a=r(51215),i=r(14221),l=r(49384),o=r(10687),c=r.n(o),s=r(21080),u=r(10919),p=r(4057);function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class f extends n.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,a=32/6,i=32/3,l=e.inactive?r:e.color,o=null!=t?t:e.type;if("none"===o)return null;if("plainline"===o)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:l,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===o)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:l,d:"M0,".concat(16,"h").concat(i,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(2*i,",").concat(16,"\n            H").concat(32,"M").concat(2*i,",").concat(16,"\n            A").concat(a,",").concat(a,",0,1,1,").concat(i,",").concat(16),className:"recharts-legend-icon"});if("rect"===o)return n.createElement("path",{stroke:"none",fill:l,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(e.legendIcon)){var c=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete c.legendIcon,n.cloneElement(e.legendIcon,c)}return n.createElement(u.i,{fill:l,cx:16,cy:16,size:32,sizeType:"diameter",type:o})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:a,inactiveColor:i,iconType:o,itemSorter:u}=this.props,h={x:0,y:0,width:32,height:32},y={display:"horizontal"===r?"inline-block":"block",marginRight:10},f={display:"inline-block",verticalAlign:"middle",marginRight:4};return(u?c()(e,u):e).map((e,r)=>{var c=e.formatter||a,u=(0,l.$)({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var m=e.inactive?i:e.color,v=c?c(e.value,e,r):e.value;return n.createElement("li",d({className:u,style:y,key:"legend-item-".concat(r)},(0,p.XC)(this.props,e,r)),n.createElement(s.u,{width:t,height:t,viewBox:h,style:f,"aria-label":"".concat(v," legend icon")},this.renderIcon(e,o)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:m}},v))})}render(){var{payload:e,layout:t,align:r}=this.props;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?r:"left"}},this.renderItems()):null}}y(f,"displayName","Legend"),y(f,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",itemSorter:"value",layout:"horizontal",verticalAlign:"middle"});var m=r(22989),v=r(45796),g=r(43209),b=r(23337),x=r(68392),O=r(51426);r(53044);var E=["contextPayload"];function P(){return(P=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function j(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?j(Object(r),!0).forEach(function(t){w(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function k(e){return e.value}function I(e){var{contextPayload:t}=e,r=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,E),a=(0,v.s)(t,e.payloadUniqBy,k),i=A(A({},r),{},{payload:a});return n.isValidElement(e.content)?n.cloneElement(e.content,i):"function"==typeof e.content?n.createElement(e.content,i):n.createElement(f,i)}function M(e){return(0,g.j)(),null}function N(e){return(0,g.j)(),null}function C(e){var t=(0,g.G)(b.g0),r=(0,i.M)(),l=(0,O.Kp)(),{width:o,height:c,wrapperStyle:s,portal:u}=e,[p,d]=(0,x.V)([t]),h=(0,O.yi)(),y=(0,O.rY)(),f=h-(l.left||0)-(l.right||0),m=D.getWidthOrHeight(e.layout,c,o,f),v=u?s:A(A({position:"absolute",width:(null==m?void 0:m.width)||o||"auto",height:(null==m?void 0:m.height)||c||"auto"},function(e,t,r,n,a,i){var l,o,{layout:c,align:s,verticalAlign:u}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(l="center"===s&&"vertical"===c?{left:((n||0)-i.width)/2}:"right"===s?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(o="middle"===u?{top:((a||0)-i.height)/2}:"bottom"===u?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),A(A({},l),o)}(s,e,l,h,y,p)),s),E=null!=u?u:r;if(null==E)return null;var j=n.createElement("div",{className:"recharts-legend-wrapper",style:v,ref:d},n.createElement(M,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign}),n.createElement(N,{width:p.width,height:p.height}),n.createElement(I,P({},e,m,{margin:l,chartWidth:h,chartHeight:y,contextPayload:t})));return(0,a.createPortal)(j,E)}class D extends n.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&(0,m.Et)(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return n.createElement(C,this.props)}}w(D,"displayName","Legend"),w(D,"defaultProps",{align:"center",iconSize:14,layout:"horizontal",verticalAlign:"bottom"})},22737:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]])},61855:(e,t,r)=>{r.d(t,{Q:()=>o});var n=r(43210),a=r(49605),i=r(54024),l=["axis"],o=(0,n.forwardRef)((e,t)=>n.createElement(i.P,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:l,tooltipPayloadSearcher:a.uN,categoricalChartProps:e,ref:t}))},77814:(e,t,r)=>{r.d(t,{Gk:()=>ei,Vf:()=>ea});var n=r(43210),a=r(49384),i=r(81888),l=r(95530),o=r(98986),c=r(98845),s=r(20237),u=r(22989),p=r(64279),d=r(54186),h=r(81730),y=r(37625),f=r(4236),m=r(46993),v=r(84648),g=r(85621),b=r(51426),x=r(57282),O=(e,t,r,n)=>(0,g.Gx)(e,"xAxis",t,n),E=(e,t,r,n)=>(0,g.CR)(e,"xAxis",t,n),P=(e,t,r,n)=>(0,g.Gx)(e,"yAxis",r,n),j=(e,t,r,n)=>(0,g.CR)(e,"yAxis",r,n),A=(0,v.Mz)([b.fz,O,P,E,j],(e,t,r,n,a)=>(0,p._L)(e,"xAxis")?(0,p.Hj)(t,n,!1):(0,p.Hj)(r,a,!1)),w=(0,v.Mz)([g.ld,(e,t,r,n,a)=>a],(e,t)=>{if(e.some(e=>"area"===e.type&&t.dataKey===e.dataKey&&(0,p.$8)(t.stackId)===e.stackId&&t.data===e.data))return t}),k=(0,v.Mz)([b.fz,O,P,E,j,(e,t,r,n,a)=>{var i,l,o=(0,b.fz)(e);if(null!=(l=(0,p._L)(o,"xAxis")?(0,g.TC)(e,"yAxis",r,n):(0,g.TC)(e,"xAxis",t,n))){var{dataKey:c,stackId:s}=a;if(null!=s){var u=null==(i=l[s])?void 0:i.stackedData;return null==u?void 0:u.find(e=>e.key===c)}}},x.HS,A,w],(e,t,r,n,a,i,l,o,c)=>{var s,{chartData:u,dataStartIndex:p,dataEndIndex:d}=l;if(null!=c&&("horizontal"===e||"vertical"===e)&&null!=t&&null!=r&&null!=n&&null!=a&&0!==n.length&&0!==a.length&&null!=o){var{data:h}=c;if(null!=(s=h&&h.length>0?h:null==u?void 0:u.slice(p,d+1)))return ea({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:a,dataStartIndex:p,areaSettings:c,stackedData:i,displayedData:s,chartBaseValue:void 0,bandSize:o})}}),I=r(83409),M=r(21426),N=r(14956),C=r(43209),D=r(36304),S=r(73865),W=r(12128),z=r(19420),L=["layout","type","stroke","connectNulls","isRange"],T=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function K(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function H(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?H(Object(r),!0).forEach(function(t){G(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function G(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function V(){return(V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function B(e,t){return e&&"none"!==e?e:t}var J=e=>{var{dataKey:t,name:r,stroke:n,fill:a,legendType:i,hide:l}=e;return[{inactive:l,dataKey:t,type:i,color:B(n,a),value:(0,p.uM)(r,t),payload:e}]};function $(e){var{dataKey:t,data:r,stroke:n,strokeWidth:a,fill:i,name:l,hide:o,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:a,fill:i,dataKey:t,nameKey:void 0,name:(0,p.uM)(l,t),hide:o,type:e.tooltipType,color:B(n,i),unit:c}}}var F=(e,t)=>{var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var i=(0,a.$)("recharts-area-dot","boolean"!=typeof e?e.className:"");r=n.createElement(l.c,V({},t,{className:i}))}return r};function _(e){var{clipPathId:t,points:r,props:a}=e,{needClip:i,dot:l,dataKey:c}=a;if(null==r||!l&&1!==r.length)return null;var s=(0,d.y$)(l),u=(0,d.J9)(a,!1),p=(0,d.J9)(l,!0),h=r.map((e,t)=>F(l,R(R(R({key:"dot-".concat(t),r:3},u),p),{},{index:t,cx:e.x,cy:e.y,dataKey:c,value:e.value,payload:e.payload,points:r}))),y={clipPath:i?"url(#clipPath-".concat(s?"":"dots-").concat(t,")"):void 0};return n.createElement(o.W,V({className:"recharts-area-dots"},y),h)}function Q(e){var{points:t,baseLine:r,needClip:a,clipPathId:l,props:s,showLabels:u}=e,{layout:p,type:h,stroke:y,connectNulls:f,isRange:m}=s,v=K(s,L);return n.createElement(n.Fragment,null,(null==t?void 0:t.length)>1&&n.createElement(o.W,{clipPath:a?"url(#clipPath-".concat(l,")"):void 0},n.createElement(i.I,V({},(0,d.J9)(v,!0),{points:t,connectNulls:f,type:h,baseLine:r,layout:p,stroke:"none",className:"recharts-area-area"})),"none"!==y&&n.createElement(i.I,V({},(0,d.J9)(s,!1),{className:"recharts-area-curve",layout:p,type:h,connectNulls:f,fill:"none",points:t})),"none"!==y&&m&&n.createElement(i.I,V({},(0,d.J9)(s,!1),{className:"recharts-area-curve",layout:p,type:h,connectNulls:f,fill:"none",points:r}))),n.createElement(_,{points:t,props:s,clipPathId:l}),u&&c.Z.renderCallByParent(s,t))}function X(e){var{alpha:t,baseLine:r,points:a,strokeWidth:i}=e,l=a[0].y,o=a[a.length-1].y;if(!(0,W.H)(l)||!(0,W.H)(o))return null;var c=t*Math.abs(l-o),s=Math.max(...a.map(e=>e.x||0));return((0,u.Et)(r)?s=Math.max(r,s):r&&Array.isArray(r)&&r.length&&(s=Math.max(...r.map(e=>e.x||0),s)),(0,u.Et)(s))?n.createElement("rect",{x:0,y:l<o?l:l-c,width:s+(i?parseInt("".concat(i),10):1),height:Math.floor(c)}):null}function q(e){var{alpha:t,baseLine:r,points:a,strokeWidth:i}=e,l=a[0].x,o=a[a.length-1].x;if(!(0,W.H)(l)||!(0,W.H)(o))return null;var c=t*Math.abs(l-o),s=Math.max(...a.map(e=>e.y||0));return((0,u.Et)(r)?s=Math.max(r,s):r&&Array.isArray(r)&&r.length&&(s=Math.max(...r.map(e=>e.y||0),s)),(0,u.Et)(s))?n.createElement("rect",{x:l<o?l:l-c,y:0,width:c,height:Math.floor(s+(i?parseInt("".concat(i),10):1))}):null}function U(e){var{alpha:t,layout:r,points:a,baseLine:i,strokeWidth:l}=e;return"vertical"===r?n.createElement(X,{alpha:t,points:a,baseLine:i,strokeWidth:l}):n.createElement(q,{alpha:t,points:a,baseLine:i,strokeWidth:l})}function Y(e){var{needClip:t,clipPathId:r,props:a,previousPointsRef:i,previousBaselineRef:l}=e,{points:c,baseLine:s,isAnimationActive:p,animationBegin:d,animationDuration:h,animationEasing:y,onAnimationStart:f,onAnimationEnd:m}=a,v=(0,D.n)(a,"recharts-area-"),[g,b]=(0,n.useState)(!0),x=(0,n.useCallback)(()=>{"function"==typeof m&&m(),b(!1)},[m]),O=(0,n.useCallback)(()=>{"function"==typeof f&&f(),b(!0)},[f]),E=i.current,P=l.current;return n.createElement(z.i,{begin:d,duration:h,isActive:p,easing:y,from:{t:0},to:{t:1},onAnimationEnd:x,onAnimationStart:O,key:v},e=>{var{t:p}=e;if(E){var d,h=E.length/c.length,y=1===p?c:c.map((e,t)=>{var r=Math.floor(t*h);if(E[r]){var n=E[r];return R(R({},e),{},{x:(0,u.GW)(n.x,e.x,p),y:(0,u.GW)(n.y,e.y,p)})}return e});return d=(0,u.Et)(s)?(0,u.GW)(P,s,p):(0,u.uy)(s)||(0,u.M8)(s)?(0,u.GW)(P,0,p):s.map((e,t)=>{var r=Math.floor(t*h);if(Array.isArray(P)&&P[r]){var n=P[r];return R(R({},e),{},{x:(0,u.GW)(n.x,e.x,p),y:(0,u.GW)(n.y,e.y,p)})}return e}),p>0&&(i.current=y,l.current=d),n.createElement(Q,{points:y,baseLine:d,needClip:t,clipPathId:r,props:a,showLabels:!g})}return p>0&&(i.current=c,l.current=s),n.createElement(o.W,null,n.createElement("defs",null,n.createElement("clipPath",{id:"animationClipPath-".concat(r)},n.createElement(U,{alpha:p,points:c,baseLine:s,layout:a.layout,strokeWidth:a.strokeWidth}))),n.createElement(o.W,{clipPath:"url(#animationClipPath-".concat(r,")")},n.createElement(Q,{points:c,baseLine:s,needClip:t,clipPathId:r,props:a,showLabels:!0})))})}function Z(e){var{needClip:t,clipPathId:r,props:a}=e,{points:i,baseLine:l,isAnimationActive:o}=a,c=(0,n.useRef)(null),s=(0,n.useRef)(),u=c.current,p=s.current;return o&&i&&i.length&&(u!==i||p!==l)?n.createElement(Y,{needClip:t,clipPathId:r,props:a,previousPointsRef:c,previousBaselineRef:s}):n.createElement(Q,{points:i,baseLine:l,needClip:t,clipPathId:r,props:a,showLabels:!0})}class ee extends n.PureComponent{constructor(){super(...arguments),G(this,"id",(0,u.NF)("recharts-area-"))}render(){var e,{hide:t,dot:r,points:i,className:l,top:c,left:s,needClip:p,xAxisId:y,yAxisId:f,width:v,height:g,id:b,baseLine:x}=this.props;if(t)return null;var O=(0,a.$)("recharts-area",l),E=(0,u.uy)(b)?this.id:b,{r:P=3,strokeWidth:j=2}=null!=(e=(0,d.J9)(r,!1))?e:{r:3,strokeWidth:2},A=(0,d.y$)(r),w=2*P+j;return n.createElement(n.Fragment,null,n.createElement(o.W,{className:O},p&&n.createElement("defs",null,n.createElement(m.Q,{clipPathId:E,xAxisId:y,yAxisId:f}),!A&&n.createElement("clipPath",{id:"clipPath-dots-".concat(E)},n.createElement("rect",{x:s-w/2,y:c-w/2,width:v+w,height:g+w}))),n.createElement(Z,{needClip:p,clipPathId:E,props:this.props})),n.createElement(h.W,{points:i,mainColor:B(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(x)&&n.createElement(h.W,{points:x,mainColor:B(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}var et={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!s.m.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function er(e){var t,r=(0,S.e)(e,et),{activeDot:a,animationBegin:i,animationDuration:l,animationEasing:o,connectNulls:c,dot:s,fill:u,fillOpacity:p,hide:d,isAnimationActive:h,legendType:y,stroke:f,xAxisId:v,yAxisId:g}=r,x=K(r,T),O=(0,b.WX)(),E=(0,M.fW)(),{needClip:P}=(0,m.l)(v,g),j=(0,I.r)(),A=(0,n.useMemo)(()=>({baseValue:e.baseValue,stackId:e.stackId,connectNulls:c,data:e.data,dataKey:e.dataKey}),[e.baseValue,e.stackId,c,e.data,e.dataKey]),{points:w,isRange:N,baseLine:D}=null!=(t=(0,C.G)(e=>k(e,v,g,j,A)))?t:{},{height:W,width:z,left:L,top:H}=(0,b.hj)();return"horizontal"!==O&&"vertical"!==O||"AreaChart"!==E&&"ComposedChart"!==E?null:n.createElement(ee,V({},x,{activeDot:a,animationBegin:i,animationDuration:l,animationEasing:o,baseLine:D,connectNulls:c,dot:s,fill:u,fillOpacity:p,height:W,hide:d,layout:O,isAnimationActive:h,isRange:N,legendType:y,needClip:P,points:w,stroke:f,width:z,left:L,top:H,xAxisId:v,yAxisId:g}))}var en=(e,t,r,n,a)=>{var i=null!=r?r:t;if((0,u.Et)(i))return i;var l="horizontal"===e?a:n,o=l.scale.domain();if("number"===l.type){var c=Math.max(o[0],o[1]),s=Math.min(o[0],o[1]);return"dataMin"===i?s:"dataMax"===i||c<0?c:Math.max(Math.min(o[0],o[1]),0)}return"dataMin"===i?o[0]:"dataMax"===i?o[1]:o[0]};function ea(e){var t,{areaSettings:{connectNulls:r,baseValue:n,dataKey:a},stackedData:i,layout:l,chartBaseValue:o,xAxis:c,yAxis:s,displayedData:u,dataStartIndex:d,xAxisTicks:h,yAxisTicks:y,bandSize:f}=e,m=i&&i.length,v=en(l,o,n,c,s),g="horizontal"===l,b=!1,x=u.map((e,t)=>{m?n=i[d+t]:Array.isArray(n=(0,p.kr)(e,a))?b=!0:n=[v,n];var n,l=null==n[1]||m&&!r&&null==(0,p.kr)(e,a);return g?{x:(0,p.nb)({axis:c,ticks:h,bandSize:f,entry:e,index:t}),y:l?null:s.scale(n[1]),value:n,payload:e}:{x:l?null:c.scale(n[1]),y:(0,p.nb)({axis:s,ticks:y,bandSize:f,entry:e,index:t}),value:n,payload:e}});return t=m||b?x.map(e=>{var t=Array.isArray(e.value)?e.value[0]:null;return g?{x:e.x,y:null!=t&&null!=e.y?s.scale(t):null}:{x:null!=t?c.scale(t):null,y:e.y}}):g?s.scale(v):c.scale(v),{points:x,baseLine:t,isRange:b}}class ei extends n.PureComponent{render(){return n.createElement(f._S,{type:"area",data:this.props.data,dataKey:this.props.dataKey,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,stackId:this.props.stackId,hide:this.props.hide,barSize:void 0},n.createElement(N.A,{legendPayload:J(this.props)}),n.createElement(y.r,{fn:$,args:this.props}),n.createElement(er,this.props))}}G(ei,"displayName","Area"),G(ei,"defaultProps",et)}};