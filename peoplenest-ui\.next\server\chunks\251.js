exports.id=251,exports.ids=[251],exports.modules={175:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isPlainObject=function(t){if(!t||"object"!=typeof t)return!1;let e=Object.getPrototypeOf(t);return(null===e||e===Object.prototype||null===Object.getPrototypeOf(e))&&"[object Object]"===Object.prototype.toString.call(t)}},1640:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isObject=function(t){return null!==t&&("object"==typeof t||"function"==typeof t)}},1706:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.eq=function(t,e){return t===e||Number.isNaN(t)&&Number.isNaN(e)}},2264:(t,e,r)=>{"use strict";r.d(e,{L:()=>N});var n=r(43210),i=r(54186),a=r(51426),o=r(24028),l=r(83409),c=r(21080),u=r(43209),s=r(94728),f=r(12128),h=["children"];function d(){return(d=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var p={width:"100%",height:"100%"},y=(0,n.forwardRef)((t,e)=>{var r,i,l=(0,a.yi)(),u=(0,a.rY)(),s=(0,o.$)();if(!(0,f.F)(l)||!(0,f.F)(u))return null;var{children:h,otherAttributes:y,title:v,desc:g}=t;return r="number"==typeof y.tabIndex?y.tabIndex:s?0:void 0,i="string"==typeof y.role?y.role:s?"application":void 0,n.createElement(c.u,d({},y,{title:v,desc:g,role:i,tabIndex:r,width:l,height:u,style:p,ref:e}),h)}),v=t=>{var{children:e}=t,r=(0,u.G)(s.U);if(!r)return null;var{width:i,height:a,y:o,x:l}=r;return n.createElement(c.u,{width:i,height:a,x:l,y:o},e)},g=(0,n.forwardRef)((t,e)=>{var{children:r}=t,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,h);return(0,l.r)()?n.createElement(v,null,r):n.createElement(y,d({ref:e},i),r)}),m=r(49384),b=r(17118),x=r(85407),w=r(98009),O=r(11281),j=r(86445);r(52693);var P=r(44919),M=r(34258),S=r(97711),A=r(14221);function E(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var _=(0,n.forwardRef)((t,e)=>{var{children:r,className:i,height:a,onClick:o,onContextMenu:l,onDoubleClick:c,onMouseDown:s,onMouseEnter:f,onMouseLeave:h,onMouseMove:d,onMouseUp:p,onTouchEnd:y,onTouchMove:v,onTouchStart:g,style:_,width:k}=t,T=(0,u.j)(),[C,D]=(0,n.useState)(null),[N,I]=(0,n.useState)(null);(0,w.l3)();var z=function(){(0,u.j)();var[t,e]=(0,n.useState)(null);return(0,u.G)(j.et),e}(),L=(0,n.useCallback)(t=>{z(t),"function"==typeof e&&e(t),D(t),I(t)},[z,e,D,I]),$=(0,n.useCallback)(t=>{T((0,x.ky)(t)),T((0,P.y)({handler:o,reactEvent:t}))},[T,o]),U=(0,n.useCallback)(t=>{T((0,x.dj)(t)),T((0,P.y)({handler:f,reactEvent:t}))},[T,f]),R=(0,n.useCallback)(t=>{T((0,b.xS)()),T((0,P.y)({handler:h,reactEvent:t}))},[T,h]),B=(0,n.useCallback)(t=>{T((0,x.dj)(t)),T((0,P.y)({handler:d,reactEvent:t}))},[T,d]),F=(0,n.useCallback)(()=>{T((0,O.Ru)())},[T]),G=(0,n.useCallback)(t=>{T((0,O.uZ)(t.key))},[T]),K=(0,n.useCallback)(t=>{T((0,P.y)({handler:l,reactEvent:t}))},[T,l]),H=(0,n.useCallback)(t=>{T((0,P.y)({handler:c,reactEvent:t}))},[T,c]),W=(0,n.useCallback)(t=>{T((0,P.y)({handler:s,reactEvent:t}))},[T,s]),Z=(0,n.useCallback)(t=>{T((0,P.y)({handler:p,reactEvent:t}))},[T,p]),q=(0,n.useCallback)(t=>{T((0,P.y)({handler:g,reactEvent:t}))},[T,g]),V=(0,n.useCallback)(t=>{T((0,M.e)(t)),T((0,P.y)({handler:v,reactEvent:t}))},[T,v]),Y=(0,n.useCallback)(t=>{T((0,P.y)({handler:y,reactEvent:t}))},[T,y]);return n.createElement(S.$.Provider,{value:C},n.createElement(A.t.Provider,{value:N},n.createElement("div",{className:(0,m.$)("recharts-wrapper",i),style:function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?E(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({position:"relative",cursor:"default",width:k,height:a},_),role:"application",onClick:$,onContextMenu:K,onDoubleClick:H,onFocus:F,onKeyDown:G,onMouseDown:W,onMouseEnter:U,onMouseLeave:R,onMouseMove:B,onMouseUp:Z,onTouchEnd:Y,onTouchMove:V,onTouchStart:q,ref:L},r)))}),k=r(22989),T=(0,n.createContext)(void 0),C=t=>{var{children:e}=t,[r]=(0,n.useState)("".concat((0,k.NF)("recharts"),"-clip")),i=(0,a.hj)();if(null==i)return null;var{left:o,top:l,height:c,width:u}=i;return n.createElement(T.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:o,y:l,height:c,width:u}))),e)},D=["children","className","width","height","style","compact","title","desc"],N=(0,n.forwardRef)((t,e)=>{var{children:r,className:a,width:o,height:l,style:c,compact:u,title:s,desc:f}=t,h=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,D),d=(0,i.J9)(h,!1);return u?n.createElement(g,{otherAttributes:d,title:s,desc:f},r):n.createElement(_,{className:a,style:c,width:o,height:l,onClick:t.onClick,onMouseLeave:t.onMouseLeave,onMouseEnter:t.onMouseEnter,onMouseMove:t.onMouseMove,onMouseDown:t.onMouseDown,onMouseUp:t.onMouseUp,onContextMenu:t.onContextMenu,onDoubleClick:t.onDoubleClick,onTouchStart:t.onTouchStart,onTouchMove:t.onTouchMove,onTouchEnd:t.onTouchEnd},n.createElement(g,{otherAttributes:d,title:s,desc:f,ref:e},n.createElement(C,null,r)))})},3567:(t,e,r)=>{"use strict";var n=r(43210);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},4057:(t,e,r)=>{"use strict";r.d(e,{QQ:()=>i,VU:()=>o,XC:()=>s,_U:()=>c,j2:()=>l});var n=r(43210),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],a=["points","pathLength"],o={svg:["viewBox","children"],polygon:a,polyline:a},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],c=(t,e)=>{if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),"object"!=typeof r&&"function"!=typeof r)return null;var i={};return Object.keys(r).forEach(t=>{l.includes(t)&&(i[t]=e||(e=>r[t](r,e)))}),i},u=(t,e,r)=>n=>(t(e,r,n),null),s=(t,e,r)=>{if(null===t||"object"!=typeof t&&"function"!=typeof t)return null;var n=null;return Object.keys(t).forEach(i=>{var a=t[i];l.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=u(a,e,r))}),n}},4236:(t,e,r)=>{"use strict";r.d(e,{G9:()=>f,_S:()=>h,pU:()=>d,zk:()=>s});var n=r(43210),i=r(75787),a=r(83409),o=["children"],l=()=>{},c=(0,n.createContext)({addErrorBar:l,removeErrorBar:l}),u=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function s(t){var{children:e}=t,r=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,o);return n.createElement(u.Provider,{value:r},e)}var f=()=>(0,n.useContext)(u),h=t=>{var{children:e,xAxisId:r,yAxisId:o,zAxisId:l,dataKey:u,data:s,stackId:f,hide:h,type:d,barSize:p}=t,[y,v]=n.useState([]),g=(0,n.useCallback)(t=>{v(e=>[...e,t])},[v]),m=(0,n.useCallback)(t=>{v(e=>e.filter(e=>e!==t))},[v]),b=(0,a.r)();return n.createElement(c.Provider,{value:{addErrorBar:g,removeErrorBar:m}},n.createElement(i.p,{type:d,data:s,xAxisId:r,yAxisId:o,zAxisId:l,dataKey:u,errorBars:y,stackId:f,hide:h,barSize:p,isPanorama:b}),e)};function d(t){var{addErrorBar:e,removeErrorBar:r}=(0,n.useContext)(c);return null}},5338:(t,e,r)=>{"use strict";r.d(e,{CA:()=>y,MC:()=>u,QG:()=>p,Vi:()=>c,cU:()=>s,fR:()=>f});var n=r(76067),i=r(71392);function a(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function o(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var l=(0,n.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(t,e){t.xAxis[e.payload.id]=(0,i.h4)(e.payload)},removeXAxis(t,e){delete t.xAxis[e.payload.id]},addYAxis(t,e){t.yAxis[e.payload.id]=(0,i.h4)(e.payload)},removeYAxis(t,e){delete t.yAxis[e.payload.id]},addZAxis(t,e){t.zAxis[e.payload.id]=(0,i.h4)(e.payload)},removeZAxis(t,e){delete t.zAxis[e.payload.id]},updateYAxisWidth(t,e){var{id:r,width:n}=e.payload;t.yAxis[r]&&(t.yAxis[r]=o(o({},t.yAxis[r]),{},{width:n}))}}}),{addXAxis:c,removeXAxis:u,addYAxis:s,removeYAxis:f,addZAxis:h,removeZAxis:d,updateYAxisWidth:p}=l.actions,y=l.reducer},5664:(t,e,r)=>{t.exports=r(87509).get},6895:(t,e,r)=>{"use strict";r(3567)},8920:(t,e,r)=>{"use strict";r.d(e,{Be:()=>v,Cv:()=>O,D0:()=>P,Gl:()=>g,Dc:()=>j});var n=r(84648),i=r(86445),a=r(69107),o=r(19335),l=r(22989),c={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},u={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},s=r(53416),f=r(51426),h={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:c.angleAxisId,includeHidden:!1,name:void 0,reversed:c.reversed,scale:c.scale,tick:c.tick,tickCount:void 0,ticks:void 0,type:c.type,unit:void 0},d={allowDataOverflow:u.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:u.tickCount,ticks:void 0,type:u.type,unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:u.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:u.tickCount,ticks:void 0,type:"category",unit:void 0},v=(t,e)=>null!=t.polarAxis.angleAxis[e]?t.polarAxis.angleAxis[e]:"radial"===t.layout.layoutType?p:h,g=(t,e)=>null!=t.polarAxis.radiusAxis[e]?t.polarAxis.radiusAxis[e]:"radial"===t.layout.layoutType?y:d,m=t=>t.polarOptions,b=(0,n.Mz)([i.Lp,i.A$,a.GO],o.lY),x=(0,n.Mz)([m,b],(t,e)=>{if(null!=t)return(0,l.F4)(t.innerRadius,e,0)}),w=(0,n.Mz)([m,b],(t,e)=>{if(null!=t)return(0,l.F4)(t.outerRadius,e,.8*e)}),O=(0,n.Mz)([m],t=>{if(null==t)return[0,0];var{startAngle:e,endAngle:r}=t;return[e,r]});(0,n.Mz)([v,O],s.I);var j=(0,n.Mz)([b,x,w],(t,e,r)=>{if(null!=t&&null!=e&&null!=r)return[e,r]});(0,n.Mz)([g,j],s.I);var P=(0,n.Mz)([f.fz,m,x,w,i.Lp,i.A$],(t,e,r,n,i,a)=>{if(("centric"===t||"radial"===t)&&null!=e&&null!=r&&null!=n){var{cx:o,cy:c,startAngle:u,endAngle:s}=e;return{cx:(0,l.F4)(o,i,i/2),cy:(0,l.F4)(c,a,a/2),innerRadius:r,outerRadius:n,startAngle:u,endAngle:s,clockWise:!1}}})},9474:(t,e,r)=>{t.exports=r(33731).last},10521:(t,e,r)=>{"use strict";r.d(e,{R:()=>n});var n=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},10687:(t,e,r)=>{t.exports=r(75446).sortBy},10907:(t,e,r)=>{"use strict";var n=r(43210),i=r(57379),a="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},o=i.useSyncExternalStore,l=n.useRef,c=n.useEffect,u=n.useMemo,s=n.useDebugValue;e.useSyncExternalStoreWithSelector=function(t,e,r,n,i){var f=l(null);if(null===f.current){var h={hasValue:!1,value:null};f.current=h}else h=f.current;var d=o(t,(f=u(function(){function t(t){if(!c){if(c=!0,o=t,t=n(t),void 0!==i&&h.hasValue){var e=h.value;if(i(e,t))return l=e}return l=t}if(e=l,a(o,t))return e;var r=n(t);return void 0!==i&&i(e,r)?(o=t,e):(o=t,l=r)}var o,l,c=!1,u=void 0===r?null:r;return[function(){return t(e())},null===u?void 0:function(){return t(u())}]},[e,r,n,i]))[0],f[1]);return c(function(){h.hasValue=!0,h.value=d},[d]),s(d),d}},10919:(t,e,r)=>{"use strict";r.d(e,{i:()=>C});var n=r(43210);let i=Math.cos,a=Math.sin,o=Math.sqrt,l=Math.PI,c=2*l,u={draw(t,e){let r=o(e/l);t.moveTo(r,0),t.arc(0,0,r,0,c)}},s=o(1/3),f=2*s,h=a(l/10)/a(7*l/10),d=a(c/10)*h,p=-i(c/10)*h,y=o(3),v=o(3)/2,g=1/o(12),m=(g/2+1)*3;var b=r(22786),x=r(15606);o(3),o(3);var w=r(49384),O=r(54186),j=r(22989),P=["type","size","sizeType"];function M(){return(M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var E={symbolCircle:u,symbolCross:{draw(t,e){let r=o(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=o(e/f),n=r*s;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=o(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=o(.8908130915292852*e),n=d*r,l=p*r;t.moveTo(0,-r),t.lineTo(n,l);for(let e=1;e<5;++e){let o=c*e/5,u=i(o),s=a(o);t.lineTo(s*r,-u*r),t.lineTo(u*n-s*l,s*n+u*l)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-o(e/(3*y));t.moveTo(0,2*r),t.lineTo(-y*r,-r),t.lineTo(y*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=o(e/m),n=r/2,i=r*g,a=r*g+r,l=-n;t.moveTo(n,i),t.lineTo(n,a),t.lineTo(l,a),t.lineTo(-.5*n-v*i,v*n+-.5*i),t.lineTo(-.5*n-v*a,v*n+-.5*a),t.lineTo(-.5*l-v*a,v*l+-.5*a),t.lineTo(-.5*n+v*i,-.5*i-v*n),t.lineTo(-.5*n+v*a,-.5*a-v*n),t.lineTo(-.5*l+v*a,-.5*a-v*l),t.closePath()}}},_=Math.PI/180,k=t=>E["symbol".concat((0,j.Zb)(t))]||u,T=(t,e,r)=>{if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*_;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},C=t=>{var{type:e="circle",size:r=64,sizeType:i="area"}=t,a=A(A({},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,P)),{},{type:e,size:r,sizeType:i}),{className:o,cx:l,cy:c}=a,s=(0,O.J9)(a,!0);return l===+l&&c===+c&&r===+r?n.createElement("path",M({},s,{className:(0,w.$)("recharts-symbols",o),transform:"translate(".concat(l,", ").concat(c,")"),d:(()=>{var t=k(e);return(function(t,e){let r=null,n=(0,x.i)(i);function i(){let i;if(r||(r=i=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),i)return r=null,i+""||null}return t="function"==typeof t?t:(0,b.A)(t||u),e="function"==typeof e?e:(0,b.A)(void 0===e?64:+e),i.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,b.A)(e),i):t},i.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,b.A)(+t),i):e},i.context=function(t){return arguments.length?(r=null==t?null:t,i):r},i})().type(t).size(T(r,i,e))()})()})):null};C.registerSymbol=(t,e)=>{E["symbol".concat((0,j.Zb)(t))]=e}},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function a(t,e,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||t,o),c=r?r+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],l]:t._events[c].push(l):(t._events[c]=l,t._eventsCount++),t}function o(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},l.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},l.prototype.emit=function(t,e,n,i,a,o){var l=r?r+t:t;if(!this._events[l])return!1;var c,u,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,i),!0;case 5:return s.fn.call(s.context,e,n,i,a),!0;case 6:return s.fn.call(s.context,e,n,i,a,o),!0}for(u=1,c=Array(f-1);u<f;u++)c[u-1]=arguments[u];s.fn.apply(s.context,c)}else{var h,d=s.length;for(u=0;u<d;u++)switch(s[u].once&&this.removeListener(t,s[u].fn,void 0,!0),f){case 1:s[u].fn.call(s[u].context);break;case 2:s[u].fn.call(s[u].context,e);break;case 3:s[u].fn.call(s[u].context,e,n);break;case 4:s[u].fn.call(s[u].context,e,n,i);break;default:if(!c)for(h=1,c=Array(f-1);h<f;h++)c[h-1]=arguments[h];s[u].fn.apply(s[u].context,c)}}return!0},l.prototype.on=function(t,e,r){return a(this,t,e,r,!1)},l.prototype.once=function(t,e,r){return a(this,t,e,r,!0)},l.prototype.removeListener=function(t,e,n,i){var a=r?r+t:t;if(!this._events[a])return this;if(!e)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==e||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var c=0,u=[],s=l.length;c<s;c++)(l[c].fn!==e||i&&!l[c].once||n&&l[c].context!==n)&&u.push(l[c]);u.length?this._events[a]=1===u.length?u[0]:u:o(this,a)}return this},l.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&o(this,e)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,t.exports=l},11208:(t,e,r)=>{"use strict";function n(t){return`Minified Redux error #${t}; visit https://redux.js.org/Errors?code=${t} for the full message or use the non-minified dev environment for full errors. `}r.d(e,{HY:()=>u,Qd:()=>l,Tw:()=>f,Zz:()=>s,ve:()=>h,y$:()=>c});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",a=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function l(t){if("object"!=typeof t||null===t)return!1;let e=t;for(;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e||null===Object.getPrototypeOf(t)}function c(t,e,r){if("function"!=typeof t)throw Error(n(2));if("function"==typeof e&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof e&&void 0===r&&(r=e,e=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(c)(t,e)}let a=t,u=e,s=new Map,f=s,h=0,d=!1;function p(){f===s&&(f=new Map,s.forEach((t,e)=>{f.set(e,t)}))}function y(){if(d)throw Error(n(3));return u}function v(t){if("function"!=typeof t)throw Error(n(4));if(d)throw Error(n(5));let e=!0;p();let r=h++;return f.set(r,t),function(){if(e){if(d)throw Error(n(6));e=!1,p(),f.delete(r),s=null}}}function g(t){if(!l(t))throw Error(n(7));if(void 0===t.type)throw Error(n(8));if("string"!=typeof t.type)throw Error(n(17));if(d)throw Error(n(9));try{d=!0,u=a(u,t)}finally{d=!1}return(s=f).forEach(t=>{t()}),t}return g({type:o.INIT}),{dispatch:g,subscribe:v,getState:y,replaceReducer:function(t){if("function"!=typeof t)throw Error(n(10));a=t,g({type:o.REPLACE})},[i]:function(){return{subscribe(t){if("object"!=typeof t||null===t)throw Error(n(11));function e(){t.next&&t.next(y())}return e(),{unsubscribe:v(e)}},[i](){return this}}}}}function u(t){let e,r=Object.keys(t),i={};for(let e=0;e<r.length;e++){let n=r[e];"function"==typeof t[n]&&(i[n]=t[n])}let a=Object.keys(i);try{Object.keys(i).forEach(t=>{let e=i[t];if(void 0===e(void 0,{type:o.INIT}))throw Error(n(12));if(void 0===e(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(t){e=t}return function(t={},r){if(e)throw e;let o=!1,l={};for(let e=0;e<a.length;e++){let c=a[e],u=i[c],s=t[c],f=u(s,r);if(void 0===f)throw r&&r.type,Error(n(14));l[c]=f,o=o||f!==s}return(o=o||a.length!==Object.keys(t).length)?l:t}}function s(...t){return 0===t.length?t=>t:1===t.length?t[0]:t.reduce((t,e)=>(...r)=>t(e(...r)))}function f(...t){return e=>(r,i)=>{let a=e(r,i),o=()=>{throw Error(n(15))},l={getState:a.getState,dispatch:(t,...e)=>o(t,...e)};return o=s(...t.map(t=>t(l)))(a.dispatch),{...a,dispatch:o}}}function h(t){return l(t)&&"type"in t&&"string"==typeof t.type}},11281:(t,e,r)=>{"use strict";r.d(e,{$7:()=>f,Ru:()=>s,uZ:()=>u});var n=r(76067),i=r(17118),a=r(69009),o=r(21426),l=r(85621),c=r(97371),u=(0,n.VP)("keyDown"),s=(0,n.VP)("focus"),f=(0,n.Nc)();f.startListening({actionCreator:u,effect:(t,e)=>{var r=e.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,u=t.payload;if("ArrowRight"===u||"ArrowLeft"===u||"Enter"===u){var s=Number((0,c.P)(n,(0,a.n4)(r))),f=(0,a.R4)(r);if("Enter"===u){var h=(0,o.pg)(r,"axis","hover",String(n.index));e.dispatch((0,i.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:h}));return}var d=s+("ArrowRight"===u?1:-1)*("left-to-right"===(0,l._y)(r)?1:-1);if(null!=f&&!(d>=f.length)&&!(d<0)){var p=(0,o.pg)(r,"axis","hover",String(d));e.dispatch((0,i.o4)({active:!0,activeIndex:d.toString(),activeDataKey:void 0,activeCoordinate:p}))}}}}}),f.startListening({actionCreator:s,effect:(t,e)=>{var r=e.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var a=(0,o.pg)(r,"axis","hover",String("0"));e.dispatch((0,i.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:a}))}}}})},12128:(t,e,r)=>{"use strict";function n(t){return Number.isFinite(t)}function i(t){return"number"==typeof t&&t>0&&Number.isFinite(t)}r.d(e,{F:()=>i,H:()=>n})},12728:(t,e,r)=>{t.exports=r(92292).isEqual},13420:(t,e,r)=>{"use strict";r.d(e,{TK:()=>l});var n=r(43210),i=r(64267),a=r(43209),o=r(83409),l=t=>{var{chartData:e}=t,r=(0,a.j)(),l=(0,o.r)();return(0,n.useEffect)(()=>l?()=>{}:(r((0,i.hq)(e)),()=>{r((0,i.hq)(void 0))}),[e,r,l]),null}},14221:(t,e,r)=>{"use strict";r.d(e,{M:()=>a,t:()=>i});var n=r(43210),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},14454:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(42066),i=r(1640),a=r(23457),o=r(1706);function l(t,e,r,n){if(e===t)return!0;switch(typeof e){case"object":return function(t,e,r,n){if(null==e)return!0;if(Array.isArray(e))return c(t,e,r,n);if(e instanceof Map){var i=t,o=e,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[t,e]of o.entries())if(!1===l(i.get(t),e,t,i,o,s))return!1;return!0}if(e instanceof Set)return u(t,e,r,n);let f=Object.keys(e);if(null==t)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(e))return n.get(e)===t;n&&n.set(e,t);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(t)&&!(o in t)||void 0===e[o]&&void 0!==t[o]||null===e[o]&&null!==t[o]||!r(t[o],e[o],o,t,e,n))return!1}return!0}finally{n&&n.delete(e)}}(t,e,r,n);case"function":if(Object.keys(e).length>0)return l(t,{...e},r,n);return o.eq(t,e);default:if(!i.isObject(t))return o.eq(t,e);if("string"==typeof e)return""===e;return!0}}function c(t,e,r,n){if(0===e.length)return!0;if(!Array.isArray(t))return!1;let i=new Set;for(let a=0;a<e.length;a++){let o=e[a],l=!1;for(let c=0;c<t.length;c++){if(i.has(c))continue;let u=t[c],s=!1;if(r(u,o,a,t,e,n)&&(s=!0),s){i.add(c),l=!0;break}}if(!l)return!1}return!0}function u(t,e,r,n){return 0===e.size||t instanceof Set&&c([...t],[...e],r,n)}e.isMatchWith=function(t,e,r){return"function"!=typeof r?n.isMatch(t,e):l(t,e,function t(e,n,i,a,o,c){let u=r(e,n,i,a,o,c);return void 0!==u?!!u:l(e,n,t,c)},new Map)},e.isSetMatch=u},14956:(t,e,r)=>{"use strict";r.d(e,{A:()=>o,_:()=>l}),r(43210);var n=r(83409),i=r(51426),a=r(43209);function o(t){var{legendPayload:e}=t;return(0,a.j)(),(0,n.r)(),null}function l(t){var{legendPayload:e}=t;return(0,a.j)(),(0,a.G)(i.fz),null}r(53044)},15606:(t,e,r)=>{"use strict";r.d(e,{i:()=>c});let n=Math.PI,i=2*n,a=i-1e-6;function o(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class l{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?o:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return o;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,a){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(t,e,r,i,a){if(t*=1,e*=1,r*=1,i*=1,(a*=1)<0)throw Error(`negative radius: ${a}`);let o=this._x1,l=this._y1,c=r-t,u=i-e,s=o-t,f=l-e,h=s*s+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(h>1e-6)if(Math.abs(f*c-u*s)>1e-6&&a){let d=r-o,p=i-l,y=c*c+u*u,v=Math.sqrt(y),g=Math.sqrt(h),m=a*Math.tan((n-Math.acos((y+h-(d*d+p*p))/(2*v*g)))/2),b=m/g,x=m/v;Math.abs(b-1)>1e-6&&this._append`L${t+b*s},${e+b*f}`,this._append`A${a},${a},0,0,${+(f*d>s*p)},${this._x1=t+x*c},${this._y1=e+x*u}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,o,l,c){if(t*=1,e*=1,r*=1,c=!!c,r<0)throw Error(`negative radius: ${r}`);let u=r*Math.cos(o),s=r*Math.sin(o),f=t+u,h=e+s,d=1^c,p=c?o-l:l-o;null===this._x1?this._append`M${f},${h}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-h)>1e-6)&&this._append`L${f},${h}`,r&&(p<0&&(p=p%i+i),p>a?this._append`A${r},${r},0,1,${d},${t-u},${e-s}A${r},${r},0,1,${d},${this._x1=f},${this._y1=h}`:p>1e-6&&this._append`A${r},${r},0,${+(p>=n)},${d},${this._x1=t+r*Math.cos(l)},${this._y1=e+r*Math.sin(l)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function c(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new l(e)}l.prototype},15708:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(95819);e.toNumber=function(t){return n.isSymbol(t)?NaN:Number(t)}},17118:(t,e,r)=>{"use strict";r.d(e,{E1:()=>v,En:()=>m,Ix:()=>l,ML:()=>d,Nt:()=>p,RD:()=>s,UF:()=>u,XB:()=>c,jF:()=>y,k_:()=>a,o4:()=>g,oP:()=>f,xS:()=>h});var n=r(76067),i=r(71392),a={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:a,hover:a},axisInteraction:{click:a,hover:a},keyboardInteraction:a,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(t,e){t.tooltipItemPayloads.push((0,i.h4)(e.payload))},removeTooltipEntrySettings(t,e){var r=(0,i.ss)(t).tooltipItemPayloads.indexOf((0,i.h4)(e.payload));r>-1&&t.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(t,e){t.settings=e.payload},setActiveMouseOverItemIndex(t,e){t.syncInteraction.active=!1,t.keyboardInteraction.active=!1,t.itemInteraction.hover.active=!0,t.itemInteraction.hover.index=e.payload.activeIndex,t.itemInteraction.hover.dataKey=e.payload.activeDataKey,t.itemInteraction.hover.coordinate=e.payload.activeCoordinate},mouseLeaveChart(t){t.itemInteraction.hover.active=!1,t.axisInteraction.hover.active=!1},mouseLeaveItem(t){t.itemInteraction.hover.active=!1},setActiveClickItemIndex(t,e){t.syncInteraction.active=!1,t.itemInteraction.click.active=!0,t.keyboardInteraction.active=!1,t.itemInteraction.click.index=e.payload.activeIndex,t.itemInteraction.click.dataKey=e.payload.activeDataKey,t.itemInteraction.click.coordinate=e.payload.activeCoordinate},setMouseOverAxisIndex(t,e){t.syncInteraction.active=!1,t.axisInteraction.hover.active=!0,t.keyboardInteraction.active=!1,t.axisInteraction.hover.index=e.payload.activeIndex,t.axisInteraction.hover.dataKey=e.payload.activeDataKey,t.axisInteraction.hover.coordinate=e.payload.activeCoordinate},setMouseClickAxisIndex(t,e){t.syncInteraction.active=!1,t.keyboardInteraction.active=!1,t.axisInteraction.click.active=!0,t.axisInteraction.click.index=e.payload.activeIndex,t.axisInteraction.click.dataKey=e.payload.activeDataKey,t.axisInteraction.click.coordinate=e.payload.activeCoordinate},setSyncInteraction(t,e){t.syncInteraction=e.payload},setKeyboardInteraction(t,e){t.keyboardInteraction.active=e.payload.active,t.keyboardInteraction.index=e.payload.activeIndex,t.keyboardInteraction.coordinate=e.payload.activeCoordinate,t.keyboardInteraction.dataKey=e.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:c,setTooltipSettingsState:u,setActiveMouseOverItemIndex:s,mouseLeaveItem:f,mouseLeaveChart:h,setActiveClickItemIndex:d,setMouseOverAxisIndex:p,setMouseClickAxisIndex:y,setSyncInteraction:v,setKeyboardInteraction:g}=o.actions,m=o.reducer},17617:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(92681),i=r(40144),a=r(74838),o=r(30415);e.iteratee=function(t){if(null==t)return n.identity;switch(typeof t){case"function":return t;case"object":if(Array.isArray(t)&&2===t.length)return o.matchesProperty(t[0],t[1]);return a.matches(t);case"string":case"symbol":case"number":return i.property(t)}}},17874:(t,e,r)=>{"use strict";r.d(e,{f:()=>p});var n=r(22989),i=r(96075),a=r(20237);function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class c{static create(t){return new c(t)}constructor(t){this.scale=t}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(t){var{bandAware:e,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==t){if(r)switch(r){case"start":default:return this.scale(t);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(e){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}isInRange(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}l(c,"EPS",1e-4);var u=function(t){var{width:e,height:r}=t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/e);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):e/Math.cos(i))};function s(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e)if(void 0!==r&&!0!==r(t[i]))return;else n.push(t[i]);return n}function f(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var a=r();return t*(e-t*a/2-n)>=0&&t*(e+t*a/2-i)<=0}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function p(t,e,r){var o,{tick:l,ticks:c,viewBox:h,minTickGap:p,orientation:y,interval:v,tickFormatter:g,unit:m,angle:b}=t;if(!c||!c.length||!l)return[];if((0,n.Et)(v)||a.m.isSsr)return null!=(o=s(c,((0,n.Et)(v)?v:0)+1))?o:[];var x=[],w="top"===y||"bottom"===y?"width":"height",O=m&&"width"===w?(0,i.P)(m,{fontSize:e,letterSpacing:r}):{width:0,height:0},j=(t,n)=>{var a,o="function"==typeof g?g(t.value,n):t.value;return"width"===w?(a=(0,i.P)(o,{fontSize:e,letterSpacing:r}),u({width:a.width+O.width,height:a.height+O.height},b)):(0,i.P)(o,{fontSize:e,letterSpacing:r})[w]},P=c.length>=2?(0,n.sA)(c[1].coordinate-c[0].coordinate):1,M=function(t,e,r){var n="width"===r,{x:i,y:a,width:o,height:l}=t;return 1===e?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(h,P,w);return"equidistantPreserveStart"===v?function(t,e,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:c}=e,u=0,h=1,d=l;h<=o.length;)if(a=function(){var e,a=null==n?void 0:n[u];if(void 0===a)return{v:s(n,h)};var o=u,p=()=>(void 0===e&&(e=r(a,o)),e),y=a.coordinate,v=0===u||f(t,y,p,d,c);v||(u=0,d=l,h+=1),v&&(d=y+t*(p()/2+i),u+=h)}())return a.v;return[]}(P,M,j,c,p):("preserveStart"===v||"preserveStartEnd"===v?function(t,e,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:c,end:u}=e;if(a){var s=n[l-1],h=r(s,l-1),p=t*(s.coordinate+t*h/2-u);o[l-1]=s=d(d({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),f(t,s.tickCoord,()=>h,c,u)&&(u=s.tickCoord-t*(h/2+i),o[l-1]=d(d({},s),{},{isShow:!0}))}for(var y=a?l-1:l,v=function(e){var n,a=o[e],l=()=>(void 0===n&&(n=r(a,e)),n);if(0===e){var s=t*(a.coordinate-t*l()/2-c);o[e]=a=d(d({},a),{},{tickCoord:s<0?a.coordinate-s*t:a.coordinate})}else o[e]=a=d(d({},a),{},{tickCoord:a.coordinate});f(t,a.tickCoord,l,c,u)&&(c=a.tickCoord+t*(l()/2+i),o[e]=d(d({},a),{},{isShow:!0}))},g=0;g<y;g++)v(g);return o}(P,M,j,c,p,"preserveStartEnd"===v):function(t,e,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=e,{end:c}=e,u=function(e){var n,u=a[e],s=()=>(void 0===n&&(n=r(u,e)),n);if(e===o-1){var h=t*(u.coordinate+t*s()/2-c);a[e]=u=d(d({},u),{},{tickCoord:h>0?u.coordinate-h*t:u.coordinate})}else a[e]=u=d(d({},u),{},{tickCoord:u.coordinate});f(t,u.tickCoord,s,l,c)&&(c=u.tickCoord-t*(s()/2+i),a[e]=d(d({},u),{},{isShow:!0}))},s=o-1;s>=0;s--)u(s);return a}(P,M,j,c,p)).filter(t=>t.isShow)}},18969:(t,e,r)=>{"use strict";r.d(e,{m:()=>tt});var n=r(43210),i=r(51215),a=r(10687),o=r.n(a),l=r(49384),c=r(22989);function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t){return Array.isArray(t)&&(0,c.vh)(t[0])&&(0,c.vh)(t[1])?t.join(" ~ "):t}var d=t=>{var{separator:e=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:s,formatter:d,itemSorter:p,wrapperClassName:y,labelClassName:v,label:g,labelFormatter:m,accessibilityLayer:b=!1}=t,x=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),w=f({margin:0},a),O=!(0,c.uy)(g),j=O?g:"",P=(0,l.$)("recharts-default-tooltip",y),M=(0,l.$)("recharts-tooltip-label",v);return O&&m&&null!=s&&(j=m(g,s)),n.createElement("div",u({className:P,style:x},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:M,style:w},n.isValidElement(j)?j:"".concat(j)),(()=>{if(s&&s.length){var t=(p?o()(s,p):s).map((t,r)=>{if("none"===t.type)return null;var a=t.formatter||d||h,{value:o,name:l}=t,u=o,p=l;if(a){var y=a(o,l,t,r,s);if(Array.isArray(y))[u,p]=y;else{if(null==y)return null;u=y}}var v=f({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:v},(0,c.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,(0,c.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},e):null,n.createElement("span",{className:"recharts-tooltip-item-value"},u),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null})())},p="recharts-tooltip-wrapper",y={visibility:"hidden"};function v(t){var{allowEscapeViewBox:e,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:u,viewBoxDimension:s}=t;if(a&&(0,c.Et)(a[n]))return a[n];var f=r[n]-l-(i>0?i:0),h=r[n]+i;if(e[n])return o[n]?f:h;var d=u[n];return null==d?0:o[n]?f<d?Math.max(h,d):Math.max(f,d):null==s?0:h+l>d+s?Math.max(f,d):Math.max(h,d)}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach(function(e){b(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function b(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class x extends n.PureComponent{constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",t=>{if("Escape"===t.key){var e,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(e=null==(r=this.props.coordinate)?void 0:r.x)?e:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var t,e;this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:t,allowEscapeViewBox:e,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:u,isAnimationActive:s,offset:f,position:h,reverseDirection:d,useTranslate3d:g,viewBox:b,wrapperStyle:x,lastBoundingBox:w,innerRef:O,hasPortalFromProps:j}=this.props,{cssClasses:P,cssProperties:M}=function(t){var e,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:u,reverseDirection:s,tooltipBox:f,useTranslate3d:h,viewBox:d}=t;return{cssProperties:e=f.height>0&&f.width>0&&a?function(t){var{translateX:e,translateY:r,useTranslate3d:n}=t;return{transform:n?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}({translateX:r=v({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:u,reverseDirection:s,tooltipDimension:f.width,viewBox:d,viewBoxDimension:d.width}),translateY:n=v({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:u,reverseDirection:s,tooltipDimension:f.height,viewBox:d,viewBoxDimension:d.height}),useTranslate3d:h}):y,cssClasses:function(t){var{coordinate:e,translateX:r,translateY:n}=t;return(0,l.$)(p,{["".concat(p,"-right")]:(0,c.Et)(r)&&e&&(0,c.Et)(e.x)&&r>=e.x,["".concat(p,"-left")]:(0,c.Et)(r)&&e&&(0,c.Et)(e.x)&&r<e.x,["".concat(p,"-bottom")]:(0,c.Et)(n)&&e&&(0,c.Et)(e.y)&&n>=e.y,["".concat(p,"-top")]:(0,c.Et)(n)&&e&&(0,c.Et)(e.y)&&n<e.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:e,coordinate:o,offsetTopLeft:f,position:h,reverseDirection:d,tooltipBox:{height:w.height,width:w.width},useTranslate3d:g,viewBox:b}),S=j?{}:m(m({transition:s&&t?"transform ".concat(r,"ms ").concat(i):void 0},M),{},{pointerEvents:"none",visibility:!this.state.dismissed&&t&&u?"visible":"hidden",position:"absolute",top:0,left:0}),A=m(m({},S),{},{visibility:!this.state.dismissed&&t&&u?"visible":"hidden"},x);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:P,style:A,ref:O},a)}}var w=r(20237),O=r(45796),j=r(51426),P=r(24028),M=r(68392),S=r(81888),A=r(54186),E=["x","y","top","left","width","height","className"];function _(){return(_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var T=(t,e,r,n,i,a)=>"M".concat(t,",").concat(i,"v").concat(n,"M").concat(a,",").concat(e,"h").concat(r),C=t=>{var{x:e=0,y:r=0,top:i=0,left:a=0,width:o=0,height:u=0,className:s}=t,f=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:e,y:r,top:i,left:a,width:o,height:u},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,E));return(0,c.Et)(e)&&(0,c.Et)(r)&&(0,c.Et)(o)&&(0,c.Et)(u)&&(0,c.Et)(i)&&(0,c.Et)(a)?n.createElement("path",_({},(0,A.J9)(f,!0),{className:(0,l.$)("recharts-cross",s),d:T(e,r,o,u,i,a)})):null},D=r(71524),N=r(19335);function I(t){var{cx:e,cy:r,radius:n,startAngle:i,endAngle:a}=t;return{points:[(0,N.IZ)(e,r,n,i),(0,N.IZ)(e,r,n,a)],cx:e,cy:r,radius:n,startAngle:i,endAngle:a}}var z=r(34955),L=r(83136),$=r(21426);function U(){return(U=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function R(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function B(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?R(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function F(t){var e,r,i,{coordinate:a,payload:o,index:c,offset:u,tooltipAxisBandSize:s,layout:f,cursor:h,tooltipEventType:d,chartName:p}=t;if(!h||!a||"ScatterChart"!==p&&"axis"!==d)return null;if("ScatterChart"===p)r=a,i=C;else if("BarChart"===p)e=s/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?a.x-e:u.left+.5,y:"horizontal"===f?u.top+.5:a.y-e,width:"horizontal"===f?s:u.width-1,height:"horizontal"===f?u.height-1:s},i=D.M;else if("radial"===f){var{cx:y,cy:v,radius:g,startAngle:m,endAngle:b}=I(a);r={cx:y,cy:v,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},i=z.h}else r={points:function(t,e,r){var n,i,a,o;if("horizontal"===t)a=n=e.x,i=r.top,o=r.top+r.height;else if("vertical"===t)o=i=e.y,n=r.left,a=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return I(e);else{var{cx:l,cy:c,innerRadius:u,outerRadius:s,angle:f}=e,h=(0,N.IZ)(l,c,u,f),d=(0,N.IZ)(l,c,s,f);n=h.x,i=h.y,a=d.x,o=d.y}return[{x:n,y:i},{x:a,y:o}]}(f,a,u)},i=S.I;var x="object"==typeof h&&"className"in h?h.className:void 0,w=B(B(B(B({stroke:"#ccc",pointerEvents:"none"},u),r),(0,A.J9)(h,!1)),{},{payload:o,payloadIndex:c,className:(0,l.$)("recharts-tooltip-cursor",x)});return(0,n.isValidElement)(h)?(0,n.cloneElement)(h,w):(0,n.createElement)(i,w)}function G(t){var e=(0,L.O)(),r=(0,j.hj)(),i=(0,j.WX)(),a=(0,$.fW)();return n.createElement(F,U({},t,{coordinate:t.coordinate,index:t.index,payload:t.payload,offset:r,layout:i,tooltipAxisBandSize:e,chartName:a}))}var K=r(97711),H=r(43209);r(17118);var W=r(98009),Z=r(43075),q=r(73865);function V(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?V(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):V(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function J(t){return t.dataKey}var X=[],Q={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!w.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function tt(t){var e,r=(0,q.e)(t,Q),{active:a,allowEscapeViewBox:o,animationDuration:l,animationEasing:c,content:u,filterNull:s,isAnimationActive:f,offset:h,payloadUniqBy:p,position:y,reverseDirection:v,useTranslate3d:g,wrapperStyle:m,cursor:b,shared:w,trigger:S,defaultIndex:A,portal:E,axisId:_}=r;(0,H.j)();var k="number"==typeof A?String(A):A,T=(0,j.sk)(),C=(0,P.$)(),D=(0,Z.Td)(w),{activeIndex:N,isActive:I}=(0,H.G)(t=>(0,$.yn)(t,D,S,k)),z=(0,H.G)(t=>(0,$.u9)(t,D,S,k)),L=(0,H.G)(t=>(0,$.BZ)(t,D,S,k)),U=(0,H.G)(t=>(0,$.dS)(t,D,S,k)),R=(0,K.X)(),B=null!=a?a:I,[F,V]=(0,M.V)([z,B]),tt="axis"===D?L:void 0;(0,W.m7)(D,S,U,tt,N,B);var te=null!=E?E:R;if(null==te)return null;var tr=null!=z?z:X;B||(tr=X),s&&tr.length&&(tr=(0,O.s)(z.filter(t=>null!=t.value&&(!0!==t.hide||r.includeHidden)),p,J));var tn=tr.length>0,ti=n.createElement(x,{allowEscapeViewBox:o,animationDuration:l,animationEasing:c,isAnimationActive:f,active:B,coordinate:U,hasPayload:tn,offset:h,position:y,reverseDirection:v,useTranslate3d:g,viewBox:T,wrapperStyle:m,lastBoundingBox:F,innerRef:V,hasPortalFromProps:!!E},(e=Y(Y({},r),{},{payload:tr,label:tt,active:B,coordinate:U,accessibilityLayer:C}),n.isValidElement(u)?n.cloneElement(u,e):"function"==typeof u?n.createElement(u,e):n.createElement(d,e)));return n.createElement(n.Fragment,null,(0,i.createPortal)(ti,te),B&&n.createElement(G,{cursor:b,tooltipEventType:D,coordinate:U,payload:z,index:N}))}},19335:(t,e,r)=>{"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}r.d(e,{IZ:()=>l,Kg:()=>a,lY:()=>c,yy:()=>d}),r(43210);var a=Math.PI/180,o=t=>180*t/Math.PI,l=(t,e,r,n)=>({x:t+Math.cos(-a*n)*r,y:e+Math.sin(-a*n)*r}),c=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},u=(t,e)=>{var{x:r,y:n}=t,{x:i,y:a}=e;return Math.sqrt((r-i)**2+(n-a)**2)},s=(t,e)=>{var{x:r,y:n}=t,{cx:i,cy:a}=e,l=u({x:r,y:n},{x:i,y:a});if(l<=0)return{radius:l,angle:0};var c=Math.acos((r-i)/l);return n>a&&(c=2*Math.PI-c),{radius:l,angle:o(c),angleInRadian:c}},f=t=>{var{startAngle:e,endAngle:r}=t,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},h=(t,e)=>{var{startAngle:r,endAngle:n}=e;return t+360*Math.min(Math.floor(r/360),Math.floor(n/360))},d=(t,e)=>{var r,{x:n,y:a}=t,{radius:o,angle:l}=s({x:n,y:a},e),{innerRadius:c,outerRadius:u}=e;if(o<c||o>u||0===o)return null;var{startAngle:d,endAngle:p}=f(e),y=l;if(d<=p){for(;y>p;)y-=360;for(;y<d;)y+=360;r=y>=d&&y<=p}else{for(;y>d;)y-=360;for(;y<p;)y+=360;r=y>=p&&y<=d}return r?i(i({},e),{},{radius:o,angle:h(y,e)}):null}},19420:(t,e,r)=>{"use strict";r.d(e,{i:()=>D});var n=r(43210),i=r(12728),a=r.n(i),o=(t,e)=>[0,3*t,3*e-6*t,3*t-3*e+1],l=(t,e)=>t.map((t,r)=>t*e**r).reduce((t,e)=>t+e),c=(t,e)=>r=>l(o(t,e),r),u=(t,e)=>r=>l([...o(t,e).map((t,e)=>t*e).slice(1),0],r),s=function(){for(var t,e,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[t,r,e,n]=[0,0,1,1];break;case"ease":[t,r,e,n]=[.25,.1,.25,1];break;case"ease-in":[t,r,e,n]=[.42,0,1,1];break;case"ease-out":[t,r,e,n]=[.42,0,.58,1];break;case"ease-in-out":[t,r,e,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([t,r,e,n]=l[1].split(")")[0].split(",").map(t=>parseFloat(t)))}else 4===a.length&&([t,r,e,n]=a);var s=c(t,e),f=c(r,n),h=u(t,e),d=t=>t>1?1:t<0?0:t,p=t=>{for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i=s(r)-e,a=h(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=d(r-i/a)}return f(r)};return p.isStepper=!1,p},f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:e=100,damping:r=8,dt:n=17}=t,i=(t,i,a)=>{var o=a+(-(t-i)*e-a*r)*n/1e3,l=a*n/1e3+t;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},h=t=>{if("string"==typeof t)switch(t){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return s(t);case"spring":return f();default:if("cubic-bezier"===t.split("(")[0])return s(t)}return"function"==typeof t?t:null};function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var y=t=>t.replace(/([A-Z])/g,t=>"-".concat(t.toLowerCase())),v=(t,e,r)=>t.map(t=>"".concat(y(t)," ").concat(e,"ms ").concat(r)).join(","),g=(t,e)=>[Object.keys(t),Object.keys(e)].reduce((t,e)=>t.filter(t=>e.includes(t))),m=(t,e)=>Object.keys(e).reduce((r,n)=>p(p({},r),{},{[n]:t(n,e[n])}),{});function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var w=(t,e,r)=>t+(e-t)*r,O=t=>{var{from:e,to:r}=t;return e!==r},j=(t,e,r)=>{var n=m((e,r)=>{if(O(r)){var[n,i]=t(r.from,r.to,r.velocity);return x(x({},r),{},{from:n,velocity:i})}return r},e);return r<1?m((t,e)=>O(e)?x(x({},e),{},{velocity:w(e.velocity,n[t].velocity,r),from:w(e.from,n[t].from,r)}):e,e):j(t,n,r-1)};let P=(t,e,r,n,i,a)=>{var o=g(t,e);return!0===r.isStepper?function(t,e,r,n,i,a){var o,l=n.reduce((r,n)=>x(x({},r),{},{[n]:{from:t[n],velocity:0,to:e[n]}}),{}),c=()=>m((t,e)=>e.from,l),u=()=>!Object.values(l).filter(O).length,s=null,f=n=>{o||(o=n);var h=(n-o)/r.dt;l=j(r,l,h),i(x(x(x({},t),e),c())),o=n,u()||(s=a.setTimeout(f))};return()=>(s=a.setTimeout(f),()=>{s()})}(t,e,r,o,i,a):function(t,e,r,n,i,a,o){var l,c=null,u=i.reduce((r,n)=>x(x({},r),{},{[n]:[t[n],e[n]]}),{}),s=i=>{l||(l=i);var f=(i-l)/n,h=m((t,e)=>w(...e,r(f)),u);if(a(x(x(x({},t),e),h)),f<1)c=o.setTimeout(s);else{var d=m((t,e)=>w(...e,r(1)),u);a(x(x(x({},t),e),d))}};return()=>(c=o.setTimeout(s),()=>{c()})}(t,e,r,n,o,i,a)};class M{setTimeout(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=e?t(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var S=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function A(){return(A=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function E(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?E(Object(r),!0).forEach(function(e){k(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function k(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class T extends n.PureComponent{constructor(t,e){super(t,e),k(this,"mounted",!1),k(this,"manager",null),k(this,"stopJSAnimation",null),k(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:t,canBegin:e}=this.props;this.mounted=!0,t&&e&&this.runAnimation(this.props)}componentDidUpdate(t){var{isActive:e,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:l}=this.props,{style:c}=this.state;if(r){if(!e){this.state&&c&&(n&&c[n]!==o||!n&&c!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!a()(t.to,o)||!t.canBegin||!t.isActive){var u=!t.canBegin||!t.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=u||i?l:t.to;this.state&&c&&(n&&c[n]!==s||!n&&c!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(_(_({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:t}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}handleStyleChange(t){this.changeStyle(t)}changeStyle(t){this.mounted&&this.setState({style:t})}runJSAnimation(t){var{from:e,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=t,c=P(e,r,h(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=c()},n,o])}runAnimation(t){var{begin:e,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:c}=t;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof c||"spring"===a)return void this.runJSAnimation(t);var u=n?{[n]:i}:i,s=v(Object.keys(u),r,a);this.manager.start([o,e,_(_({},u),{},{transition:s}),r,l])}render(){var t=this.props,{children:e,begin:r,duration:i,attributeName:a,easing:o,isActive:l,from:c,to:u,canBegin:s,onAnimationEnd:f,shouldReAnimate:h,onAnimationReStart:d,animationManager:p}=t,y=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,S),v=n.Children.count(e),g=this.state.style;if("function"==typeof e)return e(g);if(!l||0===v||i<=0)return e;var m=t=>{var{style:e={},className:r}=t.props;return(0,n.cloneElement)(t,_(_({},y),{},{style:_(_({},e),g),className:r}))};return 1===v?m(n.Children.only(e)):n.createElement("div",null,n.Children.map(e,t=>m(t)))}}k(T,"displayName","Animate"),k(T,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var C=(0,n.createContext)(null);function D(t){var e,r,i,a,o,l,c,u,s=(0,n.useContext)(C);return n.createElement(T,A({},t,{animationManager:null!=(c=null!=(u=t.animationManager)?u:s)?c:(e=new M,i=()=>null,a=!1,o=null,l=t=>{if(!a){if(Array.isArray(t)){if(!t.length)return;var[r,...n]=t;if("number"==typeof r){o=e.setTimeout(l.bind(null,n),r);return}l(r),o=e.setTimeout(l.bind(null,n));return}"object"==typeof t&&i(t),"function"==typeof t&&t()}},{stop:()=>{a=!0},start:t=>{a=!1,o&&(o(),o=null),l(t)},subscribe:t=>(i=t,()=>{i=()=>null}),getTimeoutController:()=>e})}))}},19598:(t,e,r)=>{"use strict";r.d(e,{h:()=>x});var n=r(43210),i=r(49384),a=r(71579),o=r(5338),l=r(43209),c=r(85621),u=r(69107),s=r(83409),f=t=>{var{ticks:e,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=t,o=0;if(e){e.forEach(t=>{if(t){var e=t.getBoundingClientRect();e.width>o&&(o=e.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0},h=r(97633),d=["dangerouslySetInnerHTML","ticks"];function p(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(){return(y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function v(t){return(0,l.j)(),null}var g=t=>{var e,{yAxisId:r,className:p,width:v,label:g}=t,m=(0,n.useRef)(null),b=(0,n.useRef)(null),x=(0,l.G)(u.c2),w=(0,s.r)(),O=(0,l.j)(),j="yAxis",P=(0,l.G)(t=>(0,c.iV)(t,j,r,w)),M=(0,l.G)(t=>(0,c.wP)(t,r)),S=(0,l.G)(t=>(0,c.KR)(t,r)),A=(0,l.G)(t=>(0,c.Zi)(t,j,r,w));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==v||!M||(0,h.Z)(g)||(0,n.isValidElement)(g))){var t,e=m.current,i=null==e||null==(t=e.tickRefs)?void 0:t.current,{tickSize:a,tickMargin:l}=e.props,c=f({ticks:i,label:b.current,labelGapWithTick:5,tickSize:a,tickMargin:l});Math.round(M.width)!==Math.round(c)&&O((0,o.QG)({id:r,width:c}))}},[m,null==m||null==(e=m.current)||null==(e=e.tickRefs)?void 0:e.current,null==M?void 0:M.width,M,O,g,r,v]),null==M||null==S)return null;var{dangerouslySetInnerHTML:E,ticks:_}=t,k=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,d);return n.createElement(a.u,y({},k,{ref:m,labelRef:b,scale:P,x:S.x,y:S.y,width:M.width,height:M.height,className:(0,i.$)("recharts-".concat(j," ").concat(j),p),viewBox:x,ticks:A}))},m=t=>{var e,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(v,{interval:null!=(e=t.interval)?e:"preserveEnd",id:t.yAxisId,scale:t.scale,type:t.type,domain:t.domain,allowDataOverflow:t.allowDataOverflow,dataKey:t.dataKey,allowDuplicatedCategory:t.allowDuplicatedCategory,allowDecimals:t.allowDecimals,tickCount:t.tickCount,padding:t.padding,includeHidden:null!=(r=t.includeHidden)&&r,reversed:t.reversed,ticks:t.ticks,width:t.width,orientation:t.orientation,mirror:t.mirror,hide:t.hide,unit:t.unit,name:t.name,angle:null!=(i=t.angle)?i:0,minTickGap:null!=(a=t.minTickGap)?a:5,tick:null==(o=t.tick)||o,tickFormatter:t.tickFormatter}),n.createElement(g,t))},b={allowDataOverflow:c.cd.allowDataOverflow,allowDecimals:c.cd.allowDecimals,allowDuplicatedCategory:c.cd.allowDuplicatedCategory,hide:!1,mirror:c.cd.mirror,orientation:c.cd.orientation,padding:c.cd.padding,reversed:c.cd.reversed,scale:c.cd.scale,tickCount:c.cd.tickCount,type:c.cd.type,width:c.cd.width,yAxisId:0};class x extends n.Component{render(){return n.createElement(m,this.props)}}p(x,"displayName","YAxis"),p(x,"defaultProps",b)},20237:(t,e,r)=>{"use strict";r.d(e,{m:()=>n});var n={isSsr:!0}},20911:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(55100);e.debounce=function(t,e=0,r={}){let i;"object"!=typeof r&&(r={});let{leading:a=!1,trailing:o=!0,maxWait:l}=r,c=[,,];a&&(c[0]="leading"),o&&(c[1]="trailing");let u=null,s=n.debounce(function(...e){i=t.apply(this,e),u=null},e,{edges:c}),f=function(...e){return null!=l&&(null===u&&(u=Date.now()),Date.now()-u>=l)?(i=t.apply(this,e),u=Date.now(),s.cancel(),s.schedule(),i):(s.apply(this,e),i)};return f.cancel=s.cancel,f.flush=()=>(s.flush(),i),f}},21080:(t,e,r)=>{"use strict";r.d(e,{u:()=>c});var n=r(43210),i=r(49384),a=r(54186),o=["children","width","height","viewBox","className","style","title","desc"];function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var c=(0,n.forwardRef)((t,e)=>{var{children:r,width:c,height:u,viewBox:s,className:f,style:h,title:d,desc:p}=t,y=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,o),v=s||{width:c,height:u,x:0,y:0},g=(0,i.$)("recharts-surface",f);return n.createElement("svg",l({},(0,a.J9)(y,!0,"svg"),{className:g,width:c,height:u,style:h,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height),ref:e}),n.createElement("title",null,d),n.createElement("desc",null,p),r)})},21251:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isTypedArray=function(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}},21424:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toArray=function(t){return Array.isArray(t)?t:Array.from(t)}},21426:(t,e,r)=>{"use strict";r.d(e,{BZ:()=>I,aX:()=>$,dS:()=>N,dp:()=>T,fW:()=>P,pg:()=>D,r1:()=>E,u9:()=>z,yn:()=>L});var n=r(84648),i=r(10687),a=r.n(i),o=r(43209),l=r(64279),c=r(22989),u=r(57282),s=r(69009),f=r(97350),h=r(51426),d=r(69107),p=r(86445),y=r(28550),v=r(32520),g=r(97371),m=r(49396),b=r(77100),x=r(72198),w=r(78242);function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var P=()=>(0,o.G)(f.iO),M=(t,e)=>e,S=(t,e,r)=>r,A=(t,e,r,n)=>n,E=(0,n.Mz)(s.R4,t=>a()(t,t=>t.coordinate)),_=(0,n.Mz)([w.J,M,S,A],v.i),k=(0,n.Mz)([_,s.n4],g.P),T=(t,e,r)=>{if(null!=e){var n=(0,w.J)(t);return"axis"===e?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},C=(0,n.Mz)([w.J,M,S,A],b.q),D=(0,n.Mz)([p.Lp,p.A$,h.fz,d.GO,s.R4,A,C,x.x],m.o),N=(0,n.Mz)([_,D],(t,e)=>{var r;return null!=(r=t.coordinate)?r:e}),I=(0,n.Mz)(s.R4,k,y.E),z=(0,n.Mz)([C,k,u.LF,s.Dn,I,x.x,M],(t,e,r,n,i,a,o)=>{if(null!=e&&null!=a){var{chartData:u,computedData:s,dataStartIndex:f,dataEndIndex:h}=r;return t.reduce((t,r)=>{var d,p,y,v,g,{dataDefinedOnItem:m,settings:b}=r,x=function(t,e,r){return Array.isArray(t)&&t&&e+r!==0?t.slice(e,r+1):t}((d=m,p=u,null!=d?d:p),f,h),w=null!=(y=null==b?void 0:b.dataKey)?y:null==n?void 0:n.dataKey,O=null==b?void 0:b.nameKey;return Array.isArray(v=null!=n&&n.dataKey&&!(null!=n&&n.allowDuplicatedCategory)&&Array.isArray(x)&&"axis"===o?(0,c.eP)(x,n.dataKey,i):a(x,e,s,O))?v.forEach(e=>{var r=j(j({},b),{},{name:e.name,unit:e.unit,color:void 0,fill:void 0});t.push((0,l.GF)({tooltipEntrySettings:r,dataKey:e.dataKey,payload:e.payload,value:(0,l.kr)(e.payload,e.dataKey),name:e.name}))}):t.push((0,l.GF)({tooltipEntrySettings:b,dataKey:w,payload:v,value:(0,l.kr)(v,w),name:null!=(g=(0,l.kr)(v,O))?g:null==b?void 0:b.name})),t},[])}}),L=(0,n.Mz)([_],t=>({isActive:t.active,activeIndex:t.index})),$=(t,e,r,n,i,a,o,c)=>{if(t&&e&&n&&i&&a){var u=(0,l.r4)(t.chartX,t.chartY,e,r,c);if(u){var s=(0,l.SW)(u,e),f=(0,l.gH)(s,o,a,n,i),h=(0,l.bk)(e,a,f,u);return{activeIndex:String(f),activeCoordinate:h}}}}},22786:(t,e,r)=>{"use strict";function n(t){return function(){return t}}r.d(e,{A:()=>n})},22989:(t,e,r)=>{"use strict";r.d(e,{CG:()=>d,Dj:()=>p,Et:()=>c,F4:()=>h,GW:()=>y,M8:()=>o,NF:()=>f,Zb:()=>m,_3:()=>l,eP:()=>v,sA:()=>a,uy:()=>g,vh:()=>u});var n=r(5664),i=r.n(n),a=t=>0===t?0:t>0?1:-1,o=t=>"number"==typeof t&&t!=+t,l=t=>"string"==typeof t&&t.indexOf("%")===t.length-1,c=t=>("number"==typeof t||t instanceof Number)&&!o(t),u=t=>c(t)||"string"==typeof t,s=0,f=t=>{var e=++s;return"".concat(t||"").concat(e)},h=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!c(t)&&"string"!=typeof t)return n;if(l(t)){if(null==e)return n;var a=t.indexOf("%");r=e*parseFloat(t.slice(0,a))/100}else r=+t;return o(r)&&(r=n),i&&null!=e&&r>e&&(r=e),r},d=t=>{if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},p=(t,e)=>c(t)&&c(e)?r=>t+r*(e-t):()=>e;function y(t,e,r){return c(t)&&c(e)?t+r*(e-t):e}function v(t,e,r){if(t&&t.length)return t.find(t=>t&&("function"==typeof e?e(t):i()(t,e))===r)}var g=t=>null==t,m=t=>g(t)?t:"".concat(t.charAt(0).toUpperCase()).concat(t.slice(1))},23337:(t,e,r)=>{"use strict";r.d(e,{dc:()=>a,ff:()=>i,g0:()=>o});var n=r(84648),i=t=>t.legend.settings,a=t=>t.legend.size,o=(0,n.Mz)([t=>t.legend.payload],t=>t.flat(1))},23457:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isPrimitive=function(t){return null==t||"object"!=typeof t&&"function"!=typeof t}},23561:(t,e,r)=>{"use strict";r.d(e,{E:()=>_});var n=r(43210),i=r(49384),a=r(22989),o=r(20237),l=r(54186),c=r(96075),u=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,h=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,d={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},p=Object.keys(d);class y{static parse(t){var e,[,r,n]=null!=(e=h.exec(t))?e:[];return new y(parseFloat(r),null!=n?n:"")}constructor(t,e){this.num=t,this.unit=e,this.num=t,this.unit=e,(0,a.M8)(t)&&(this.unit=""),""===e||f.test(e)||(this.num=NaN,this.unit=""),p.includes(e)&&(this.num=t*d[e],this.unit="px")}add(t){return this.unit!==t.unit?new y(NaN,""):new y(this.num+t.num,this.unit)}subtract(t){return this.unit!==t.unit?new y(NaN,""):new y(this.num-t.num,this.unit)}multiply(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new y(NaN,""):new y(this.num*t.num,this.unit||t.unit)}divide(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new y(NaN,""):new y(this.num/t.num,this.unit||t.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,a.M8)(this.num)}}function v(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,[,n,i,a]=null!=(r=u.exec(e))?r:[],o=y.parse(null!=n?n:""),l=y.parse(null!=a?a:""),c="*"===i?o.multiply(l):o.divide(l);if(c.isNaN())return"NaN";e=e.replace(u,c.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var f,[,h,d,p]=null!=(f=s.exec(e))?f:[],v=y.parse(null!=h?h:""),g=y.parse(null!=p?p:""),m="+"===d?v.add(g):v.subtract(g);if(m.isNaN())return"NaN";e=e.replace(s,m.toString())}return e}var g=/\(([^()]*)\)/;function m(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e,r=t;null!=(e=g.exec(r));){var[,n]=e;r=r.replace(g,v(n))}return r}(e),e=v(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],x=["dx","dy","angle","className","breakAll"];function w(){return(w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function O(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var j=/[ \f\n\r\t\v\u2028\u2029]+/,P=t=>{var{children:e,breakAll:r,style:n}=t;try{var i=[];(0,a.uy)(e)||(i=r?e.toString().split(""):e.toString().split(j));var o=i.map(t=>({word:t,width:(0,c.P)(t,n).width})),l=r?0:(0,c.P)("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:l}}catch(t){return null}},M=(t,e,r,n,i)=>{var o,{maxLines:l,children:c,style:u,breakAll:s}=t,f=(0,a.Et)(l),h=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce((t,e)=>{var{word:a,width:o}=e,l=t[t.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):t.push({words:[a],width:o}),t},[])},d=h(e),p=t=>t.reduce((t,e)=>t.width>e.width?t:e);if(!f||i||!(d.length>l||p(d).width>Number(n)))return d;for(var y=t=>{var e=h(P({breakAll:s,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>l||p(e).width>Number(n),e]},v=0,g=c.length-1,m=0;v<=g&&m<=c.length-1;){var b=Math.floor((v+g)/2),[x,w]=y(b-1),[O]=y(b);if(x||O||(v=b+1),x&&O&&(g=b-1),!x&&O){o=w;break}m++}return o||d},S=t=>[{words:(0,a.uy)(t)?[]:t.toString().split(j)}],A=t=>{var{width:e,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:l}=t;if((e||r)&&!o.m.isSsr){var c=P({breakAll:a,children:n,style:i});if(!c)return S(n);var{wordsWithComputedWidth:u,spaceWidth:s}=c;return M({breakAll:a,children:n,maxLines:l,style:i},u,s,e,r)}return S(n)},E="#808080",_=(0,n.forwardRef)((t,e)=>{var r,{x:o=0,y:c=0,lineHeight:u="1em",capHeight:s="0.71em",scaleToFit:f=!1,textAnchor:h="start",verticalAnchor:d="end",fill:p=E}=t,y=O(t,b),v=(0,n.useMemo)(()=>A({breakAll:y.breakAll,children:y.children,maxLines:y.maxLines,scaleToFit:f,style:y.style,width:y.width}),[y.breakAll,y.children,y.maxLines,f,y.style,y.width]),{dx:g,dy:j,angle:P,className:M,breakAll:S}=y,_=O(y,x);if(!(0,a.vh)(o)||!(0,a.vh)(c))return null;var k=o+((0,a.Et)(g)?g:0),T=c+((0,a.Et)(j)?j:0);switch(d){case"start":r=m("calc(".concat(s,")"));break;case"middle":r=m("calc(".concat((v.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:r=m("calc(".concat(v.length-1," * -").concat(u,")"))}var C=[];if(f){var D=v[0].width,{width:N}=y;C.push("scale(".concat((0,a.Et)(N)?N/D:1,")"))}return P&&C.push("rotate(".concat(P,", ").concat(k,", ").concat(T,")")),C.length&&(_.transform=C.join(" ")),n.createElement("text",w({},(0,l.J9)(_,!0),{ref:e,x:k,y:T,className:(0,i.$)("recharts-text",M),textAnchor:h,fill:p.includes("url")?E:p}),v.map((t,e)=>{var i=t.words.join(S?"":" ");return n.createElement("tspan",{x:k,dy:0===e?r:u,key:"".concat(i,"-").concat(e)},i)}))});_.displayName="Text"},23814:(t,e,r)=>{"use strict";r.d(e,{W:()=>a,h:()=>i});var n=r(84648),i=(0,n.Mz)(t=>t.cartesianAxis.xAxis,t=>Object.values(t)),a=(0,n.Mz)(t=>t.cartesianAxis.yAxis,t=>Object.values(t))},23854:(t,e,r)=>{t.exports=r(45263).uniqBy},24028:(t,e,r)=>{"use strict";r.d(e,{$:()=>i});var n=r(43209),i=()=>(0,n.G)(t=>t.rootProps.accessibilityLayer)},25679:(t,e,r)=>{"use strict";r.d(e,{f:()=>n});var n=t=>null;n.displayName="Cell"},25893:(t,e,r)=>{"use strict";r.d(e,{p:()=>i}),r(43210),r(32181);var n=r(43209);function i(t){return(0,n.j)(),null}},26349:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(48130);e.isArrayLike=function(t){return null!=t&&"function"!=typeof t&&n.isLength(t.length)}},26652:(t,e,r)=>{"use strict";r.d(e,{E:()=>n});var n=(0,r(43210).createContext)(null)},27469:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.argumentsTag="[object Arguments]",e.arrayBufferTag="[object ArrayBuffer]",e.arrayTag="[object Array]",e.bigInt64ArrayTag="[object BigInt64Array]",e.bigUint64ArrayTag="[object BigUint64Array]",e.booleanTag="[object Boolean]",e.dataViewTag="[object DataView]",e.dateTag="[object Date]",e.errorTag="[object Error]",e.float32ArrayTag="[object Float32Array]",e.float64ArrayTag="[object Float64Array]",e.functionTag="[object Function]",e.int16ArrayTag="[object Int16Array]",e.int32ArrayTag="[object Int32Array]",e.int8ArrayTag="[object Int8Array]",e.mapTag="[object Map]",e.numberTag="[object Number]",e.objectTag="[object Object]",e.regexpTag="[object RegExp]",e.setTag="[object Set]",e.stringTag="[object String]",e.symbolTag="[object Symbol]",e.uint16ArrayTag="[object Uint16Array]",e.uint32ArrayTag="[object Uint32Array]",e.uint8ArrayTag="[object Uint8Array]",e.uint8ClampedArrayTag="[object Uint8ClampedArray]"},27747:(t,e,r)=>{"use strict";r.d(e,{W:()=>v});var n=r(43210),i=r(49384),a=r(71579),o=r(43209);r(5338);var l=r(85621),c=r(69107),u=r(83409),s=["dangerouslySetInnerHTML","ticks"];function f(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h(){return(h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function d(t){return(0,o.j)(),null}var p=t=>{var{xAxisId:e,className:r}=t,f=(0,o.G)(c.c2),d=(0,u.r)(),p="xAxis",y=(0,o.G)(t=>(0,l.iV)(t,p,e,d)),v=(0,o.G)(t=>(0,l.Zi)(t,p,e,d)),g=(0,o.G)(t=>(0,l.Lw)(t,e)),m=(0,o.G)(t=>(0,l.L$)(t,e));if(null==g||null==m)return null;var{dangerouslySetInnerHTML:b,ticks:x}=t,w=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,s);return n.createElement(a.u,h({},w,{scale:y,x:m.x,y:m.y,width:g.width,height:g.height,className:(0,i.$)("recharts-".concat(p," ").concat(p),r),viewBox:f,ticks:v}))},y=t=>{var e,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(d,{interval:null!=(e=t.interval)?e:"preserveEnd",id:t.xAxisId,scale:t.scale,type:t.type,padding:t.padding,allowDataOverflow:t.allowDataOverflow,domain:t.domain,dataKey:t.dataKey,allowDuplicatedCategory:t.allowDuplicatedCategory,allowDecimals:t.allowDecimals,tickCount:t.tickCount,includeHidden:null!=(r=t.includeHidden)&&r,reversed:t.reversed,ticks:t.ticks,height:t.height,orientation:t.orientation,mirror:t.mirror,hide:t.hide,unit:t.unit,name:t.name,angle:null!=(i=t.angle)?i:0,minTickGap:null!=(a=t.minTickGap)?a:5,tick:null==(o=t.tick)||o,tickFormatter:t.tickFormatter}),n.createElement(p,t))};class v extends n.Component{render(){return n.createElement(y,this.props)}}f(v,"displayName","XAxis"),f(v,"defaultProps",{allowDataOverflow:l.PU.allowDataOverflow,allowDecimals:l.PU.allowDecimals,allowDuplicatedCategory:l.PU.allowDuplicatedCategory,height:l.PU.height,hide:!1,mirror:l.PU.mirror,orientation:l.PU.orientation,padding:l.PU.padding,reversed:l.PU.reversed,scale:l.PU.scale,tickCount:l.PU.tickCount,type:l.PU.type,xAxisId:0})},27977:(t,e,r)=>{"use strict";r.d(e,{g:()=>u});var n=r(84648),i=r(51426),a=r(69009),o=r(69107),l=r(21426),c=r(8920),u=(0,n.Mz)([(t,e)=>e,i.fz,c.D0,a.Re,a.gL,a.R4,l.r1,o.GO],l.aX)},28382:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.flatten=function(t,e=1){let r=[],n=Math.floor(e),i=(t,e)=>{for(let a=0;a<t.length;a++){let o=t[a];Array.isArray(o)&&e<n?i(o,e+1):r.push(o)}};return i(t,0),r}},28550:(t,e,r)=>{"use strict";r.d(e,{E:()=>i});var n=r(22989),i=(t,e)=>{var r,i=Number(e);if(!(0,n.M8)(i)&&null!=e)return i>=0?null==t||null==(r=t[i])?void 0:r.value:void 0}},29243:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(91428);e.isArguments=function(t){return null!==t&&"object"==typeof t&&"[object Arguments]"===n.getTag(t)}},29628:(t,e,r)=>{"use strict";r.d(e,{_:()=>b});var n=r(43210),i=r(98986),a=r(54186),o=r(4236),l=r(85621),c=r(43209),u=r(83409),s=t=>{var e=(0,u.r)();return(0,c.G)(r=>(0,l.Gx)(r,"xAxis",t,e))},f=t=>{var e=(0,u.r)();return(0,c.G)(r=>(0,l.Gx)(r,"yAxis",t,e))},h=r(73865),d=r(19420),p=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function y(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(){return(v=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function g(t){var{direction:e,width:r,dataKey:l,isAnimationActive:c,animationBegin:u,animationDuration:h,animationEasing:y}=t,g=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,p),m=(0,a.J9)(g,!1),{data:b,dataPointFormatter:x,xAxisId:w,yAxisId:O,errorBarOffset:j}=(0,o.G9)(),P=s(w),M=f(O);if((null==P?void 0:P.scale)==null||(null==M?void 0:M.scale)==null||null==b||"x"===e&&"number"!==P.type)return null;var S=b.map(t=>{var a,o,{x:s,y:f,value:p,errorVal:g}=x(t,l,e);if(!g)return null;var b=[];if(Array.isArray(g)?[a,o]=g:a=o=g,"x"===e){var{scale:w}=P,O=f+j,S=O+r,A=O-r,E=w(p-a),_=w(p+o);b.push({x1:_,y1:S,x2:_,y2:A}),b.push({x1:E,y1:O,x2:_,y2:O}),b.push({x1:E,y1:S,x2:E,y2:A})}else if("y"===e){var{scale:k}=M,T=s+j,C=T-r,D=T+r,N=k(p-a),I=k(p+o);b.push({x1:C,y1:I,x2:D,y2:I}),b.push({x1:T,y1:N,x2:T,y2:I}),b.push({x1:C,y1:N,x2:D,y2:N})}var z="".concat(s+j,"px ").concat(f+j,"px");return n.createElement(i.W,v({className:"recharts-errorBar",key:"bar-".concat(b.map(t=>"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)))},m),b.map(t=>{var e=c?{transformOrigin:"".concat(t.x1-5,"px")}:void 0;return n.createElement(d.i,{from:{transform:"scaleY(0)",transformOrigin:z},to:{transform:"scaleY(1)",transformOrigin:z},begin:u,easing:y,isActive:c,duration:h,key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2),style:{transformOrigin:z}},n.createElement("line",v({},t,{style:e})))}))});return n.createElement(i.W,{className:"recharts-errorBars"},S)}var m=(0,n.createContext)(void 0);function b(t){var{direction:e,children:r}=t;return n.createElement(m.Provider,{value:e},r)}var x={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function w(t){var e,r,i=(e=t.direction,r=(0,n.useContext)(m),null!=e?e:null!=r?r:"x"),{width:a,isAnimationActive:l,animationBegin:c,animationDuration:u,animationEasing:s}=(0,h.e)(t,x);return n.createElement(n.Fragment,null,n.createElement(o.pU,{dataKey:t.dataKey,direction:i}),n.createElement(g,v({},t,{direction:i,width:a,isAnimationActive:l,animationBegin:c,animationDuration:u,animationEasing:s})))}class O extends n.Component{render(){return n.createElement(w,this.props)}}y(O,"defaultProps",x),y(O,"displayName","ErrorBar")},29632:(t,e,r)=>{"use strict";t.exports=r(97668)},29862:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(92923),i=r(27469);e.cloneDeepWith=function(t,e){return n.cloneDeepWith(t,(r,a,o,l)=>{let c=e?.(r,a,o,l);if(null!=c)return c;if("object"==typeof t)switch(Object.prototype.toString.call(t)){case i.numberTag:case i.stringTag:case i.booleanTag:{let e=new t.constructor(t?.valueOf());return n.copyProperties(e,t),e}case i.argumentsTag:{let e={};return n.copyProperties(e,t),e.length=t.length,e[Symbol.iterator]=t[Symbol.iterator],e}default:return}})}},30415:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(42066),i=r(53038),a=r(59138),o=r(87509),l=r(57841);e.matchesProperty=function(t,e){switch(typeof t){case"object":Object.is(t?.valueOf(),-0)&&(t="-0");break;case"number":t=i.toKey(t)}return e=a.cloneDeep(e),function(r){let i=o.get(r,t);return void 0===i?l.has(r,t):void 0===e?void 0===i:n.isMatch(i,e)}}},30802:(t,e,r)=>{"use strict";r.d(e,{As:()=>s,Ch:()=>l,TK:()=>f,Vi:()=>u,g5:()=>c,iZ:()=>h,lm:()=>o});var n=r(76067),i=r(71392),a=(0,n.Z0)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(t){t.countOfBars+=1},removeBar(t){t.countOfBars-=1},addCartesianGraphicalItem(t,e){t.cartesianItems.push((0,i.h4)(e.payload))},removeCartesianGraphicalItem(t,e){var r=(0,i.ss)(t).cartesianItems.indexOf((0,i.h4)(e.payload));r>-1&&t.cartesianItems.splice(r,1)},addPolarGraphicalItem(t,e){t.polarItems.push((0,i.h4)(e.payload))},removePolarGraphicalItem(t,e){var r=(0,i.ss)(t).polarItems.indexOf((0,i.h4)(e.payload));r>-1&&t.polarItems.splice(r,1)}}}),{addBar:o,removeBar:l,addCartesianGraphicalItem:c,removeCartesianGraphicalItem:u,addPolarGraphicalItem:s,removePolarGraphicalItem:f}=a.actions,h=a.reducer},30921:(t,e,r)=>{t.exports=r(71337).range},32181:(t,e,r)=>{"use strict";r.d(e,{mZ:()=>l,vE:()=>o});var n=r(76067),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},a=(0,n.Z0)({name:"rootProps",initialState:i,reducers:{updateOptions:(t,e)=>{var r;t.accessibilityLayer=e.payload.accessibilityLayer,t.barCategoryGap=e.payload.barCategoryGap,t.barGap=null!=(r=e.payload.barGap)?r:i.barGap,t.barSize=e.payload.barSize,t.maxBarSize=e.payload.maxBarSize,t.stackOffset=e.payload.stackOffset,t.syncId=e.payload.syncId,t.syncMethod=e.payload.syncMethod,t.className=e.payload.className}}}),o=a.reducer,{updateOptions:l}=a.actions},32520:(t,e,r)=>{"use strict";r.d(e,{i:()=>o});var n=r(17118);function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var o=(t,e,r,i)=>{if(null==e)return n.k_;var o=function(t,e,r){return"axis"===e?"click"===r?t.axisInteraction.click:t.axisInteraction.hover:"click"===r?t.itemInteraction.click:t.itemInteraction.hover}(t,e,r);if(null==o)return n.k_;if(o.active)return o;if(t.keyboardInteraction.active)return t.keyboardInteraction;if(t.syncInteraction.active&&null!=t.syncInteraction.index)return t.syncInteraction;var l=!0===t.settings.active;if(null!=o.index){if(l)return a(a({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return a(a({},n.k_),{},{coordinate:o.coordinate})}},33731:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(97766),i=r(21424),a=r(26349);e.last=function(t){if(a.isArrayLike(t))return n.last(i.toArray(t))}},34258:(t,e,r)=>{"use strict";r.d(e,{e:()=>p,k:()=>y});var n=r(76067),i=r(17118),a=r(27977),o=r(77357),l=r(43075),c=r(75601),u=r(84648),s=r(72198),f=r(78242),h=(0,u.Mz)([f.J],t=>t.tooltipItemPayloads),d=(0,u.Mz)([h,s.x,(t,e,r)=>e,(t,e,r)=>r],(t,e,r,n)=>{var i=t.find(t=>t.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return e(a,r)}}),p=(0,n.VP)("touchMove"),y=(0,n.Nc)();y.startListening({actionCreator:p,effect:(t,e)=>{var r=t.payload,n=e.getState(),u=(0,l.au)(n,n.tooltip.settings.shared);if("axis"===u){var s=(0,a.g)(n,(0,o.w)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==s?void 0:s.activeIndex)!=null&&e.dispatch((0,i.Nt)({activeIndex:s.activeIndex,activeDataKey:void 0,activeCoordinate:s.activeCoordinate}))}else if("item"===u){var f,h=r.touches[0],p=document.elementFromPoint(h.clientX,h.clientY);if(!p||!p.getAttribute)return;var y=p.getAttribute(c.F0),v=null!=(f=p.getAttribute(c.um))?f:void 0,g=d(e.getState(),y,v);e.dispatch((0,i.RD)({activeDataKey:v,activeIndex:y,activeCoordinate:g}))}}})},34955:(t,e,r)=>{"use strict";r.d(e,{h:()=>y});var n=r(43210),i=r(49384),a=r(54186),o=r(19335),l=r(22989),c=r(73865);function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var s=(t,e)=>(0,l.sA)(e-t)*Math.min(Math.abs(e-t),359.999),f=t=>{var{cx:e,cy:r,radius:n,angle:i,sign:a,isExternal:l,cornerRadius:c,cornerIsExternal:u}=t,s=c*(l?1:-1)+n,f=Math.asin(c/s)/o.Kg,h=u?i:i+a*f,d=(0,o.IZ)(e,r,s,h);return{center:d,circleTangency:(0,o.IZ)(e,r,n,h),lineTangency:(0,o.IZ)(e,r,s*Math.cos(f*o.Kg),u?i-a*f:i),theta:f}},h=t=>{var{cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:l}=t,c=s(a,l),u=a+c,f=(0,o.IZ)(e,r,i,a),h=(0,o.IZ)(e,r,i,u),d="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(a>u),",\n    ").concat(h.x,",").concat(h.y,"\n  ");if(n>0){var p=(0,o.IZ)(e,r,n,a),y=(0,o.IZ)(e,r,n,u);d+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(a<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else d+="L ".concat(e,",").concat(r," Z");return d},d=t=>{var{cx:e,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:c,startAngle:u,endAngle:s}=t,d=(0,l.sA)(s-u),{circleTangency:p,lineTangency:y,theta:v}=f({cx:e,cy:r,radius:i,angle:u,sign:d,cornerRadius:a,cornerIsExternal:c}),{circleTangency:g,lineTangency:m,theta:b}=f({cx:e,cy:r,radius:i,angle:s,sign:-d,cornerRadius:a,cornerIsExternal:c}),x=c?Math.abs(u-s):Math.abs(u-s)-v-b;if(x<0)return o?"M ".concat(y.x,",").concat(y.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):h({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:s});var w="M ".concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(x>180),",").concat(+(d<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:j,theta:P}=f({cx:e,cy:r,radius:n,angle:u,sign:d,isExternal:!0,cornerRadius:a,cornerIsExternal:c}),{circleTangency:M,lineTangency:S,theta:A}=f({cx:e,cy:r,radius:n,angle:s,sign:-d,isExternal:!0,cornerRadius:a,cornerIsExternal:c}),E=c?Math.abs(u-s):Math.abs(u-s)-P-A;if(E<0&&0===a)return"".concat(w,"L").concat(e,",").concat(r,"Z");w+="L".concat(S.x,",").concat(S.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(E>180),",").concat(+(d>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(j.x,",").concat(j.y,"Z")}else w+="L".concat(e,",").concat(r,"Z");return w},p={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},y=t=>{var e,r=(0,c.e)(t,p),{cx:o,cy:s,innerRadius:f,outerRadius:y,cornerRadius:v,forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x,className:w}=r;if(y<f||b===x)return null;var O=(0,i.$)("recharts-sector",w),j=y-f,P=(0,l.F4)(v,j,0,!0);return e=P>0&&360>Math.abs(b-x)?d({cx:o,cy:s,innerRadius:f,outerRadius:y,cornerRadius:Math.min(P,j/2),forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x}):h({cx:o,cy:s,innerRadius:f,outerRadius:y,startAngle:b,endAngle:x}),n.createElement("path",u({},(0,a.J9)(r,!0),{className:O,d:e}))}},35314:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isUnsafeProperty=function(t){return"__proto__"===t}},36023:(t,e)=>{"use strict";function r(t){return"symbol"==typeof t?1:null===t?2:void 0===t?3:4*(t!=t)}Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.compareValues=(t,e,n)=>{if(t!==e){let i=r(t),a=r(e);if(i===a&&0===i){if(t<e)return"desc"===n?1:-1;if(t>e)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},36166:(t,e,r)=>{"use strict";r.d(e,{N:()=>n});var n=(t,e)=>e},36304:(t,e,r)=>{"use strict";r.d(e,{n:()=>a});var n=r(43210),i=r(22989);function a(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,i.NF)(e)),a=(0,n.useRef)(t);return a.current!==t&&(r.current=(0,i.NF)(e),a.current=t),r.current}},37586:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(36023),i=r(76021),a=r(43574);e.orderBy=function(t,e,r,o){if(null==t)return[];r=o?void 0:r,Array.isArray(t)||(t=Object.values(t)),Array.isArray(e)||(e=null==e?[null]:[e]),0===e.length&&(e=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(t=>String(t));let l=(t,e)=>{let r=t;for(let t=0;t<e.length&&null!=r;++t)r=r[e[t]];return r},c=(t,e)=>null==e||null==t?e:"object"==typeof t&&"key"in t?Object.hasOwn(e,t.key)?e[t.key]:l(e,t.path):"function"==typeof t?t(e):Array.isArray(t)?l(e,t):"object"==typeof e?e[t]:e,u=e.map(t=>(Array.isArray(t)&&1===t.length&&(t=t[0]),null==t||"function"==typeof t||Array.isArray(t)||i.isKey(t))?t:{key:t,path:a.toPath(t)});return t.map(t=>({original:t,criteria:u.map(e=>c(e,t))})).slice().sort((t,e)=>{for(let i=0;i<u.length;i++){let a=n.compareValues(t.criteria[i],e.criteria[i],r[i]);if(0!==a)return a}return 0}).map(t=>t.original)}},37625:(t,e,r)=>{"use strict";r.d(e,{r:()=>a}),r(43210);var n=r(43209);r(17118);var i=r(83409);function a(t){var{fn:e,args:r}=t;return(0,n.j)(),(0,i.r)(),null}},39733:(t,e,r)=>{"use strict";t.exports=r(10907)},40144:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(87509);e.property=function(t){return function(e){return n.get(e,t)}}},42066:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(14454);e.isMatch=function(t,e){return n.isMatchWith(t,e,()=>void 0)}},42750:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(15708);e.toFinite=function(t){return t?(t=n.toNumber(t))===1/0||t===-1/0?(t<0?-1:1)*Number.MAX_VALUE:t==t?t:0:0===t?t:0}},43075:(t,e,r)=>{"use strict";r.d(e,{$g:()=>o,Hw:()=>a,Td:()=>c,au:()=>l,xH:()=>i});var n=r(43209),i=t=>t.options.defaultTooltipEventType,a=t=>t.options.validateTooltipEventTypes;function o(t,e,r){if(null==t)return e;var n=t?"axis":"item";return null==r?e:r.includes(n)?n:e}function l(t,e){return o(e,i(t),a(t))}function c(t){return(0,n.G)(e=>l(e,t))}},43084:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(20911);e.throttle=function(t,e=0,r={}){"object"!=typeof r&&(r={});let{leading:i=!0,trailing:a=!0}=r;return n.debounce(t,e,{leading:i,trailing:a,maxWait:e})}},43209:(t,e,r)=>{"use strict";r.d(e,{G:()=>f,j:()=>l});var n=r(39733),i=r(43210),a=r(26652),o=t=>t,l=()=>{var t=(0,i.useContext)(a.E);return t?t.store.dispatch:o},c=()=>{},u=()=>c,s=(t,e)=>t===e;function f(t){var e=(0,i.useContext)(a.E);return(0,n.useSyncExternalStoreWithSelector)(e?e.subscription.addNestedSub:u,e?e.store.getState:c,e?e.store.getState:c,e?t:c,s)}},43574:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toPath=function(t){let e=[],r=t.length;if(0===r)return e;let n=0,i="",a="",o=!1;for(46===t.charCodeAt(0)&&(e.push(""),n++);n<r;){let l=t[n];a?"\\"===l&&n+1<r?i+=t[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,e.push(i),i=""):i+=l:"["===l?(o=!0,i&&(e.push(i),i="")):"."===l?i&&(e.push(i),i=""):i+=l,n++}return i&&e.push(i),e}},44919:(t,e,r)=>{"use strict";r.d(e,{x:()=>o,y:()=>a});var n=r(76067),i=r(69009),a=(0,n.VP)("externalEvent"),o=(0,n.Nc)();o.startListening({actionCreator:a,effect:(t,e)=>{if(null!=t.payload.handler){var r=e.getState(),n={activeCoordinate:(0,i.eE)(r),activeDataKey:(0,i.Xb)(r),activeIndex:(0,i.A2)(r),activeLabel:(0,i.BZ)(r),activeTooltipIndex:(0,i.A2)(r),isTooltipActive:(0,i.yn)(r)};t.payload.handler(n,t.payload.reactEvent)}}})},45263:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(59618),i=r(92681),a=r(90830),o=r(17617);e.uniqBy=function(t,e=i.identity){return a.isArrayLikeObject(t)?n.uniqBy(Array.from(t),o.iteratee(e)):[]}},45796:(t,e,r)=>{"use strict";r.d(e,{s:()=>a});var n=r(23854),i=r.n(n);function a(t,e,r){return!0===e?i()(t,r):"function"==typeof e?i()(t,e):t}},46993:(t,e,r)=>{"use strict";r.d(e,{Q:()=>c,l:()=>l});var n=r(43210),i=r(51426),a=r(43209),o=r(85621);function l(t,e){var r,n,i=(0,a.G)(e=>(0,o.Rl)(e,t)),l=(0,a.G)(t=>(0,o.sf)(t,e)),c=null!=(r=null==i?void 0:i.allowDataOverflow)?r:o.PU.allowDataOverflow,u=null!=(n=null==l?void 0:l.allowDataOverflow)?n:o.cd.allowDataOverflow;return{needClip:c||u,needClipX:c,needClipY:u}}function c(t){var{xAxisId:e,yAxisId:r,clipPathId:a}=t,o=(0,i.hj)(),{needClipX:c,needClipY:u,needClip:s}=l(e,r);if(!s)return null;var{left:f,top:h,width:d,height:p}=o;return n.createElement("clipPath",{id:"clipPath-".concat(a)},n.createElement("rect",{x:c?f:f-d/2,y:u?h:h-p/2,width:c?d:2*d,height:u?p:2*p}))}},48130:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isLength=function(t){return Number.isSafeInteger(t)&&t>=0}},48482:(t,e,r)=>{"use strict";r.d(e,{u:()=>f});var n=r(49384),i=r(43210),a=r(67766),o=r.n(a),l=r(22989),c=r(10521);function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var f=(0,i.forwardRef)((t,e)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:u="100%",height:f="100%",minWidth:h=0,minHeight:d,maxHeight:p,children:y,debounce:v=0,id:g,className:m,onResize:b,style:x={}}=t,w=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(e,()=>w.current);var[j,P]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),M=(0,i.useCallback)((t,e)=>{P(r=>{var n=Math.round(t),i=Math.round(e);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var t=t=>{var e,{width:r,height:n}=t[0].contentRect;M(r,n),null==(e=O.current)||e.call(O,r,n)};v>0&&(t=o()(t,v,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),{width:r,height:n}=w.current.getBoundingClientRect();return M(r,n),e.observe(w.current),()=>{e.disconnect()}},[M,v]);var S=(0,i.useMemo)(()=>{var{containerWidth:t,containerHeight:e}=j;if(t<0||e<0)return null;(0,c.R)((0,l._3)(u)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",u,f),(0,c.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(u)?t:u,a=(0,l._3)(f)?e:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),p&&a>p&&(a=p)),(0,c.R)(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,u,f,h,d,r),i.Children.map(y,t=>(0,i.cloneElement)(t,{width:n,height:a,style:s({height:"100%",width:"100%",maxHeight:a,maxWidth:n},t.props.style)}))},[r,y,f,p,d,h,j,u]);return i.createElement("div",{id:g?"".concat(g):void 0,className:(0,n.$)("recharts-responsive-container",m),style:s(s({},x),{},{width:u,height:f,minWidth:h,minHeight:d,maxHeight:p}),ref:w},S)})},48657:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}r.d(e,{A:()=>n}),Array.prototype.slice},49396:(t,e,r)=>{"use strict";r.d(e,{o:()=>n});var n=(t,e,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var c=o[0],u=null==c?void 0:l(c.positions,a);if(null!=u)return u;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+e)/2};else return{x:(n.left+t)/2,y:s.coordinate}}}},49605:(t,e,r)=>{"use strict";r.d(e,{dl:()=>c,lJ:()=>l,uN:()=>a});var n=r(76067),i=r(22989);function a(t,e){if(e){var r=Number.parseInt(e,10);if(!(0,i.M8)(r))return null==t?void 0:t[r]}}var o=(0,n.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:t=>{null==t.eventEmitter&&(t.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=o.reducer,{createEventEmitter:c}=o.actions},49899:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isObjectLike=function(t){return"object"==typeof t&&null!==t}},51426:(t,e,r)=>{"use strict";r.d(e,{Kp:()=>p,WX:()=>v,fz:()=>y,hj:()=>s,rY:()=>h,sk:()=>c,yi:()=>f}),r(43210);var n=r(43209),i=r(69107),a=r(86445),o=r(83409),l=r(94728),c=()=>{var t,e=(0,o.r)(),r=(0,n.G)(i.Ds),a=(0,n.G)(l.U),c=null==(t=(0,n.G)(l.C))?void 0:t.padding;return e&&a&&c?{width:a.width-c.left-c.right,height:a.height-c.top-c.bottom,x:c.left,y:c.top}:r},u={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var t;return null!=(t=(0,n.G)(i.GO))?t:u},f=()=>(0,n.G)(a.Lp),h=()=>(0,n.G)(a.A$),d={top:0,right:0,bottom:0,left:0},p=()=>{var t;return null!=(t=(0,n.G)(t=>t.layout.margin))?t:d},y=t=>t.layout.layoutType,v=()=>(0,n.G)(y)},52371:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(92923);e.cloneDeep=function(t){return n.cloneDeepWithImpl(t,void 0,t,new Map,void 0)}},52693:(t,e,r)=>{"use strict";r.d(e,{B_:()=>i,JK:()=>a,Vp:()=>c,gX:()=>o,hF:()=>l});var n=(0,r(76067).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(t,e){t.layoutType=e.payload},setChartSize(t,e){t.width=e.payload.width,t.height=e.payload.height},setMargin(t,e){t.margin.top=e.payload.top,t.margin.right=e.payload.right,t.margin.bottom=e.payload.bottom,t.margin.left=e.payload.left},setScale(t,e){t.scale=e.payload}}}),{setMargin:i,setLayout:a,setChartSize:o,setScale:l}=n.actions,c=n.reducer},53038:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.toKey=function(t){return"string"==typeof t||"symbol"==typeof t?t:Object.is(t?.valueOf?.(),-0)?"-0":String(t)}},53044:(t,e,r)=>{"use strict";r.d(e,{CU:()=>s,Lx:()=>c,h1:()=>l,hx:()=>o,u3:()=>u});var n=r(76067),i=r(71392),a=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(t,e){t.size.width=e.payload.width,t.size.height=e.payload.height},setLegendSettings(t,e){t.settings.align=e.payload.align,t.settings.layout=e.payload.layout,t.settings.verticalAlign=e.payload.verticalAlign},addLegendPayload(t,e){t.payload.push((0,i.h4)(e.payload))},removeLegendPayload(t,e){var r=(0,i.ss)(t).payload.indexOf((0,i.h4)(e.payload));r>-1&&t.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:l,addLegendPayload:c,removeLegendPayload:u}=a.actions,s=a.reducer},53416:(t,e,r)=>{"use strict";r.d(e,{I:()=>n});var n=(t,e)=>{if(t&&e)return null!=t&&t.reversed?[e[1],e[0]]:e}},54024:(t,e,r)=>{"use strict";r.d(e,{P:()=>p});var n=r(43210),i=r(64231),a=r(13420),o=r(71680),l=r(25893),c=r(2264),u=r(73865),s=r(12128),f=["width","height"];function h(){return(h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var d={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p=(0,n.forwardRef)(function(t,e){var r,p=(0,u.e)(t.categoricalChartProps,d),{width:y,height:v}=p,g=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(p,f);if(!(0,s.F)(y)||!(0,s.F)(v))return null;var{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,categoricalChartProps:O}=t;return n.createElement(i.J,{preloadedState:{options:{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,eventEmitter:void 0}},reduxStoreName:null!=(r=O.id)?r:m},n.createElement(a.TK,{chartData:O.data}),n.createElement(o.s,{width:y,height:v,layout:p.layout,margin:p.margin}),n.createElement(l.p,{accessibilityLayer:p.accessibilityLayer,barCategoryGap:p.barCategoryGap,maxBarSize:p.maxBarSize,stackOffset:p.stackOffset,barGap:p.barGap,barSize:p.barSize,syncId:p.syncId,syncMethod:p.syncMethod,className:p.className}),n.createElement(c.L,h({},g,{width:y,height:v,ref:e})))})},54186:(t,e,r)=>{"use strict";r.d(e,{J9:()=>v,aS:()=>d,y$:()=>p});var n=r(5664),i=r.n(n),a=r(43210),o=r(29632),l=r(22989),c=r(4057),u=t=>"string"==typeof t?t:t?t.displayName||t.name||"Component":"",s=null,f=null,h=t=>{if(t===s&&Array.isArray(f))return f;var e=[];return a.Children.forEach(t,t=>{(0,l.uy)(t)||((0,o.isFragment)(t)?e=e.concat(h(t.props.children)):e.push(t))}),f=e,s=t,e};function d(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(t=>u(t)):[u(e)],h(t).forEach(t=>{var e=i()(t,"type.displayName")||i()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}var p=t=>!t||"object"!=typeof t||!("clipDot"in t)||!!t.clipDot,y=(t,e,r,n)=>{var i,a=null!=(i=n&&(null===c.VU||void 0===c.VU?void 0:c.VU[n]))?i:[];return e.startsWith("data-")||"function"!=typeof t&&(n&&a.includes(e)||c.QQ.includes(e))||r&&c.j2.includes(e)},v=(t,e,r)=>{if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,a.isValidElement)(t)&&(n=t.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(t=>{var a;y(null==(a=n)?void 0:a[t],t,e,r)&&(i[t]=n[t])}),i}},55100:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.debounce=function(t,e,{signal:r,edges:n}={}){let i,a=null,o=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),c=()=>{null!==a&&(t.apply(i,a),i=void 0,a=null)},u=()=>{l&&c(),d()},s=null,f=()=>{null!=s&&clearTimeout(s),s=setTimeout(()=>{s=null,u()},e)},h=()=>{null!==s&&(clearTimeout(s),s=null)},d=()=>{h(),i=void 0,a=null},p=function(...t){if(r?.aborted)return;i=this,a=t;let e=null==s;f(),o&&e&&c()};return p.schedule=f,p.cancel=d,p.flush=()=>{h(),c()},r?.addEventListener("abort",d,{once:!0}),p}},57282:(t,e,r)=>{"use strict";r.d(e,{HS:()=>o,LF:()=>i,z3:()=>a});var n=r(84648),i=t=>t.chartData,a=(0,n.Mz)([i],t=>{var e=null!=t.chartData?t.chartData.length-1:0;return{chartData:t.chartData,computedData:t.computedData,dataEndIndex:e,dataStartIndex:0}}),o=(t,e,r,n)=>n?a(t):i(t)},57841:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(76431),i=r(98150),a=r(29243),o=r(43574);e.has=function(t,e){let r;if(0===(r=Array.isArray(e)?e:"string"==typeof e&&n.isDeepKey(e)&&t?.[e]==null?o.toPath(e):[e]).length)return!1;let l=t;for(let t=0;t<r.length;t++){let e=r[t];if((null==l||!Object.hasOwn(l,e))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(e)&&e<l.length))return!1;l=l[e]}return!0}},59138:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(29862);e.cloneDeep=function(t){return n.cloneDeepWith(t)}},59618:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.uniqBy=function(t,e){let r=new Map;for(let n=0;n<t.length;n++){let i=t[n],a=e(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},60324:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isPlainObject=function(t){if("object"!=typeof t||null==t)return!1;if(null===Object.getPrototypeOf(t))return!0;if("[object Object]"!==Object.prototype.toString.call(t)){let e=t[Symbol.toStringTag];return null!=e&&!!Object.getOwnPropertyDescriptor(t,Symbol.toStringTag)?.writable&&t.toString()===`[object ${e}]`}let e=t;for(;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}},60559:(t,e,r)=>{"use strict";r.d(e,{E:()=>n});var n=(t,e,r)=>r},61545:(t,e,r)=>{"use strict";r.d(e,{Cj:()=>a,Pg:()=>o,Ub:()=>l});var n=r(43209),i=r(17118),a=(t,e)=>{var r=(0,n.j)();return(n,a)=>o=>{null==t||t(n,a,o),r((0,i.RD)({activeIndex:String(a),activeDataKey:e,activeCoordinate:n.tooltipPosition}))}},o=t=>{var e=(0,n.j)();return(r,n)=>a=>{null==t||t(r,n,a),e((0,i.oP)())}},l=(t,e)=>{var r=(0,n.j)();return(n,a)=>o=>{null==t||t(n,a,o),r((0,i.ML)({activeIndex:String(a),activeDataKey:e,activeCoordinate:n.tooltipPosition}))}}},61645:(t,e,r)=>{"use strict";r.d(e,{J:()=>a,U:()=>i});var n=(0,r(76067).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(t,e)=>e.payload}}),{updatePolarOptions:i}=n.actions,a=n.reducer},64231:(t,e,r)=>{"use strict";r.d(e,{J:()=>th});var n=r(43210);r(6895);var i=Symbol.for("react.forward_ref"),a=Symbol.for("react.memo");function o(t){return t.dependsOnOwnProps?!!t.dependsOnOwnProps:1!==t.length}var l={notify(){},get:()=>[]},c="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,u="undefined"!=typeof navigator&&"ReactNative"===navigator.product,s=c||u?n.useLayoutEffect:n.useEffect;function f(t,e){return t===e?0!==t||0!==e||1/t==1/e:t!=t&&e!=e}var h={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},d={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},p={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},y={[i]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[a]:p};function v(t){return function(t){if("object"==typeof t&&null!==t){let{$$typeof:e}=t;switch(e){case null:switch(t=t.type){case null:case null:case null:case null:case null:return t;default:switch(t=t&&t.$$typeof){case null:case i:case null:case a:case null:return t;default:return e}}case null:return e}}}(t)===a?p:y[t.$$typeof]||h}var g=Object.defineProperty,m=Object.getOwnPropertyNames,b=Object.getOwnPropertySymbols,x=Object.getOwnPropertyDescriptor,w=Object.getPrototypeOf,O=Object.prototype,j=Symbol.for("react-redux-context"),P="undefined"!=typeof globalThis?globalThis:{},M=function(){if(!n.createContext)return{};let t=P[j]??=new Map,e=t.get(n.createContext);return e||(e=n.createContext(null),t.set(n.createContext,e)),e}(),S=function(t){let{children:e,context:r,serverState:i,store:a}=t,o=n.useMemo(()=>{let t=function(t,e){let r,n=l,i=0,a=!1;function o(){s.onStateChange&&s.onStateChange()}function c(){if(i++,!r){let e,i;r=t.subscribe(o),e=null,i=null,n={clear(){e=null,i=null},notify(){let t=e;for(;t;)t.callback(),t=t.next},get(){let t=[],r=e;for(;r;)t.push(r),r=r.next;return t},subscribe(t){let r=!0,n=i={callback:t,next:null,prev:i};return n.prev?n.prev.next=n:e=n,function(){r&&null!==e&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:e=n.next)}}}}}function u(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=l)}let s={addNestedSub:function(t){c();let e=n.subscribe(t),r=!1;return()=>{r||(r=!0,e(),u())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,c())},tryUnsubscribe:function(){a&&(a=!1,u())},getListeners:()=>n};return s}(a);return{store:a,subscription:t,getServerState:i?()=>i:void 0}},[a,i]),c=n.useMemo(()=>a.getState(),[a]);return s(()=>{let{subscription:t}=o;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),c!==a.getState()&&t.notifyNestedSubs(),()=>{t.tryUnsubscribe(),t.onStateChange=void 0}},[o,c]),n.createElement((r||M).Provider,{value:o},e)},A=r(11208),E=r(76067),_=r(49605),k=r(17118),T=r(64267),C=r(52693),D=r(85407);function N(t,e){return e instanceof HTMLElement?"HTMLElement <".concat(e.tagName,' class="').concat(e.className,'">'):e===window?"global.window":e}var I=r(5338),z=r(30802),L=r(71392),$=(0,E.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(t,e)=>{t.dots.push(e.payload)},removeDot:(t,e)=>{var r=(0,L.ss)(t).dots.findIndex(t=>t===e.payload);-1!==r&&t.dots.splice(r,1)},addArea:(t,e)=>{t.areas.push(e.payload)},removeArea:(t,e)=>{var r=(0,L.ss)(t).areas.findIndex(t=>t===e.payload);-1!==r&&t.areas.splice(r,1)},addLine:(t,e)=>{t.lines.push(e.payload)},removeLine:(t,e)=>{var r=(0,L.ss)(t).lines.findIndex(t=>t===e.payload);-1!==r&&t.lines.splice(r,1)}}}),{addDot:U,removeDot:R,addArea:B,removeArea:F,addLine:G,removeLine:K}=$.actions,H=$.reducer,W={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},Z=(0,E.Z0)({name:"brush",initialState:W,reducers:{setBrushSettings:(t,e)=>null==e.payload?W:e.payload}}),{setBrushSettings:q}=Z.actions,V=Z.reducer,Y=r(53044),J=r(32181),X=(0,E.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(t,e){t.radiusAxis[e.payload.id]=(0,L.h4)(e.payload)},removeRadiusAxis(t,e){delete t.radiusAxis[e.payload.id]},addAngleAxis(t,e){t.angleAxis[e.payload.id]=(0,L.h4)(e.payload)},removeAngleAxis(t,e){delete t.angleAxis[e.payload.id]}}}),{addRadiusAxis:Q,removeRadiusAxis:tt,addAngleAxis:te,removeAngleAxis:tr}=X.actions,tn=X.reducer,ti=r(61645),ta=r(11281),to=r(44919),tl=r(34258),tc=(0,A.HY)({brush:V,cartesianAxis:I.CA,chartData:T.LV,graphicalItems:z.iZ,layout:C.Vp,legend:Y.CU,options:_.lJ,polarAxis:tn,polarOptions:ti.J,referenceElements:H,rootProps:J.vE,tooltip:k.En}),tu=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,E.U1)({reducer:tc,preloadedState:t,middleware:t=>t({serializableCheck:!1}).concat([D.YF.middleware,D.fP.middleware,ta.$7.middleware,to.x.middleware,tl.k.middleware]),devTools:{serialize:{replacer:N},name:"recharts-".concat(e)}})},ts=r(83409),tf=r(26652);function th(t){var{preloadedState:e,children:r,reduxStoreName:i}=t,a=(0,ts.r)(),o=(0,n.useRef)(null);if(a)return r;null==o.current&&(o.current=tu(e,i));var l=tf.E;return n.createElement(S,{context:l,store:o.current},r)}},64267:(t,e,r)=>{"use strict";r.d(e,{LV:()=>l,M:()=>a,hq:()=>i});var n=(0,r(76067).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(t,e){if(t.chartData=e.payload,null==e.payload){t.dataStartIndex=0,t.dataEndIndex=0;return}e.payload.length>0&&t.dataEndIndex!==e.payload.length-1&&(t.dataEndIndex=e.payload.length-1)},setComputedData(t,e){t.computedData=e.payload},setDataStartEndIndexes(t,e){var{startIndex:r,endIndex:n}=e.payload;null!=r&&(t.dataStartIndex=r),null!=n&&(t.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:a,setComputedData:o}=n.actions,l=n.reducer},64279:(t,e,r)=>{"use strict";r.d(e,{qx:()=>I,IH:()=>N,s0:()=>b,gH:()=>m,SW:()=>B,YB:()=>j,bk:()=>R,Hj:()=>z,DW:()=>k,y2:()=>_,nb:()=>E,PW:()=>w,Mk:()=>D,$8:()=>A,yy:()=>S,Rh:()=>O,GF:()=>L,uM:()=>$,kr:()=>g,r4:()=>U,_L:()=>x,_f:()=>P});var n=r(10687),i=r.n(n),a=r(5664),o=r.n(a);function l(t,e){if((i=t.length)>1)for(var r,n,i,a=1,o=t[e[0]],l=o.length;a<i;++a)for(n=o,o=t[e[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var c=r(48657),u=r(22786);function s(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function f(t,e){return t[e]}function h(t){let e=[];return e.key=t,e}var d=r(22989),p=r(19335);function y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?y(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function g(t,e,r){return(0,d.uy)(t)||(0,d.uy)(e)?r:(0,d.vh)(e)?o()(t,e,r):"function"==typeof e?e(t):r}var m=(t,e,r,n,i)=>{var a,o=-1,l=null!=(a=null==e?void 0:e.length)?a:0;if(l<=1||null==t)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var c=0;c<l;c++){var u=c>0?r[c-1].coordinate:r[l-1].coordinate,s=r[c].coordinate,f=c>=l-1?r[0].coordinate:r[c+1].coordinate,h=void 0;if((0,d.sA)(s-u)!==(0,d.sA)(f-s)){var p=[];if((0,d.sA)(f-s)===(0,d.sA)(i[1]-i[0])){h=f;var y=s+i[1]-i[0];p[0]=Math.min(y,(y+u)/2),p[1]=Math.max(y,(y+u)/2)}else{h=u;var v=f+i[1]-i[0];p[0]=Math.min(s,(v+s)/2),p[1]=Math.max(s,(v+s)/2)}var g=[Math.min(s,(h+s)/2),Math.max(s,(h+s)/2)];if(t>g[0]&&t<=g[1]||t>=p[0]&&t<=p[1]){({index:o}=r[c]);break}}else{var m=Math.min(u,f),b=Math.max(u,f);if(t>(m+s)/2&&t<=(b+s)/2){({index:o}=r[c]);break}}}else if(e){for(var x=0;x<l;x++)if(0===x&&t<=(e[x].coordinate+e[x+1].coordinate)/2||x>0&&x<l-1&&t>(e[x].coordinate+e[x-1].coordinate)/2&&t<=(e[x].coordinate+e[x+1].coordinate)/2||x===l-1&&t>(e[x].coordinate+e[x-1].coordinate)/2){({index:o}=e[x]);break}}return o},b=(t,e,r)=>{if(e&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=e;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&(0,d.Et)(t[a]))return v(v({},t),{},{[a]:t[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&(0,d.Et)(t[o]))return v(v({},t),{},{[o]:t[o]+(i||0)})}return t},x=(t,e)=>"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e,w=(t,e,r,n)=>{if(n)return t.map(t=>t.coordinate);var i,a,o=t.map(t=>(t.coordinate===e&&(i=!0),t.coordinate===r&&(a=!0),t.coordinate));return i||o.push(e),a||o.push(r),o},O=(t,e,r)=>{if(!t)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:c,categoricalDomain:u,tickCount:s,ticks:f,niceTicks:h,axisType:p}=t;if(!o)return null;var y="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,v=(e||r)&&"category"===i&&o.bandwidth?o.bandwidth()/y:0;return(v="angleAxis"===p&&a&&a.length>=2?2*(0,d.sA)(a[0]-a[1])*v:v,e&&(f||h))?(f||h||[]).map((t,e)=>({coordinate:o(n?n.indexOf(t):t)+v,value:t,offset:v,index:e})).filter(t=>!(0,d.M8)(t.coordinate)):c&&u?u.map((t,e)=>({coordinate:o(t)+v,value:t,index:e,offset:v})):o.ticks&&!r&&null!=s?o.ticks(s).map((t,e)=>({coordinate:o(t)+v,value:t,offset:v,index:e})):o.domain().map((t,e)=>({coordinate:o(t)+v,value:n?n[t]:t,index:e,offset:v}))},j=t=>{var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=t(e[0]),l=t(e[r-1]);(o<i||o>a||l<i||l>a)&&t.domain([e[0],e[r-1]])}},P=(t,e)=>{if(!e||2!==e.length||!(0,d.Et)(e[0])||!(0,d.Et)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!(0,d.Et)(t[0])||t[0]<r)&&(i[0]=r),(!(0,d.Et)(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},M={sign:t=>{var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,a=0,o=0;o<e;++o){var l=(0,d.M8)(t[o][r][1])?t[o][r][0]:t[o][r][1];l>=0?(t[o][r][0]=i,t[o][r][1]=i+l,i=t[o][r][1]):(t[o][r][0]=a,t[o][r][1]=a+l,a=t[o][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,a=0,o=t[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=t[r][a][1]||0;if(i)for(r=0;r<n;++r)t[r][a][1]/=i}l(t,e)}},none:l,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],a=i.length;n<a;++n){for(var o=0,c=0;o<r;++o)c+=t[o][n][1]||0;i[n][1]+=i[n][0]=-c/2}l(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var c=0,u=0,s=0;c<i;++c){for(var f=t[e[c]],h=f[o][1]||0,d=(h-(f[o-1][1]||0))/2,p=0;p<c;++p){var y=t[e[p]];d+=(y[o][1]||0)-(y[o-1][1]||0)}u+=h,s+=d*h}r[o-1][1]+=r[o-1][0]=a,u&&(a-=s/u)}r[o-1][1]+=r[o-1][0]=a,l(t,e)}},positive:t=>{var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,a=0;a<e;++a){var o=(0,d.M8)(t[a][r][1])?t[a][r][0]:t[a][r][1];o>=0?(t[a][r][0]=i,t[a][r][1]=i+o,i=t[a][r][1]):(t[a][r][0]=0,t[a][r][1]=0)}}},S=(t,e,r)=>{var n=M[r];return(function(){var t=(0,u.A)([]),e=s,r=l,n=f;function i(i){var a,o,l=Array.from(t.apply(this,arguments),h),u=l.length,s=-1;for(let t of i)for(a=0,++s;a<u;++a)(l[a][s]=[0,+n(t,l[a].key,s,i)]).data=t;for(a=0,o=(0,c.A)(e(l));a<u;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,u.A)(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:(0,u.A)(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?s:"function"==typeof t?t:(0,u.A)(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?l:t,i):r},i})().keys(e).value((t,e)=>+g(t,e,0)).order(s).offset(n)(t)};function A(t){return null==t?void 0:String(t)}function E(t){var{axis:e,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=t;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!(0,d.uy)(i[e.dataKey])){var l=(0,d.eP)(r,"value",i[e.dataKey]);if(l)return l.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=g(i,(0,d.uy)(o)?e.dataKey:o);return(0,d.uy)(c)?null:e.scale(c)}var _=t=>{var{axis:e,ticks:r,offset:n,bandSize:i,entry:a,index:o}=t;if("category"===e.type)return r[o]?r[o].coordinate+n:null;var l=g(a,e.dataKey,e.scale.domain()[o]);return(0,d.uy)(l)?null:e.scale(l)-i/2+n},k=t=>{var{numericAxis:e}=t,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},T=t=>{var e=t.flat(2).filter(d.Et);return[Math.min(...e),Math.max(...e)]},C=t=>[t[0]===1/0?0:t[0],t[1]===-1/0?0:t[1]],D=(t,e,r)=>{if(null!=t)return C(Object.keys(t).reduce((n,i)=>{var{stackedData:a}=t[i],o=a.reduce((t,n)=>{var i=T(n.slice(e,r+1));return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},N=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,I=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,z=(t,e,r)=>{if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var a=i()(e,t=>t.coordinate),o=1/0,l=1,c=a.length;l<c;l++){var u=a[l],s=a[l-1];o=Math.min((u.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function L(t){var{tooltipEntrySettings:e,dataKey:r,payload:n,value:i,name:a}=t;return v(v({},e),{},{dataKey:r,payload:n,value:i,name:a})}function $(t,e){return t?String(t):"string"==typeof e?e:void 0}function U(t,e,r,n,i){return"horizontal"===r||"vertical"===r?t>=i.left&&t<=i.left+i.width&&e>=i.top&&e<=i.top+i.height?{x:t,y:e}:null:n?(0,p.yy)({x:t,y:e},n):null}var R=(t,e,r,n)=>{var i=e.find(t=>t&&t.index===r);if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var a=i.coordinate,{radius:o}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:c}=n;return v(v(v({},n),(0,p.IZ)(n.cx,n.cy,l,c)),{},{angle:c,radius:l})}return{x:0,y:0}},B=(t,e)=>"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius},66777:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(98150),i=r(26349),a=r(1640),o=r(1706);e.isIterateeCall=function(t,e,r){return!!a.isObject(r)&&(!!("number"==typeof e&&i.isArrayLike(r)&&n.isIndex(e))&&e<r.length||"string"==typeof e&&e in r)&&o.eq(r[e],t)}},67629:(t,e,r)=>{"use strict";r.d(e,{y:()=>j});var n=r(43210),i=r(92867),a=r.n(i),o=r(71524),l=r(49384),c=r(54186),u=r(73865),s=r(19420);function f(){return(f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var h=(t,e,r,n,i)=>{var a,o=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-o/2,",").concat(e+i)+"L ".concat(t+r-o/2-n,",").concat(e+i)+"L ".concat(t,",").concat(e," Z")},d={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},p=t=>{var e=(0,u.e)(t,d),r=(0,n.useRef)(),[i,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&a(t)}catch(t){}},[]);var{x:o,y:p,upperWidth:y,lowerWidth:v,height:g,className:m}=e,{animationEasing:b,animationDuration:x,animationBegin:w,isUpdateAnimationActive:O}=e;if(o!==+o||p!==+p||y!==+y||v!==+v||g!==+g||0===y&&0===v||0===g)return null;var j=(0,l.$)("recharts-trapezoid",m);return O?n.createElement(s.i,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:g,x:o,y:p},to:{upperWidth:y,lowerWidth:v,height:g,x:o,y:p},duration:x,animationEasing:b,isActive:O},t=>{var{upperWidth:a,lowerWidth:o,height:l,x:u,y:d}=t;return n.createElement(s.i,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,easing:b},n.createElement("path",f({},(0,c.J9)(e,!0),{className:j,d:h(u,d,a,o,l),ref:r})))}):n.createElement("g",null,n.createElement("path",f({},(0,c.J9)(e,!0),{className:j,d:h(o,p,y,v,g)})))},y=r(34955),v=r(98986),g=r(10919),m=["option","shapeType","propTransformer","activeClassName","isActive"];function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function w(t,e){return x(x({},e),t)}function O(t){var{shapeType:e,elementProps:r}=t;switch(e){case"rectangle":return n.createElement(o.M,r);case"trapezoid":return n.createElement(p,r);case"sector":return n.createElement(y.h,r);case"symbols":if("symbols"===e)return n.createElement(g.i,r);break;default:return null}}function j(t){var e,{option:r,shapeType:i,propTransformer:o=w,activeClassName:l="recharts-active-shape",isActive:c}=t,u=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,m);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,x(x({},u),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)e=r(u);else if(a()(r)&&"boolean"!=typeof r){var s=o(r,u);e=n.createElement(O,{shapeType:i,elementProps:s})}else e=n.createElement(O,{shapeType:i,elementProps:u});return c?n.createElement(v.W,{className:l},e):e}},67766:(t,e,r)=>{t.exports=r(43084).throttle},68392:(t,e,r)=>{"use strict";r.d(e,{V:()=>i});var n=r(43210);function i(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[e,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(t=>{if(null!=t){var n=t.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-e.height)>1||Math.abs(i.left-e.left)>1||Math.abs(i.top-e.top)>1||Math.abs(i.width-e.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[e.width,e.height,e.top,e.left,...t]);return[e,i]}},69009:(t,e,r)=>{"use strict";r.d(e,{BZ:()=>ti,eE:()=>tc,Xb:()=>ta,A2:()=>tn,yn:()=>tu,Dn:()=>j,gL:()=>q,fl:()=>V,R4:()=>X,Re:()=>w,n4:()=>_});var n=r(84648),i=r(85621),a=r(51426),o=r(64279),l=r(57282),c=r(97350),u=r(22989),s=r(53416),f=r(43075),h=r(28550),d=r(32520),p=r(97371),y=r(49396),v=r(86445),g=r(69107),m=r(77100),b=r(72198),x=r(78242),w=t=>{var e=(0,a.fz)(t);return"horizontal"===e?"xAxis":"vertical"===e?"yAxis":"centric"===e?"angleAxis":"radiusAxis"},O=t=>t.tooltip.settings.axisId,j=t=>{var e=w(t),r=O(t);return(0,i.Hd)(t,e,r)},P=(0,n.Mz)([j,a.fz,i.um,c.iO,w],i.sr),M=(0,n.Mz)([t=>t.graphicalItems.cartesianItems,t=>t.graphicalItems.polarItems],(t,e)=>[...t,...e]),S=(0,n.Mz)([w,O],i.eo),A=(0,n.Mz)([M,j,S],i.ec),E=(0,n.Mz)([A],i.rj),_=(0,n.Mz)([E,l.LF],i.Nk),k=(0,n.Mz)([_,j,A],i.fb),T=(0,n.Mz)([j],i.S5),C=(0,n.Mz)([_,A,c.eC],i.MK),D=(0,n.Mz)([C,l.LF,w],i.pM),N=(0,n.Mz)([A],i.IO),I=(0,n.Mz)([_,j,N,w],i.kz),z=(0,n.Mz)([i.Kr,w,O],i.P9),L=(0,n.Mz)([z,w],i.Oz),$=(0,n.Mz)([i.gT,w,O],i.P9),U=(0,n.Mz)([$,w],i.q),R=(0,n.Mz)([i.$X,w,O],i.P9),B=(0,n.Mz)([R,w],i.bb),F=(0,n.Mz)([L,B,U],i.yi),G=(0,n.Mz)([j,T,D,I,F],i.wL),K=(0,n.Mz)([j,a.fz,_,k,c.eC,w,G],i.tP),H=(0,n.Mz)([K,j,P],i.xp),W=(0,n.Mz)([j,K,H,w],i.g1),Z=t=>{var e=w(t),r=O(t);return(0,i.D5)(t,e,r,!1)},q=(0,n.Mz)([j,Z],s.I),V=(0,n.Mz)([j,P,W,q],i.Qn),Y=(0,n.Mz)([a.fz,k,j,w],i.tF),J=(0,n.Mz)([a.fz,k,j,w],i.iv),X=(0,n.Mz)([a.fz,j,P,V,Z,Y,J,w],(t,e,r,n,i,a,l,c)=>{if(e){var{type:s}=e,f=(0,o._L)(t,c);if(n){var h="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,d="category"===s&&n.bandwidth?n.bandwidth()/h:0;return(d="angleAxis"===c&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,u.sA)(i[0]-i[1])*d:d,f&&l)?l.map((t,e)=>({coordinate:n(t)+d,value:t,index:e,offset:d})):n.domain().map((t,e)=>({coordinate:n(t)+d,value:a?a[t]:t,index:e,offset:d}))}}}),Q=(0,n.Mz)([f.xH,f.Hw,t=>t.tooltip.settings],(t,e,r)=>(0,f.$g)(r.shared,t,e)),tt=t=>t.tooltip.settings.trigger,te=t=>t.tooltip.settings.defaultIndex,tr=(0,n.Mz)([x.J,Q,tt,te],d.i),tn=(0,n.Mz)([tr,_],p.P),ti=(0,n.Mz)([X,tn],h.E),ta=(0,n.Mz)([tr],t=>{if(t)return t.dataKey}),to=(0,n.Mz)([x.J,Q,tt,te],m.q),tl=(0,n.Mz)([v.Lp,v.A$,a.fz,g.GO,X,te,to,b.x],y.o),tc=(0,n.Mz)([tr,tl],(t,e)=>null!=t&&t.coordinate?t.coordinate:e),tu=(0,n.Mz)([tr],t=>t.active)},69107:(t,e,r)=>{"use strict";r.d(e,{Ds:()=>p,GO:()=>d,c2:()=>y});var n=r(84648),i=r(5664),a=r.n(i),o=r(23337),l=r(64279),c=r(86445),u=r(23814),s=r(75601);function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var d=(0,n.Mz)([c.Lp,c.A$,c.HK,t=>t.brush.height,u.h,u.W,o.ff,o.dc],(t,e,r,n,i,o,c,u)=>{var f=o.reduce((t,e)=>{var{orientation:r}=e;if(!e.mirror&&!e.hide){var n="number"==typeof e.width?e.width:s.tQ;return h(h({},t),{},{[r]:t[r]+n})}return t},{left:r.left||0,right:r.right||0}),d=i.reduce((t,e)=>{var{orientation:r}=e;return e.mirror||e.hide?t:h(h({},t),{},{[r]:a()(t,"".concat(r))+e.height})},{top:r.top||0,bottom:r.bottom||0}),p=h(h({},d),f),y=p.bottom;p.bottom+=n;var v=t-(p=(0,l.s0)(p,c,u)).left-p.right,g=e-p.top-p.bottom;return h(h({brushBottom:y},p),{},{width:Math.max(v,0),height:Math.max(g,0)})}),p=(0,n.Mz)(d,t=>({x:t.left,y:t.top,width:t.width,height:t.height})),y=(0,n.Mz)(c.Lp,c.A$,(t,e)=>({x:0,y:0,width:t,height:e}))},69404:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(175),i=r(91653),a=r(91428),o=r(27469),l=r(1706);e.isEqualWith=function(t,e,r){return function t(e,r,c,u,s,f,h){let d=h(e,r,c,u,s,f);if(void 0!==d)return d;if(typeof e==typeof r)switch(typeof e){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return e===r;case"number":return e===r||Object.is(e,r)}return function e(r,c,u,s){if(Object.is(r,c))return!0;let f=a.getTag(r),h=a.getTag(c);if(f===o.argumentsTag&&(f=o.objectTag),h===o.argumentsTag&&(h=o.objectTag),f!==h)return!1;switch(f){case o.stringTag:return r.toString()===c.toString();case o.numberTag:{let t=r.valueOf(),e=c.valueOf();return l.eq(t,e)}case o.booleanTag:case o.dateTag:case o.symbolTag:return Object.is(r.valueOf(),c.valueOf());case o.regexpTag:return r.source===c.source&&r.flags===c.flags;case o.functionTag:return r===c}let d=(u=u??new Map).get(r),p=u.get(c);if(null!=d&&null!=p)return d===c;u.set(r,c),u.set(c,r);try{switch(f){case o.mapTag:if(r.size!==c.size)return!1;for(let[e,n]of r.entries())if(!c.has(e)||!t(n,c.get(e),e,r,c,u,s))return!1;return!0;case o.setTag:{if(r.size!==c.size)return!1;let e=Array.from(r.values()),n=Array.from(c.values());for(let i=0;i<e.length;i++){let a=e[i],o=n.findIndex(e=>t(a,e,void 0,r,c,u,s));if(-1===o)return!1;n.splice(o,1)}return!0}case o.arrayTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:case o.bigUint64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.bigInt64ArrayTag:case o.float32ArrayTag:case o.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(r)!==Buffer.isBuffer(c)||r.length!==c.length)return!1;for(let e=0;e<r.length;e++)if(!t(r[e],c[e],e,r,c,u,s))return!1;return!0;case o.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return e(new Uint8Array(r),new Uint8Array(c),u,s);case o.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return e(new Uint8Array(r),new Uint8Array(c),u,s);case o.errorTag:return r.name===c.name&&r.message===c.message;case o.objectTag:{if(!(e(r.constructor,c.constructor,u,s)||n.isPlainObject(r)&&n.isPlainObject(c)))return!1;let a=[...Object.keys(r),...i.getSymbols(r)],o=[...Object.keys(c),...i.getSymbols(c)];if(a.length!==o.length)return!1;for(let e=0;e<a.length;e++){let n=a[e],i=r[n];if(!Object.hasOwn(c,n))return!1;let o=c[n];if(!t(i,o,n,r,c,u,s))return!1}return!0}default:return!1}}finally{u.delete(r),u.delete(c)}}(e,r,f,h)}(t,e,void 0,void 0,void 0,void 0,r)}},71337:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(66777),i=r(42750);e.range=function(t,e,r){r&&"number"!=typeof r&&n.isIterateeCall(t,e,r)&&(e=r=void 0),t=i.toFinite(t),void 0===e?(e=t,t=0):e=i.toFinite(e),r=void 0===r?t<e?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((e-t)/(r||1)),0),o=Array(a);for(let e=0;e<a;e++)o[e]=t,t+=r;return o}},71392:(t,e,r)=>{"use strict";r.d(e,{Qx:()=>u,a6:()=>s,h4:()=>H,jM:()=>K,ss:()=>F});var n,i=Symbol.for("immer-nothing"),a=Symbol.for("immer-draftable"),o=Symbol.for("immer-state");function l(t,...e){throw Error(`[Immer] minified error nr: ${t}. Full error at: https://bit.ly/3cXEKWf`)}var c=Object.getPrototypeOf;function u(t){return!!t&&!!t[o]}function s(t){return!!t&&(h(t)||Array.isArray(t)||!!t[a]||!!t.constructor?.[a]||g(t)||m(t))}var f=Object.prototype.constructor.toString();function h(t){if(!t||"object"!=typeof t)return!1;let e=c(t);if(null===e)return!0;let r=Object.hasOwnProperty.call(e,"constructor")&&e.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function d(t,e){0===p(t)?Reflect.ownKeys(t).forEach(r=>{e(r,t[r],t)}):t.forEach((r,n)=>e(n,r,t))}function p(t){let e=t[o];return e?e.type_:Array.isArray(t)?1:g(t)?2:3*!!m(t)}function y(t,e){return 2===p(t)?t.has(e):Object.prototype.hasOwnProperty.call(t,e)}function v(t,e,r){let n=p(t);2===n?t.set(e,r):3===n?t.add(r):t[e]=r}function g(t){return t instanceof Map}function m(t){return t instanceof Set}function b(t){return t.copy_||t.base_}function x(t,e){if(g(t))return new Map(t);if(m(t))return new Set(t);if(Array.isArray(t))return Array.prototype.slice.call(t);let r=h(t);if(!0!==e&&("class_only"!==e||r)){let e=c(t);return null!==e&&r?{...t}:Object.assign(Object.create(e),t)}{let e=Object.getOwnPropertyDescriptors(t);delete e[o];let r=Reflect.ownKeys(e);for(let n=0;n<r.length;n++){let i=r[n],a=e[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(e[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:t[i]})}return Object.create(c(t),e)}}function w(t,e=!1){return j(t)||u(t)||!s(t)||(p(t)>1&&(t.set=t.add=t.clear=t.delete=O),Object.freeze(t),e&&Object.entries(t).forEach(([t,e])=>w(e,!0))),t}function O(){l(2)}function j(t){return Object.isFrozen(t)}var P={};function M(t){let e=P[t];return e||l(0,t),e}function S(t,e){e&&(M("Patches"),t.patches_=[],t.inversePatches_=[],t.patchListener_=e)}function A(t){E(t),t.drafts_.forEach(k),t.drafts_=null}function E(t){t===n&&(n=t.parent_)}function _(t){return n={drafts_:[],parent_:n,immer_:t,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function k(t){let e=t[o];0===e.type_||1===e.type_?e.revoke_():e.revoked_=!0}function T(t,e){e.unfinalizedDrafts_=e.drafts_.length;let r=e.drafts_[0];return void 0!==t&&t!==r?(r[o].modified_&&(A(e),l(4)),s(t)&&(t=C(e,t),e.parent_||N(e,t)),e.patches_&&M("Patches").generateReplacementPatches_(r[o].base_,t,e.patches_,e.inversePatches_)):t=C(e,r,[]),A(e),e.patches_&&e.patchListener_(e.patches_,e.inversePatches_),t!==i?t:void 0}function C(t,e,r){if(j(e))return e;let n=e[o];if(!n)return d(e,(i,a)=>D(t,n,e,i,a,r)),e;if(n.scope_!==t)return e;if(!n.modified_)return N(t,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let e=n.copy_,i=e,a=!1;3===n.type_&&(i=new Set(e),e.clear(),a=!0),d(i,(i,o)=>D(t,n,e,i,o,r,a)),N(t,e,!1),r&&t.patches_&&M("Patches").generatePatches_(n,r,t.patches_,t.inversePatches_)}return n.copy_}function D(t,e,r,n,i,a,o){if(u(i)){let o=C(t,i,a&&e&&3!==e.type_&&!y(e.assigned_,n)?a.concat(n):void 0);if(v(r,n,o),!u(o))return;t.canAutoFreeze_=!1}else o&&r.add(i);if(s(i)&&!j(i)){if(!t.immer_.autoFreeze_&&t.unfinalizedDrafts_<1)return;C(t,i),(!e||!e.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&N(t,i)}}function N(t,e,r=!1){!t.parent_&&t.immer_.autoFreeze_&&t.canAutoFreeze_&&w(e,r)}var I={get(t,e){if(e===o)return t;let r=b(t);if(!y(r,e)){var n=t,i=r,a=e;let o=$(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let l=r[e];return t.finalized_||!s(l)?l:l===L(t.base_,e)?(R(t),t.copy_[e]=B(l,t)):l},has:(t,e)=>e in b(t),ownKeys:t=>Reflect.ownKeys(b(t)),set(t,e,r){let n=$(b(t),e);if(n?.set)return n.set.call(t.draft_,r),!0;if(!t.modified_){let n=L(b(t),e),i=n?.[o];if(i&&i.base_===r)return t.copy_[e]=r,t.assigned_[e]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||y(t.base_,e)))return!0;R(t),U(t)}return!!(t.copy_[e]===r&&(void 0!==r||e in t.copy_)||Number.isNaN(r)&&Number.isNaN(t.copy_[e]))||(t.copy_[e]=r,t.assigned_[e]=!0,!0)},deleteProperty:(t,e)=>(void 0!==L(t.base_,e)||e in t.base_?(t.assigned_[e]=!1,R(t),U(t)):delete t.assigned_[e],t.copy_&&delete t.copy_[e],!0),getOwnPropertyDescriptor(t,e){let r=b(t),n=Reflect.getOwnPropertyDescriptor(r,e);return n?{writable:!0,configurable:1!==t.type_||"length"!==e,enumerable:n.enumerable,value:r[e]}:n},defineProperty(){l(11)},getPrototypeOf:t=>c(t.base_),setPrototypeOf(){l(12)}},z={};function L(t,e){let r=t[o];return(r?b(r):t)[e]}function $(t,e){if(!(e in t))return;let r=c(t);for(;r;){let t=Object.getOwnPropertyDescriptor(r,e);if(t)return t;r=c(r)}}function U(t){!t.modified_&&(t.modified_=!0,t.parent_&&U(t.parent_))}function R(t){t.copy_||(t.copy_=x(t.base_,t.scope_.immer_.useStrictShallowCopy_))}function B(t,e){let r=g(t)?M("MapSet").proxyMap_(t,e):m(t)?M("MapSet").proxySet_(t,e):function(t,e){let r=Array.isArray(t),i={type_:+!!r,scope_:e?e.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:e,base_:t,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=I;r&&(a=[i],o=z);let{revoke:l,proxy:c}=Proxy.revocable(a,o);return i.draft_=c,i.revoke_=l,c}(t,e);return(e?e.scope_:n).drafts_.push(r),r}function F(t){return u(t)||l(10,t),function t(e){let r;if(!s(e)||j(e))return e;let n=e[o];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=x(e,n.scope_.immer_.useStrictShallowCopy_)}else r=x(e,!0);return d(r,(e,n)=>{v(r,e,t(n))}),n&&(n.finalized_=!1),r}(t)}d(I,(t,e)=>{z[t]=function(){return arguments[0]=arguments[0][0],e.apply(this,arguments)}}),z.deleteProperty=function(t,e){return z.set.call(this,t,e,void 0)},z.set=function(t,e,r){return I.set.call(this,t[0],e,r,t[0])};var G=new class{constructor(t){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(t,e,r)=>{let n;if("function"==typeof t&&"function"!=typeof e){let r=e;e=t;let n=this;return function(t=r,...i){return n.produce(t,t=>e.call(this,t,...i))}}if("function"!=typeof e&&l(6),void 0!==r&&"function"!=typeof r&&l(7),s(t)){let i=_(this),a=B(t,void 0),o=!0;try{n=e(a),o=!1}finally{o?A(i):E(i)}return S(i,r),T(n,i)}if(t&&"object"==typeof t)l(1,t);else{if(void 0===(n=e(t))&&(n=t),n===i&&(n=void 0),this.autoFreeze_&&w(n,!0),r){let e=[],i=[];M("Patches").generateReplacementPatches_(t,n,e,i),r(e,i)}return n}},this.produceWithPatches=(t,e)=>{let r,n;return"function"==typeof t?(e,...r)=>this.produceWithPatches(e,e=>t(e,...r)):[this.produce(t,e,(t,e)=>{r=t,n=e}),r,n]},"boolean"==typeof t?.autoFreeze&&this.setAutoFreeze(t.autoFreeze),"boolean"==typeof t?.useStrictShallowCopy&&this.setUseStrictShallowCopy(t.useStrictShallowCopy)}createDraft(t){s(t)||l(8),u(t)&&(t=F(t));let e=_(this),r=B(t,void 0);return r[o].isManual_=!0,E(e),r}finishDraft(t,e){let r=t&&t[o];r&&r.isManual_||l(9);let{scope_:n}=r;return S(n,e),T(void 0,n)}setAutoFreeze(t){this.autoFreeze_=t}setUseStrictShallowCopy(t){this.useStrictShallowCopy_=t}applyPatches(t,e){let r;for(r=e.length-1;r>=0;r--){let n=e[r];if(0===n.path.length&&"replace"===n.op){t=n.value;break}}r>-1&&(e=e.slice(r+1));let n=M("Patches").applyPatches_;return u(t)?n(t,e):this.produce(t,t=>n(t,e))}},K=G.produce;function H(t){return t}G.produceWithPatches.bind(G),G.setAutoFreeze.bind(G),G.setUseStrictShallowCopy.bind(G),G.applyPatches.bind(G),G.createDraft.bind(G),G.finishDraft.bind(G)},71524:(t,e,r)=>{"use strict";r.d(e,{M:()=>f});var n=r(43210),i=r(49384),a=r(54186),o=r(73865),l=r(19420);function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var u=(t,e,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,c=r>=0?1:-1,u=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(t,",").concat(e+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(u,",").concat(t+c*s[0],",").concat(e)),a+="L ".concat(t+r-c*s[1],",").concat(e),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(u,",\n        ").concat(t+r,",").concat(e+l*s[1])),a+="L ".concat(t+r,",").concat(e+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(u,",\n        ").concat(t+r-c*s[2],",").concat(e+n)),a+="L ".concat(t+c*s[3],",").concat(e+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(u,",\n        ").concat(t,",").concat(e+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var h=Math.min(o,i);a="M ".concat(t,",").concat(e+l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(u,",").concat(t+c*h,",").concat(e,"\n            L ").concat(t+r-c*h,",").concat(e,"\n            A ").concat(h,",").concat(h,",0,0,").concat(u,",").concat(t+r,",").concat(e+l*h,"\n            L ").concat(t+r,",").concat(e+n-l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(u,",").concat(t+r-c*h,",").concat(e+n,"\n            L ").concat(t+c*h,",").concat(e+n,"\n            A ").concat(h,",").concat(h,",0,0,").concat(u,",").concat(t,",").concat(e+n-l*h," Z")}else a="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},s={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=t=>{var e=(0,o.e)(t,s),r=(0,n.useRef)(null),[f,h]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&h(t)}catch(t){}},[]);var{x:d,y:p,width:y,height:v,radius:g,className:m}=e,{animationEasing:b,animationDuration:x,animationBegin:w,isAnimationActive:O,isUpdateAnimationActive:j}=e;if(d!==+d||p!==+p||y!==+y||v!==+v||0===y||0===v)return null;var P=(0,i.$)("recharts-rectangle",m);return j?n.createElement(l.i,{canBegin:f>0,from:{width:y,height:v,x:d,y:p},to:{width:y,height:v,x:d,y:p},duration:x,animationEasing:b,isActive:j},t=>{var{width:i,height:o,x:s,y:h}=t;return n.createElement(l.i,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,isActive:O,easing:b},n.createElement("path",c({},(0,a.J9)(e,!0),{className:P,d:u(s,h,i,o,g),ref:r})))}):n.createElement("path",c({},(0,a.J9)(e,!0),{className:P,d:u(d,p,y,v,g)}))}},71579:(t,e,r)=>{"use strict";r.d(e,{u:()=>O});var n=r(43210),i=r(5664),a=r.n(i),o=r(49384);function l(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}var c=r(98986),u=r(23561),s=r(97633),f=r(22989),h=r(4057),d=r(54186),p=r(17874),y=["viewBox"],v=["viewBox"];function g(){return(g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function m(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?m(Object(r),!0).forEach(function(e){w(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function x(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function w(t,e,r){var n;return(e="symbol"==typeof(n=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}class O extends n.Component{constructor(t){super(t),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(t,e){var{viewBox:r}=t,n=x(t,y),i=this.props,{viewBox:a}=i,o=x(i,v);return!l(r,a)||!l(n,o)||!l(e,this.state)}getTickLineCoord(t){var e,r,n,i,a,o,{x:l,y:c,width:u,height:s,orientation:h,tickSize:d,mirror:p,tickMargin:y}=this.props,v=p?-1:1,g=t.tickSize||d,m=(0,f.Et)(t.tickCoord)?t.tickCoord:t.coordinate;switch(h){case"top":e=r=t.coordinate,o=(n=(i=c+!p*s)-v*g)-v*y,a=m;break;case"left":n=i=t.coordinate,a=(e=(r=l+!p*u)-v*g)-v*y,o=m;break;case"right":n=i=t.coordinate,a=(e=(r=l+p*u)+v*g)+v*y,o=m;break;default:e=r=t.coordinate,o=(n=(i=c+p*s)+v*g)+v*y,a=m}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var t,{orientation:e,mirror:r}=this.props;switch(e){case"left":t=r?"start":"end";break;case"right":t=r?"end":"start";break;default:t="middle"}return t}getTickVerticalAnchor(){var{orientation:t,mirror:e}=this.props;switch(t){case"left":case"right":return"middle";case"top":return e?"start":"end";default:return e?"end":"start"}}renderAxisLine(){var{x:t,y:e,width:r,height:i,orientation:l,mirror:c,axisLine:u}=this.props,s=b(b(b({},(0,d.J9)(this.props,!1)),(0,d.J9)(u,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var f=+("top"===l&&!c||"bottom"===l&&c);s=b(b({},s),{},{x1:t,y1:e+f*i,x2:t+r,y2:e+f*i})}else{var h=+("left"===l&&!c||"right"===l&&c);s=b(b({},s),{},{x1:t+h*r,y1:e,x2:t+h*r,y2:e+i})}return n.createElement("line",g({},s,{className:(0,o.$)("recharts-cartesian-axis-line",a()(u,"className"))}))}static renderTickItem(t,e,r){var i,a=(0,o.$)(e.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(t))i=n.cloneElement(t,b(b({},e),{},{className:a}));else if("function"==typeof t)i=t(b(b({},e),{},{className:a}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof t&&(l=(0,o.$)(l,t.className)),i=n.createElement(u.E,g({},e,{className:l}),r)}return i}renderTicks(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:l,tick:u,tickFormatter:s,unit:f}=this.props,y=(0,p.f)(b(b({},this.props),{},{ticks:r}),t,e),v=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),x=(0,d.J9)(this.props,!1),w=(0,d.J9)(u,!1),j=b(b({},x),{},{fill:"none"},(0,d.J9)(i,!1)),P=y.map((t,e)=>{var{line:r,tick:d}=this.getTickLineCoord(t),p=b(b(b(b({textAnchor:v,verticalAnchor:m},x),{},{stroke:"none",fill:l},w),d),{},{index:e,payload:t,visibleTicksCount:y.length,tickFormatter:s});return n.createElement(c.W,g({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,h.XC)(this.props,t,e)),i&&n.createElement("line",g({},j,r,{className:(0,o.$)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),u&&O.renderTickItem(u,p,"".concat("function"==typeof s?s(t.value,e):t.value).concat(f||"")))});return P.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},P):null}render(){var{axisLine:t,width:e,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:l}=this.props;return null!=e&&e<=0||null!=r&&r<=0?null:n.createElement(c.W,{className:(0,o.$)("recharts-cartesian-axis",i),ref:t=>{if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(e);var r=e[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},t&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),s.J.renderCallByParent(this.props))}}w(O,"displayName","CartesianAxis"),w(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},71680:(t,e,r)=>{"use strict";r.d(e,{s:()=>a}),r(43210);var n=r(83409);r(52693);var i=r(43209);function a(t){var{layout:e,width:r,height:a,margin:o}=t;return(0,i.j)(),(0,n.r)(),null}},72198:(t,e,r)=>{"use strict";r.d(e,{x:()=>n});var n=t=>t.options.tooltipPayloadSearcher},73865:(t,e,r)=>{"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i(t,e){var r=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return Object.keys(e).reduce((t,r)=>(void 0===t[r]&&void 0!==e[r]&&(t[r]=e[r]),t),r)}r.d(e,{e:()=>i})},74838:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(42066),i=r(52371);e.matches=function(t){return t=i.cloneDeep(t),e=>n.isMatch(e,t)}},75446:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(37586),i=r(28382),a=r(66777);e.sortBy=function(t,...e){let r=e.length;return r>1&&a.isIterateeCall(t,e[0],e[1])?e=[]:r>2&&a.isIterateeCall(e[0],e[1],e[2])&&(e=[e[0]]),n.orderBy(t,i.flatten(e),["asc"])}},75601:(t,e,r)=>{"use strict";r.d(e,{F0:()=>n,tQ:()=>a,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",a=60},75787:(t,e,r)=>{"use strict";r.d(e,{p:()=>i,v:()=>a}),r(43210);var n=r(43209);function i(t){return(0,n.j)(),null}function a(t){return(0,n.j)(),null}r(30802),r(64279)},76021:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(95819),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.isKey=function(t,e){return!Array.isArray(t)&&(!!("number"==typeof t||"boolean"==typeof t||null==t||n.isSymbol(t))||"string"==typeof t&&(a.test(t)||!i.test(t))||null!=e&&Object.hasOwn(e,t))}},76067:(t,e,r)=>{"use strict";r.d(e,{U1:()=>m,VP:()=>u,Nc:()=>ty,Z0:()=>T});var n=r(11208);function i(t){return({dispatch:e,getState:r})=>n=>i=>"function"==typeof i?i(e,r,t):n(i)}var a=i(),o=r(71392),l="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var c=t=>t&&"function"==typeof t.match;function u(t,e){function r(...n){if(e){let r=e(...n);if(!r)throw Error(tw(0));return{type:t,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:t,payload:n[0]}}return r.toString=()=>`${t}`,r.type=t,r.match=e=>(0,n.ve)(e)&&e.type===t,r}function s(t){return["type","payload","error","meta"].indexOf(t)>-1}var f=class t extends Array{constructor(...e){super(...e),Object.setPrototypeOf(this,t.prototype)}static get[Symbol.species](){return t}concat(...t){return super.concat.apply(this,t)}prepend(...e){return 1===e.length&&Array.isArray(e[0])?new t(...e[0].concat(this)):new t(...e.concat(this))}};function h(t){return(0,o.a6)(t)?(0,o.jM)(t,()=>{}):t}function d(t,e,r){return t.has(e)?t.get(e):t.set(e,r(e)).get(e)}var p=()=>function(t){let{thunk:e=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=t??{},l=new f;return e&&("boolean"==typeof e?l.push(a):l.push(i(e.extraArgument))),l},y=t=>e=>{setTimeout(e,t)},v=(t={type:"raf"})=>e=>(...r)=>{let n=e(...r),i=!0,a=!1,o=!1,l=new Set,c="tick"===t.type?queueMicrotask:"raf"===t.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:y(10):"callback"===t.type?t.queueNotification:y(t.timeout),u=()=>{o=!1,a&&(a=!1,l.forEach(t=>t()))};return Object.assign({},n,{subscribe(t){let e=n.subscribe(()=>i&&t());return l.add(t),()=>{e(),l.delete(t)}},dispatch(t){try{return(a=!(i=!t?.meta?.RTK_autoBatch))&&!o&&(o=!0,c(u)),n.dispatch(t)}finally{i=!0}}})},g=t=>function(e){let{autoBatch:r=!0}=e??{},n=new f(t);return r&&n.push(v("object"==typeof r?r:void 0)),n};function m(t){let e,r,i=p(),{reducer:a,middleware:o,devTools:c=!0,duplicateMiddlewareCheck:u=!0,preloadedState:s,enhancers:f}=t||{};if("function"==typeof a)e=a;else if((0,n.Qd)(a))e=(0,n.HY)(a);else throw Error(tw(1));r="function"==typeof o?o(i):i();let h=n.Zz;c&&(h=l({trace:!1,..."object"==typeof c&&c}));let d=g((0,n.Tw)(...r)),y=h(..."function"==typeof f?f(d):d());return(0,n.y$)(e,s,y)}function b(t){let e,r={},n=[],i={addCase(t,e){let n="string"==typeof t?t:t.type;if(!n)throw Error(tw(28));if(n in r)throw Error(tw(29));return r[n]=e,i},addMatcher:(t,e)=>(n.push({matcher:t,reducer:e}),i),addDefaultCase:t=>(e=t,i)};return t(i),[r,n,e]}var x=(t,e)=>c(t)?t.match(e):t(e);function w(...t){return e=>t.some(t=>x(t,e))}var O=(t=21)=>{let e="",r=t;for(;r--;)e+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return e},j=["name","message","stack","code"],P=class{constructor(t,e){this.payload=t,this.meta=e}_type},M=class{constructor(t,e){this.payload=t,this.meta=e}_type},S=t=>{if("object"==typeof t&&null!==t){let e={};for(let r of j)"string"==typeof t[r]&&(e[r]=t[r]);return e}return{message:String(t)}},A="External signal was aborted";function E(t){if(t.meta&&t.meta.rejectedWithValue)throw t.payload;if(t.error)throw t.error;return t.payload}var _=Symbol.for("rtk-slice-createasyncthunk"),k=(t=>(t.reducer="reducer",t.reducerWithPrepare="reducerWithPrepare",t.asyncThunk="asyncThunk",t))(k||{}),T=function({creators:t}={}){let e=t?.asyncThunk?.[_];return function(t){let r,{name:n,reducerPath:i=n}=t;if(!n)throw Error(tw(11));let a=("function"==typeof t.reducers?t.reducers(function(){function t(t,e){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...e}}return t.withTypes=()=>t,{reducer:t=>Object.assign({[t.name]:(...e)=>t(...e)}[t.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(t,e)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:e}),asyncThunk:t}}()):t.reducers)||{},l=Object.keys(a),c={},s={},f={},p=[],y={addCase(t,e){let r="string"==typeof t?t:t.type;if(!r)throw Error(tw(12));if(r in s)throw Error(tw(13));return s[r]=e,y},addMatcher:(t,e)=>(p.push({matcher:t,reducer:e}),y),exposeAction:(t,e)=>(f[t]=e,y),exposeCaseReducer:(t,e)=>(c[t]=e,y)};function v(){let[e={},r=[],n]="function"==typeof t.extraReducers?b(t.extraReducers):[t.extraReducers],i={...e,...s};return function(t,e){let r,[n,i,a]=b(e);if("function"==typeof t)r=()=>h(t());else{let e=h(t);r=()=>e}function l(t=r(),e){let c=[n[e.type],...i.filter(({matcher:t})=>t(e)).map(({reducer:t})=>t)];return 0===c.filter(t=>!!t).length&&(c=[a]),c.reduce((t,r)=>{if(r)if((0,o.Qx)(t)){let n=r(t,e);return void 0===n?t:n}else{if((0,o.a6)(t))return(0,o.jM)(t,t=>r(t,e));let n=r(t,e);if(void 0===n){if(null===t)return t;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return t},t)}return l.getInitialState=r,l}(t.initialState,t=>{for(let e in i)t.addCase(e,i[e]);for(let e of p)t.addMatcher(e.matcher,e.reducer);for(let e of r)t.addMatcher(e.matcher,e.reducer);n&&t.addDefaultCase(n)})}l.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof t.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:t,reducerName:e},r,n,i){if(!i)throw Error(tw(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:c,settled:u,options:s}=r,f=i(t,a,s);n.exposeAction(e,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),c&&n.addCase(f.rejected,c),u&&n.addMatcher(f.settled,u),n.exposeCaseReducer(e,{fulfilled:o||C,pending:l||C,rejected:c||C,settled:u||C})}(o,i,y,e):function({type:t,reducerName:e,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(tw(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(t,a).exposeCaseReducer(e,a).exposeAction(e,o?u(t,o):u(t))}(o,i,y)});let g=t=>t,m=new Map,x=new WeakMap;function w(t,e){return r||(r=v()),r(t,e)}function O(){return r||(r=v()),r.getInitialState()}function j(e,r=!1){function n(t){let i=t[e];return void 0===i&&r&&(i=d(x,n,O)),i}function i(e=g){let n=d(m,r,()=>new WeakMap);return d(n,e,()=>{let n={};for(let[i,a]of Object.entries(t.selectors??{}))n[i]=function(t,e,r,n){function i(a,...o){let l=e(a);return void 0===l&&n&&(l=r()),t(l,...o)}return i.unwrapped=t,i}(a,e,()=>d(x,e,O),r);return n})}return{reducerPath:e,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let P={name:n,reducer:w,actions:f,caseReducers:c,getInitialState:O,...j(i),injectInto(t,{reducerPath:e,...r}={}){let n=e??i;return t.inject({reducerPath:n,reducer:w},r),{...P,...j(n,!0)}}};return P}}();function C(){}function D(t){return function(e,r){let n=e=>{isAction(r)&&Object.keys(r).every(s)?t(r.payload,e):t(r,e)};return(null)(e)?(n(e),e):createNextState3(e,n)}}function N(t,e){return e(t)}function I(t){return Array.isArray(t)||(t=Object.values(t)),t}var z="listener",L="completed",$="cancelled",U=`task-${$}`,R=`task-${L}`,B=`${z}-${$}`,F=`${z}-${L}`,G=class{constructor(t){this.code=t,this.message=`task ${$} (reason: ${t})`}name="TaskAbortError";message},K=(t,e)=>{if("function"!=typeof t)throw TypeError(tw(32))},H=()=>{},W=(t,e=H)=>(t.catch(e),t),Z=(t,e)=>(t.addEventListener("abort",e,{once:!0}),()=>t.removeEventListener("abort",e)),q=(t,e)=>{let r=t.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:e,configurable:!0,writable:!0}),t.abort(e))},V=t=>{if(t.aborted){let{reason:e}=t;throw new G(e)}};function Y(t,e){let r=H;return new Promise((n,i)=>{let a=()=>i(new G(t.reason));if(t.aborted)return void a();r=Z(t,a),e.finally(()=>r()).then(n,i)}).finally(()=>{r=H})}var J=async(t,e)=>{try{await Promise.resolve();let e=await t();return{status:"ok",value:e}}catch(t){return{status:t instanceof G?"cancelled":"rejected",error:t}}finally{e?.()}},X=t=>e=>W(Y(t,e).then(e=>(V(t),e))),Q=t=>{let e=X(t);return t=>e(new Promise(e=>setTimeout(e,t)))},{assign:tt}=Object,te={},tr="listenerMiddleware",tn=(t,e)=>{let r=e=>Z(t,()=>q(e,t.reason));return(n,i)=>{K(n,"taskExecutor");let a=new AbortController;r(a);let o=J(async()=>{V(t),V(a.signal);let e=await n({pause:X(a.signal),delay:Q(a.signal),signal:a.signal});return V(a.signal),e},()=>q(a,R));return i?.autoJoin&&e.push(o.catch(H)),{result:X(t)(o),cancel(){q(a,U)}}}},ti=(t,e)=>{let r=async(r,n)=>{V(e);let i=()=>{},a=[new Promise((e,n)=>{let a=t({predicate:r,effect:(t,r)=>{r.unsubscribe(),e([t,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(t=>setTimeout(t,n,null)));try{let t=await Y(e,Promise.race(a));return V(e),t}finally{i()}};return(t,e)=>W(r(t,e))},ta=t=>{let{type:e,actionCreator:r,matcher:n,predicate:i,effect:a}=t;if(e)i=u(e).match;else if(r)e=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(tw(21));return K(a,"options.listener"),{predicate:i,type:e,effect:a}},to=tt(t=>{let{type:e,predicate:r,effect:n}=ta(t);return{id:O(),effect:n,type:e,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(tw(22))}}},{withTypes:()=>to}),tl=(t,e)=>{let{type:r,effect:n,predicate:i}=ta(e);return Array.from(t.values()).find(t=>("string"==typeof r?t.type===r:t.predicate===i)&&t.effect===n)},tc=t=>{t.pending.forEach(t=>{q(t,B)})},tu=t=>()=>{t.forEach(tc),t.clear()},ts=(t,e,r)=>{try{t(e,r)}catch(t){setTimeout(()=>{throw t},0)}},tf=tt(u(`${tr}/add`),{withTypes:()=>tf}),th=u(`${tr}/removeAll`),td=tt(u(`${tr}/remove`),{withTypes:()=>td}),tp=(...t)=>{console.error(`${tr}/error`,...t)},ty=(t={})=>{let e=new Map,{extra:r,onError:i=tp}=t;K(i,"onError");let a=t=>(t.unsubscribe=()=>e.delete(t.id),e.set(t.id,t),e=>{t.unsubscribe(),e?.cancelActive&&tc(t)}),o=t=>a(tl(e,t)??to(t));tt(o,{withTypes:()=>o});let l=t=>{let r=tl(e,t);return r&&(r.unsubscribe(),t.cancelActive&&tc(r)),!!r};tt(l,{withTypes:()=>l});let c=async(t,n,a,l)=>{let c=new AbortController,u=ti(o,c.signal),s=[];try{t.pending.add(c),await Promise.resolve(t.effect(n,tt({},a,{getOriginalState:l,condition:(t,e)=>u(t,e).then(Boolean),take:u,delay:Q(c.signal),pause:X(c.signal),extra:r,signal:c.signal,fork:tn(c.signal,s),unsubscribe:t.unsubscribe,subscribe:()=>{e.set(t.id,t)},cancelActiveListeners:()=>{t.pending.forEach((t,e,r)=>{t!==c&&(q(t,B),r.delete(t))})},cancel:()=>{q(c,B),t.pending.delete(c)},throwIfCancelled:()=>{V(c.signal)}})))}catch(t){t instanceof G||ts(i,t,{raisedBy:"effect"})}finally{await Promise.all(s),q(c,F),t.pending.delete(c)}},u=tu(e);return{middleware:t=>r=>a=>{let s;if(!(0,n.ve)(a))return r(a);if(tf.match(a))return o(a.payload);if(th.match(a))return void u();if(td.match(a))return l(a.payload);let f=t.getState(),h=()=>{if(f===te)throw Error(tw(23));return f};try{if(s=r(a),e.size>0){let r=t.getState();for(let n of Array.from(e.values())){let e=!1;try{e=n.predicate(a,r,f)}catch(t){e=!1,ts(i,t,{raisedBy:"predicate"})}e&&c(n,a,t,h)}}}finally{f=te}return s},startListening:o,stopListening:l,clearListeners:u}},tv=t=>"reducerPath"in t&&"string"==typeof t.reducerPath,tg=Symbol.for("rtk-state-proxy-original"),tm=t=>!!t&&!!t[tg],tb=new WeakMap,tx={};function tw(t){return`Minified Redux Toolkit error #${t}; visit https://redux-toolkit.js.org/Errors?code=${t} for the full message or use the non-minified dev environment for full errors. `}},76431:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isDeepKey=function(t){switch(typeof t){case"number":case"symbol":return!1;case"string":return t.includes(".")||t.includes("[")||t.includes("]")}}},77100:(t,e,r)=>{"use strict";r.d(e,{q:()=>n});var n=(t,e,r,n)=>{var i;return"axis"===e?t.tooltipItemPayloads:0===t.tooltipItemPayloads.length?[]:null==(i="hover"===r?t.itemInteraction.hover.dataKey:t.itemInteraction.click.dataKey)&&null!=n?[t.tooltipItemPayloads[0]]:t.tooltipItemPayloads.filter(t=>{var e;return(null==(e=t.settings)?void 0:e.dataKey)===i})}},77357:(t,e,r)=>{"use strict";r.d(e,{w:()=>n});var n=t=>{var e=t.currentTarget.getBoundingClientRect(),r=e.width/t.currentTarget.offsetWidth,n=e.height/t.currentTarget.offsetHeight;return{chartX:Math.round((t.clientX-e.left)/r),chartY:Math.round((t.clientY-e.top)/n)}}},78242:(t,e,r)=>{"use strict";r.d(e,{J:()=>n});var n=t=>t.tooltip},81888:(t,e,r)=>{"use strict";r.d(e,{I:()=>G});var n=r(43210);function i(){}function a(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function o(t){this._context=t}function l(t){this._context=t}function c(t){this._context=t}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},l.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class u{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function h(t){return new f(t)}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function d(t,e,r){var n=t._x1-t._x0,i=e-t._x1,a=(t._y1-t._y0)/(n||i<0&&-0),o=(r-t._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function p(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function y(t,e,r){var n=t._x0,i=t._y0,a=t._x1,o=t._y1,l=(a-n)/3;t._context.bezierCurveTo(n+l,i+l*e,a-l,o-l*r,a,o)}function v(t){this._context=t}function g(t){this._context=new m(t)}function m(t){this._context=t}function b(t){this._context=t}function x(t){var e,r,n=t.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,a[e]=4,o[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/a[e-1],a[e]-=r,o[e]-=r*o[e-1];for(i[n-1]=o[n-1]/a[n-1],e=n-2;e>=0;--e)i[e]=(o[e]-i[e+1])/a[e];for(e=0,a[n-1]=(t[n]+i[n-1])/2;e<n-1;++e)a[e]=2*t[e+1]-i[e+1];return[i,a]}function w(t,e){this._context=t,this._t=e}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,p(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,y(this,p(this,r=d(this,t,e)),r);break;default:y(this,this._t0,r=d(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(g.prototype=Object.create(v.prototype)).point=function(t,e){v.prototype.point.call(this,e,t)},m.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,a){this._context.bezierCurveTo(e,t,n,r,a,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=x(t),i=x(e),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],t[o],e[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var O=r(48657),j=r(22786),P=r(15606);function M(t){return t[0]}function S(t){return t[1]}function A(t,e){var r=(0,j.A)(!0),n=null,i=h,a=null,o=(0,P.i)(l);function l(l){var c,u,s,f=(l=(0,O.A)(l)).length,h=!1;for(null==n&&(a=i(s=o())),c=0;c<=f;++c)!(c<f&&r(u=l[c],c,l))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+t(u,c,l),+e(u,c,l));if(s)return a=null,s+""||null}return t="function"==typeof t?t:void 0===t?M:(0,j.A)(t),e="function"==typeof e?e:void 0===e?S:(0,j.A)(e),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),l):t},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),l):e},l.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,j.A)(!!t),l):r},l.curve=function(t){return arguments.length?(i=t,null!=n&&(a=i(n)),l):i},l.context=function(t){return arguments.length?(null==t?n=a=null:a=i(n=t),l):n},l}function E(t,e,r){var n=null,i=(0,j.A)(!0),a=null,o=h,l=null,c=(0,P.i)(u);function u(u){var s,f,h,d,p,y=(u=(0,O.A)(u)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(p=c())),s=0;s<=y;++s){if(!(s<y&&i(d=u[s],s,u))===v)if(v=!v)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),h=s-1;h>=f;--h)l.point(g[h],m[h]);l.lineEnd(),l.areaEnd()}v&&(g[s]=+t(d,s,u),m[s]=+e(d,s,u),l.point(n?+n(d,s,u):g[s],r?+r(d,s,u):m[s]))}if(p)return l=null,p+""||null}function s(){return A().defined(i).curve(o).context(a)}return t="function"==typeof t?t:void 0===t?M:(0,j.A)(+t),e="function"==typeof e?e:void 0===e?(0,j.A)(0):(0,j.A)(+e),r="function"==typeof r?r:void 0===r?S:(0,j.A)(+r),u.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),n=null,u):t},u.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),u):t},u.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,j.A)(+t),u):n},u.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),r=null,u):e},u.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),u):e},u.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,j.A)(+t),u):r},u.lineX0=u.lineY0=function(){return s().x(t).y(e)},u.lineY1=function(){return s().x(t).y(r)},u.lineX1=function(){return s().x(n).y(e)},u.defined=function(t){return arguments.length?(i="function"==typeof t?t:(0,j.A)(!!t),u):i},u.curve=function(t){return arguments.length?(o=t,null!=a&&(l=o(a)),u):o},u.context=function(t){return arguments.length?(null==t?a=l=null:l=o(a=t),u):a},u}var _=r(49384),k=r(4057),T=r(54186),C=r(22989),D=r(12128);function N(){return(N=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function I(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function z(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?I(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var L={curveBasisClosed:function(t){return new l(t)},curveBasisOpen:function(t){return new c(t)},curveBasis:function(t){return new o(t)},curveBumpX:function(t){return new u(t,!0)},curveBumpY:function(t){return new u(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:h,curveMonotoneX:function(t){return new v(t)},curveMonotoneY:function(t){return new g(t)},curveNatural:function(t){return new b(t)},curveStep:function(t){return new w(t,.5)},curveStepAfter:function(t){return new w(t,1)},curveStepBefore:function(t){return new w(t,0)}},$=t=>(0,D.H)(t.x)&&(0,D.H)(t.y),U=t=>t.x,R=t=>t.y,B=(t,e)=>{if("function"==typeof t)return t;var r="curve".concat((0,C.Zb)(t));return("curveMonotone"===r||"curveBump"===r)&&e?L["".concat(r).concat("vertical"===e?"Y":"X")]:L[r]||h},F=t=>{var e,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=t,l=B(r,a),c=o?n.filter($):n;if(Array.isArray(i)){var u=o?i.filter(t=>$(t)):i,s=c.map((t,e)=>z(z({},t),{},{base:u[e]}));return(e="vertical"===a?E().y(R).x1(U).x0(t=>t.base.x):E().x(U).y1(R).y0(t=>t.base.y)).defined($).curve(l),e(s)}return(e="vertical"===a&&(0,C.Et)(i)?E().y(R).x1(U).x0(i):(0,C.Et)(i)?E().x(U).y1(R).y0(i):A().x(U).y(R)).defined($).curve(l),e(c)},G=t=>{var{className:e,points:r,path:i,pathRef:a}=t;if((!r||!r.length)&&!i)return null;var o=r&&r.length?F(t):i;return n.createElement("path",N({},(0,T.J9)(t,!1),(0,k._U)(t),{className:(0,_.$)("recharts-curve",e),d:null===o?void 0:o,ref:a}))}},83136:(t,e,r)=>{"use strict";r.d(e,{E:()=>c,O:()=>u});var n=r(43209),i=r(64279),a=r(69009);function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var c=()=>(0,n.G)(a.Dn),u=()=>{var t=c(),e=(0,n.G)(a.R4),r=(0,n.G)(a.fl);return(0,i.Hj)(l(l({},t),{},{scale:r}),e)}},83409:(t,e,r)=>{"use strict";r.d(e,{r:()=>a});var n=r(43210),i=(0,n.createContext)(null),a=()=>null!=(0,n.useContext)(i)},84648:(t,e,r)=>{"use strict";r.d(e,{Mz:()=>w});var n=t=>Array.isArray(t)?t:[t],i=0,a=null,o=class{revision=i;_value;_lastValue;_isEqual=l;constructor(t,e=l){this._value=this._lastValue=t,this._isEqual=e}get value(){return a?.add(this),this._value}set value(t){this.value!==t&&(this._value=t,this.revision=++i)}};function l(t,e){return t===e}function c(t){return t instanceof o||console.warn("Not a valid cell! ",t),t.value}var u=(t,e)=>!1;function s(){return function(t,e=l){return new o(null,e)}(0,u)}var f=t=>{let e=t.collectionTag;null===e&&(e=t.collectionTag=s()),c(e)};Symbol();var h=0,d=Object.getPrototypeOf({}),p=class{constructor(t){this.value=t,this.value=t,this.tag.value=t}proxy=new Proxy(this,y);tag=s();tags={};children={};collectionTag=null;id=h++},y={get:(t,e)=>(function(){let{value:r}=t,n=Reflect.get(r,e);if("symbol"==typeof e||e in d)return n;if("object"==typeof n&&null!==n){let r=t.children[e];return void 0===r&&(r=t.children[e]=function(t){return Array.isArray(t)?new v(t):new p(t)}(n)),r.tag&&c(r.tag),r.proxy}{let r=t.tags[e];return void 0===r&&((r=t.tags[e]=s()).value=n),c(r),n}})(),ownKeys:t=>(f(t),Reflect.ownKeys(t.value)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t.value,e),has:(t,e)=>Reflect.has(t.value,e)},v=class{constructor(t){this.value=t,this.value=t,this.tag.value=t}proxy=new Proxy([this],g);tag=s();tags={};children={};collectionTag=null;id=h++},g={get:([t],e)=>("length"===e&&f(t),y.get(t,e)),ownKeys:([t])=>y.ownKeys(t),getOwnPropertyDescriptor:([t],e)=>y.getOwnPropertyDescriptor(t,e),has:([t],e)=>y.has(t,e)},m="undefined"!=typeof WeakRef?WeakRef:class{constructor(t){this.value=t}deref(){return this.value}};function b(){return{s:0,v:void 0,o:null,p:null}}function x(t,e={}){let r,n=b(),{resultEqualityCheck:i}=e,a=0;function o(){let e,o=n,{length:l}=arguments;for(let t=0;t<l;t++){let e=arguments[t];if("function"==typeof e||"object"==typeof e&&null!==e){let t=o.o;null===t&&(o.o=t=new WeakMap);let r=t.get(e);void 0===r?(o=b(),t.set(e,o)):o=r}else{let t=o.p;null===t&&(o.p=t=new Map);let r=t.get(e);void 0===r?(o=b(),t.set(e,o)):o=r}}let c=o;if(1===o.s)e=o.v;else if(e=t.apply(null,arguments),a++,i){let t=r?.deref?.()??r;null!=t&&i(t,e)&&(e=t,0!==a&&a--),r="object"==typeof e&&null!==e||"function"==typeof e?new m(e):e}return c.s=1,c.v=e,e}return o.clearCache=()=>{n=b(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var w=function(t,...e){let r="function"==typeof t?{memoize:t,memoizeOptions:e}:t,i=(...t)=>{let e,i=0,a=0,o={},l=t.pop();"object"==typeof l&&(o=l,l=t.pop()),function(t,e=`expected a function, instead received ${typeof t}`){if("function"!=typeof t)throw TypeError(e)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:c,memoizeOptions:u=[],argsMemoize:s=x,argsMemoizeOptions:f=[],devModeChecks:h={}}={...r,...o},d=n(u),p=n(f),y=function(t){let e=Array.isArray(t[0])?t[0]:t;return!function(t,e="expected all items to be functions, instead received the following types: "){if(!t.every(t=>"function"==typeof t)){let r=t.map(t=>"function"==typeof t?`function ${t.name||"unnamed"}()`:typeof t).join(", ");throw TypeError(`${e}[${r}]`)}}(e,"createSelector expects all input-selectors to be functions, but received the following types: "),e}(t),v=c(function(){return i++,l.apply(null,arguments)},...d);return Object.assign(s(function(){a++;let t=function(t,e){let r=[],{length:n}=t;for(let i=0;i<n;i++)r.push(t[i].apply(null,e));return r}(y,arguments);return e=v.apply(null,t)},...p),{resultFunc:l,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>e,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:c,argsMemoize:s})};return Object.assign(i,{withTypes:()=>i}),i}(x),O=Object.assign((t,e=w)=>{!function(t,e=`expected an object, instead received ${typeof t}`){if("object"!=typeof t)throw TypeError(e)}(t,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof t}`);let r=Object.keys(t);return e(r.map(e=>t[e]),(...t)=>t.reduce((t,e,n)=>(t[r[n]]=e,t),{}))},{withTypes:()=>O})},85168:(t,e,r)=>{"use strict";r.d(e,{d:()=>C});var n=r(43210),i=r(10521),a=r(22989),o=r(54186),l=r(64279),c=r(17874),u=r(71579),s=r(51426),f=r(85621),h=r(43209),d=r(83409),p=r(73865),y=["x1","y1","x2","y2","key"],v=["offset"],g=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function w(){return(w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function O(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var j=t=>{var{fill:e}=t;if(!e||"none"===e)return null;var{fillOpacity:r,x:i,y:a,width:o,height:l,ry:c}=t;return n.createElement("rect",{x:i,y:a,ry:c,width:o,height:l,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function P(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if("function"==typeof t)r=t(e);else{var{x1:i,y1:a,x2:l,y2:c,key:u}=e,s=O(e,y),f=(0,o.J9)(s,!1),{offset:h}=f,d=O(f,v);r=n.createElement("line",w({},d,{x1:i,y1:a,x2:l,y2:c,fill:"none",key:u}))}return r}function M(t){var{x:e,width:r,horizontal:i=!0,horizontalPoints:a}=t;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=t,c=O(t,g),u=a.map((t,n)=>P(i,x(x({},c),{},{x1:e,y1:t,x2:e+r,y2:t,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function S(t){var{y:e,height:r,vertical:i=!0,verticalPoints:a}=t;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=t,c=O(t,m),u=a.map((t,n)=>P(i,x(x({},c),{},{x1:t,y1:e,x2:t,y2:e+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function A(t){var{horizontalFill:e,fillOpacity:r,x:i,y:a,width:o,height:l,horizontalPoints:c,horizontal:u=!0}=t;if(!u||!e||!e.length)return null;var s=c.map(t=>Math.round(t+a-a)).sort((t,e)=>t-e);a!==s[0]&&s.unshift(0);var f=s.map((t,c)=>{var u=s[c+1]?s[c+1]-t:a+l-t;if(u<=0)return null;var f=c%e.length;return n.createElement("rect",{key:"react-".concat(c),y:t,x:i,height:u,width:o,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function E(t){var{vertical:e=!0,verticalFill:r,fillOpacity:i,x:a,y:o,width:l,height:c,verticalPoints:u}=t;if(!e||!r||!r.length)return null;var s=u.map(t=>Math.round(t+a-a)).sort((t,e)=>t-e);a!==s[0]&&s.unshift(0);var f=s.map((t,e)=>{var u=s[e+1]?s[e+1]-t:a+l-t;if(u<=0)return null;var f=e%r.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:o,width:u,height:c,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var _=(t,e)=>{var{xAxis:r,width:n,height:i,offset:a}=t;return(0,l.PW)((0,c.f)(x(x(x({},u.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,e)},k=(t,e)=>{var{yAxis:r,width:n,height:i,offset:a}=t;return(0,l.PW)((0,c.f)(x(x(x({},u.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,e)},T={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function C(t){var e=(0,s.yi)(),r=(0,s.rY)(),o=(0,s.hj)(),l=x(x({},(0,p.e)(t,T)),{},{x:(0,a.Et)(t.x)?t.x:o.left,y:(0,a.Et)(t.y)?t.y:o.top,width:(0,a.Et)(t.width)?t.width:o.width,height:(0,a.Et)(t.height)?t.height:o.height}),{xAxisId:c,yAxisId:u,x:y,y:v,width:g,height:m,syncWithTicks:b,horizontalValues:O,verticalValues:P}=l,C=(0,d.r)(),D=(0,h.G)(t=>(0,f.ZB)(t,"xAxis",c,C)),N=(0,h.G)(t=>(0,f.ZB)(t,"yAxis",u,C));if(!(0,a.Et)(g)||g<=0||!(0,a.Et)(m)||m<=0||!(0,a.Et)(y)||y!==+y||!(0,a.Et)(v)||v!==+v)return null;var I=l.verticalCoordinatesGenerator||_,z=l.horizontalCoordinatesGenerator||k,{horizontalPoints:L,verticalPoints:$}=l;if((!L||!L.length)&&"function"==typeof z){var U=O&&O.length,R=z({yAxis:N?x(x({},N),{},{ticks:U?O:N.ticks}):void 0,width:e,height:r,offset:o},!!U||b);(0,i.R)(Array.isArray(R),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof R,"]")),Array.isArray(R)&&(L=R)}if((!$||!$.length)&&"function"==typeof I){var B=P&&P.length,F=I({xAxis:D?x(x({},D),{},{ticks:B?P:D.ticks}):void 0,width:e,height:r,offset:o},!!B||b);(0,i.R)(Array.isArray(F),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof F,"]")),Array.isArray(F)&&($=F)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(j,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(A,w({},l,{horizontalPoints:L})),n.createElement(E,w({},l,{verticalPoints:$})),n.createElement(M,w({},l,{offset:o,horizontalPoints:L,xAxis:D,yAxis:N})),n.createElement(S,w({},l,{offset:o,verticalPoints:$,xAxis:D,yAxis:N})))}C.displayName="CartesianGrid"},85407:(t,e,r)=>{"use strict";r.d(e,{YF:()=>u,dj:()=>s,fP:()=>f,ky:()=>c});var n=r(76067),i=r(17118),a=r(27977),o=r(43075),l=r(77357),c=(0,n.VP)("mouseClick"),u=(0,n.Nc)();u.startListening({actionCreator:c,effect:(t,e)=>{var r=t.payload,n=(0,a.g)(e.getState(),(0,l.w)(r));(null==n?void 0:n.activeIndex)!=null&&e.dispatch((0,i.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var s=(0,n.VP)("mouseMove"),f=(0,n.Nc)();f.startListening({actionCreator:s,effect:(t,e)=>{var r=t.payload,n=e.getState(),c=(0,o.au)(n,n.tooltip.settings.shared),u=(0,a.g)(n,(0,l.w)(r));"axis"===c&&((null==u?void 0:u.activeIndex)!=null?e.dispatch((0,i.Nt)({activeIndex:u.activeIndex,activeDataKey:void 0,activeCoordinate:u.activeCoordinate})):e.dispatch((0,i.xS)()))}})},85621:(t,e,r)=>{"use strict";r.d(e,{kz:()=>iT,fb:()=>ij,q:()=>iZ,tP:()=>i2,g1:()=>i7,iv:()=>aE,Nk:()=>iw,pM:()=>i_,Oz:()=>iH,tF:()=>aS,rj:()=>ib,ec:()=>iy,bb:()=>iV,xp:()=>i8,wL:()=>iQ,sr:()=>i3,Qn:()=>i5,MK:()=>iA,IO:()=>ig,P9:()=>iU,S5:()=>iz,PU:()=>ie,cd:()=>ii,eo:()=>ih,yi:()=>iL,ZB:()=>ak,D5:()=>al,iV:()=>au,Hd:()=>iu,Gx:()=>aD,DP:()=>ic,BQ:()=>aM,_y:()=>aI,AV:()=>iX,um:()=>is,xM:()=>i6,gT:()=>iB,Kr:()=>i$,$X:()=>iG,TC:()=>iE,Zi:()=>aT,CR:()=>aC,ld:()=>id,L$:()=>aO,Rl:()=>ir,Lw:()=>ag,KR:()=>aj,sf:()=>ia,wP:()=>aP});var n={};r.r(n),r.d(n,{scaleBand:()=>p,scaleDiverging:()=>function t(){var e=t$(rQ()(tx));return e.copy=function(){return rY(e,t())},c.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=tW(rQ()).domain([.1,1,10]);return e.copy=function(){return rY(e,t()).base(e.base())},c.apply(e,arguments)},scaleDivergingPow:()=>r0,scaleDivergingSqrt:()=>r1,scaleDivergingSymlog:()=>function t(){var e=tV(rQ());return e.copy=function(){return rY(e,t()).constant(e.constant())},c.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,tm),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,tm):[0,1],t$(n)},scaleImplicit:()=>h,scaleLinear:()=>function t(){var e=tS();return e.copy=function(){return tP(e,t())},l.apply(e,arguments),t$(e)},scaleLog:()=>function t(){let e=tW(tM()).domain([1,10]);return e.copy=()=>tP(e,t()).base(e.base()),l.apply(e,arguments),e},scaleOrdinal:()=>d,scalePoint:()=>y,scalePow:()=>t0,scaleQuantile:()=>function t(){var e,r=[],n=[],i=[];function a(){var t=0,e=Math.max(1,n.length);for(i=Array(e-1);++t<e;)i[t-1]=function(t,e,r=A){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,a=Math.floor(i),o=+r(t[a],a,t);return o+(r(t[a+1],a+1,t)-o)*(i-a)}}(r,t/e);return o}function o(t){return null==t||isNaN(t*=1)?e:n[_(i,t)]}return o.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:r[0],e<i.length?i[e]:r[r.length-1]]},o.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(j),a()},o.range=function(t){return arguments.length?(n=Array.from(t),a()):n.slice()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.quantiles=function(){return i.slice()},o.copy=function(){return t().domain(r).range(n).unknown(e)},l.apply(o,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,i=1,a=[.5],o=[0,1];function c(t){return null!=t&&t<=t?o[_(a,t,0,i)]:e}function u(){var t=-1;for(a=Array(i);++t<i;)a[t]=((t+1)*n-(t-i)*r)/(i+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(i=(o=Array.from(t)).length-1,u()):o.slice()},c.invertExtent=function(t){var e=o.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,a[0]]:e>=i?[a[i-1],n]:[a[e-1],a[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return a.slice()},c.copy=function(){return t().domain([r,n]).range(o).unknown(e)},l.apply(t$(c),arguments)},scaleRadial:()=>function t(){var e,r=tS(),n=[0,1],i=!1;function a(t){var n,a=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(a)?e:i?Math.round(a):a}return a.invert=function(t){return r.invert(t2(t))},a.domain=function(t){return arguments.length?(r.domain(t),a):r.domain()},a.range=function(t){return arguments.length?(r.range((n=Array.from(t,tm)).map(t2)),a):n.slice()},a.rangeRound=function(t){return a.range(t).round(!0)},a.round=function(t){return arguments.length?(i=!!t,a):i},a.clamp=function(t){return arguments.length?(r.clamp(t),a):r.clamp()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},l.apply(a,arguments),t$(a)},scaleSequential:()=>function t(){var e=t$(rV()(tx));return e.copy=function(){return rY(e,t())},c.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=tW(rV()).domain([1,10]);return e.copy=function(){return rY(e,t()).base(e.base())},c.apply(e,arguments)},scaleSequentialPow:()=>rJ,scaleSequentialQuantile:()=>function t(){var e=[],r=tx;function n(t){if(null!=t&&!isNaN(t*=1))return r((_(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(j),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return t3(t);if(e>=1)return t4(t);var n,i=(n-1)*e,a=Math.floor(i),o=t4((function t(e,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(e.length-1,i)),!(n<=r&&r<=i))return e;for(a=void 0===a?t6:function(t=j){if(t===j)return t6;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,c=Math.log(o),u=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*u*(o-u)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*u/o+s)),h=Math.min(i,Math.floor(r+(o-l)*u/o+s));t(e,r,f,h,a)}let o=e[r],l=n,c=i;for(t5(e,n,r),a(e[i],o)>0&&t5(e,n,i);l<c;){for(t5(e,l,c),++l,--c;0>a(e[l],o);)++l;for(;a(e[c],o)>0;)--c}0===a(e[n],o)?t5(e,n,c):t5(e,++c,i),c<=r&&(n=c+1),r<=c&&(i=c-1)}return e})(t,a).subarray(0,a+1));return o+(t3(t.subarray(a+1))-o)*(i-a)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},c.apply(n,arguments)},scaleSequentialSqrt:()=>rX,scaleSequentialSymlog:()=>function t(){var e=tV(rV());return e.copy=function(){return rY(e,t()).constant(e.constant())},c.apply(e,arguments)},scaleSqrt:()=>t1,scaleSymlog:()=>function t(){var e=tV(tM());return e.copy=function(){return tP(e,t()).constant(e.constant())},l.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],i=1;function a(t){return null!=t&&t<=t?n[_(r,t,0,i)]:e}return a.domain=function(t){return arguments.length?(i=Math.min((r=Array.from(t)).length,n.length-1),a):r.slice()},a.range=function(t){return arguments.length?(n=Array.from(t),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},a.unknown=function(t){return arguments.length?(e=t,a):e},a.copy=function(){return t().domain(r).range(n).unknown(e)},l.apply(a,arguments)},scaleTime:()=>rZ,scaleUtc:()=>rq,tickFormat:()=>tL});var i=r(84648),a=r(30921),o=r.n(a);function l(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function c(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class u extends Map{constructor(t,e=f){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(s(this,t))}has(t){return super.has(s(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function s({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function f(t){return null!==t&&"object"==typeof t?t.valueOf():t}let h=Symbol("implicit");function d(){var t=new u,e=[],r=[],n=h;function i(i){let a=t.get(i);if(void 0===a){if(n!==h)return n;t.set(i,a=e.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new u,r))t.has(n)||t.set(n,e.push(n)-1);return i},i.range=function(t){return arguments.length?(r=Array.from(t),i):r.slice()},i.unknown=function(t){return arguments.length?(n=t,i):n},i.copy=function(){return d(e,r).unknown(n)},l.apply(i,arguments),i}function p(){var t,e,r=d().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,c=!1,u=0,s=0,f=.5;function h(){var r=n().length,l=o<a,h=l?o:a,d=l?a:o;t=(d-h)/Math.max(1,r-u+2*s),c&&(t=Math.floor(t)),h+=(d-h-t*(r-u))*f,e=t*(1-u),c&&(h=Math.round(h),e=Math.round(e));var p=(function(t,e,r){t*=1,e*=1,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),a=Array(i);++n<i;)a[n]=t+n*r;return a})(r).map(function(e){return h+t*e});return i(l?p.reverse():p)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),h()):n()},r.range=function(t){return arguments.length?([a,o]=t,a*=1,o*=1,h()):[a,o]},r.rangeRound=function(t){return[a,o]=t,a*=1,o*=1,c=!0,h()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,h()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,s=+t),h()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),h()):u},r.paddingOuter=function(t){return arguments.length?(s=+t,h()):s},r.align=function(t){return arguments.length?(f=Math.max(0,Math.min(1,t)),h()):f},r.copy=function(){return p(n(),[a,o]).round(c).paddingInner(u).paddingOuter(s).align(f)},l.apply(h(),arguments)}function y(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(p.apply(null,arguments).paddingInner(1))}let v=Math.sqrt(50),g=Math.sqrt(10),m=Math.sqrt(2);function b(t,e,r){let n,i,a,o=(e-t)/Math.max(0,r),l=Math.floor(Math.log10(o)),c=o/Math.pow(10,l),u=c>=v?10:c>=g?5:c>=m?2:1;return(l<0?(n=Math.round(t*(a=Math.pow(10,-l)/u)),i=Math.round(e*a),n/a<t&&++n,i/a>e&&--i,a=-a):(n=Math.round(t/(a=Math.pow(10,l)*u)),i=Math.round(e/a),n*a<t&&++n,i*a>e&&--i),i<n&&.5<=r&&r<2)?b(t,e,2*r):[n,i,a]}function x(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[i,a,o]=n?b(e,t,r):b(t,e,r);if(!(a>=i))return[];let l=a-i+1,c=Array(l);if(n)if(o<0)for(let t=0;t<l;++t)c[t]=-((a-t)/o);else for(let t=0;t<l;++t)c[t]=(a-t)*o;else if(o<0)for(let t=0;t<l;++t)c[t]=-((i+t)/o);else for(let t=0;t<l;++t)c[t]=(i+t)*o;return c}function w(t,e,r){return b(t*=1,e*=1,r*=1)[2]}function O(t,e,r){e*=1,t*=1,r*=1;let n=e<t,i=n?w(e,t,r):w(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function j(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function P(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function M(t){let e,r,n;function i(t,n,a=0,o=t.length){if(a<o){if(0!==e(n,n))return o;do{let e=a+o>>>1;0>r(t[e],n)?a=e+1:o=e}while(a<o)}return a}return 2!==t.length?(e=j,r=(e,r)=>j(t(e),r),n=(e,r)=>t(e)-r):(e=t===j||t===P?t:S,r=t,n=t),{left:i,center:function(t,e,r=0,a=t.length){let o=i(t,e,r,a-1);return o>r&&n(t[o-1],e)>-n(t[o],e)?o-1:o},right:function(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>=r(t[e],n)?i=e+1:a=e}while(i<a)}return i}}}function S(){return 0}function A(t){return null===t?NaN:+t}let E=M(j),_=E.right;function k(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function T(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function C(){}E.left,M(A).center;var D="\\s*([+-]?\\d+)\\s*",N="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",I="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",z=/^#([0-9a-f]{3,8})$/,L=RegExp(`^rgb\\(${D},${D},${D}\\)$`),$=RegExp(`^rgb\\(${I},${I},${I}\\)$`),U=RegExp(`^rgba\\(${D},${D},${D},${N}\\)$`),R=RegExp(`^rgba\\(${I},${I},${I},${N}\\)$`),B=RegExp(`^hsl\\(${N},${I},${I}\\)$`),F=RegExp(`^hsla\\(${N},${I},${I},${N}\\)$`),G={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function K(){return this.rgb().formatHex()}function H(){return this.rgb().formatRgb()}function W(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=z.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?Z(e):3===r?new Y(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?q(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?q(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=L.exec(t))?new Y(e[1],e[2],e[3],1):(e=$.exec(t))?new Y(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=U.exec(t))?q(e[1],e[2],e[3],e[4]):(e=R.exec(t))?q(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=B.exec(t))?tr(e[1],e[2]/100,e[3]/100,1):(e=F.exec(t))?tr(e[1],e[2]/100,e[3]/100,e[4]):G.hasOwnProperty(t)?Z(G[t]):"transparent"===t?new Y(NaN,NaN,NaN,0):null}function Z(t){return new Y(t>>16&255,t>>8&255,255&t,1)}function q(t,e,r,n){return n<=0&&(t=e=r=NaN),new Y(t,e,r,n)}function V(t,e,r,n){var i;return 1==arguments.length?((i=t)instanceof C||(i=W(i)),i)?new Y((i=i.rgb()).r,i.g,i.b,i.opacity):new Y:new Y(t,e,r,null==n?1:n)}function Y(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function J(){return`#${te(this.r)}${te(this.g)}${te(this.b)}`}function X(){let t=Q(this.opacity);return`${1===t?"rgb(":"rgba("}${tt(this.r)}, ${tt(this.g)}, ${tt(this.b)}${1===t?")":`, ${t})`}`}function Q(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function tt(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function te(t){return((t=tt(t))<16?"0":"")+t.toString(16)}function tr(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new ti(t,e,r,n)}function tn(t){if(t instanceof ti)return new ti(t.h,t.s,t.l,t.opacity);if(t instanceof C||(t=W(t)),!t)return new ti;if(t instanceof ti)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),a=Math.max(e,r,n),o=NaN,l=a-i,c=(a+i)/2;return l?(o=e===a?(r-n)/l+(r<n)*6:r===a?(n-e)/l+2:(e-r)/l+4,l/=c<.5?a+i:2-a-i,o*=60):l=c>0&&c<1?0:o,new ti(o,l,c,t.opacity)}function ti(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function ta(t){return(t=(t||0)%360)<0?t+360:t}function to(t){return Math.max(0,Math.min(1,t||0))}function tl(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function tc(t,e,r,n,i){var a=t*t,o=a*t;return((1-3*t+3*a-o)*e+(4-6*a+3*o)*r+(1+3*t+3*a-3*o)*n+o*i)/6}k(C,W,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:K,formatHex:K,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return tn(this).formatHsl()},formatRgb:H,toString:H}),k(Y,V,T(C,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new Y(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new Y(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Y(tt(this.r),tt(this.g),tt(this.b),Q(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:J,formatHex:J,formatHex8:function(){return`#${te(this.r)}${te(this.g)}${te(this.b)}${te((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:X,toString:X})),k(ti,function(t,e,r,n){return 1==arguments.length?tn(t):new ti(t,e,r,null==n?1:n)},T(C,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new ti(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new ti(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new Y(tl(t>=240?t-240:t+120,i,n),tl(t,i,n),tl(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new ti(ta(this.h),to(this.s),to(this.l),Q(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=Q(this.opacity);return`${1===t?"hsl(":"hsla("}${ta(this.h)}, ${100*to(this.s)}%, ${100*to(this.l)}%${1===t?")":`, ${t})`}`}}));let tu=t=>()=>t;function ts(t,e){var r,n,i=e-t;return i?(r=t,n=i,function(t){return r+t*n}):tu(isNaN(t)?e:t)}let tf=function t(e){var r,n=1==(r=+e)?ts:function(t,e){var n,i,a;return e-t?(n=t,i=e,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(t){return Math.pow(n+t*i,a)}):tu(isNaN(t)?e:t)};function i(t,e){var r=n((t=V(t)).r,(e=V(e)).r),i=n(t.g,e.g),a=n(t.b,e.b),o=ts(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=a(e),t.opacity=o(e),t+""}}return i.gamma=t,i}(1);function th(t){return function(e){var r,n,i=e.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=V(e[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=t(a),o=t(o),l=t(l),n.opacity=1,function(t){return n.r=a(t),n.g=o(t),n.b=l(t),n+""}}}th(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],a=t[n+1],o=n>0?t[n-1]:2*i-a,l=n<e-1?t[n+2]:2*a-i;return tc((r-n/e)*e,o,i,a,l)}}),th(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],a=t[n%e],o=t[(n+1)%e],l=t[(n+2)%e];return tc((r-n/e)*e,i,a,o,l)}});function td(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var tp=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ty=RegExp(tp.source,"g");function tv(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?tu(e):("number"===i?td:"string"===i?(n=W(e))?(e=n,tf):function(t,e){var r,n,i,a,o,l=tp.lastIndex=ty.lastIndex=0,c=-1,u=[],s=[];for(t+="",e+="";(i=tp.exec(t))&&(a=ty.exec(e));)(o=a.index)>l&&(o=e.slice(l,o),u[c]?u[c]+=o:u[++c]=o),(i=i[0])===(a=a[0])?u[c]?u[c]+=a:u[++c]=a:(u[++c]=null,s.push({i:c,x:td(i,a)})),l=ty.lastIndex;return l<e.length&&(o=e.slice(l),u[c]?u[c]+=o:u[++c]=o),u.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)u[(r=s[n]).i]=r.x(t);return u.join("")})}:e instanceof W?tf:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=tv(t[r],e[r]);for(;r<n;++r)o[r]=e[r];return function(t){for(r=0;r<i;++r)o[r]=a[r](t);return o}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=tv(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:td:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(a){for(r=0;r<n;++r)i[r]=t[r]*(1-a)+e[r]*a;return i}})(t,e)}function tg(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function tm(t){return+t}var tb=[0,1];function tx(t){return t}function tw(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function tO(t,e,r){var n=t[0],i=t[1],a=e[0],o=e[1];return i<n?(n=tw(i,n),a=r(o,a)):(n=tw(n,i),a=r(a,o)),function(t){return a(n(t))}}function tj(t,e,r){var n=Math.min(t.length,e.length)-1,i=Array(n),a=Array(n),o=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++o<n;)i[o]=tw(t[o],t[o+1]),a[o]=r(e[o],e[o+1]);return function(e){var r=_(t,e,1,n)-1;return a[r](i[r](e))}}function tP(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function tM(){var t,e,r,n,i,a,o=tb,l=tb,c=tv,u=tx;function s(){var t,e,r,c=Math.min(o.length,l.length);return u!==tx&&(t=o[0],e=o[c-1],t>e&&(r=t,t=e,e=r),u=function(r){return Math.max(t,Math.min(e,r))}),n=c>2?tj:tO,i=a=null,f}function f(e){return null==e||isNaN(e*=1)?r:(i||(i=n(o.map(t),l,c)))(t(u(e)))}return f.invert=function(r){return u(e((a||(a=n(l,o.map(t),td)))(r)))},f.domain=function(t){return arguments.length?(o=Array.from(t,tm),s()):o.slice()},f.range=function(t){return arguments.length?(l=Array.from(t),s()):l.slice()},f.rangeRound=function(t){return l=Array.from(t),c=tg,s()},f.clamp=function(t){return arguments.length?(u=!!t||tx,s()):u!==tx},f.interpolate=function(t){return arguments.length?(c=t,s()):c},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function tS(){return tM()(tx,tx)}var tA=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tE(t){var e;if(!(e=tA.exec(t)))throw Error("invalid format: "+t);return new t_({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function t_(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tk(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function tT(t){return(t=tk(Math.abs(t)))?t[1]:NaN}function tC(t,e){var r=tk(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}tE.prototype=t_.prototype,t_.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let tD={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tC(100*t,e),r:tC,s:function(t,e){var r=tk(t,e);if(!r)return t+"";var n=r[0],i=r[1],a=i-(r7=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=n.length;return a===o?n:a>o?n+Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+Array(1-a).join("0")+tk(t,Math.max(0,e+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function tN(t){return t}var tI=Array.prototype.map,tz=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tL(t,e,r,n){var i,a,o,l=O(t,e,r);switch((n=tE(null==n?",f":n)).type){case"s":var c=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(o=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tT(c)/3)))-tT(Math.abs(l))))||(n.precision=o),nr(n,c);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(o=Math.max(0,tT(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(i=Math.abs(i=l)))-tT(i))+1)||(n.precision=o-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(o=Math.max(0,-tT(Math.abs(l))))||(n.precision=o-("%"===n.type)*2)}return ne(n)}function t$(t){var e=t.domain;return t.ticks=function(t){var r=e();return x(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return tL(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,a=e(),o=0,l=a.length-1,c=a[o],u=a[l],s=10;for(u<c&&(i=c,c=u,u=i,i=o,o=l,l=i);s-- >0;){if((i=w(c,u,r))===n)return a[o]=c,a[l]=u,e(a);if(i>0)c=Math.floor(c/i)*i,u=Math.ceil(u/i)*i;else if(i<0)c=Math.ceil(c*i)/i,u=Math.floor(u*i)/i;else break;n=i}return t},t}function tU(t,e){t=t.slice();var r,n=0,i=t.length-1,a=t[n],o=t[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),t[n]=e.floor(a),t[i]=e.ceil(o),t}function tR(t){return Math.log(t)}function tB(t){return Math.exp(t)}function tF(t){return-Math.log(-t)}function tG(t){return-Math.exp(-t)}function tK(t){return isFinite(t)?+("1e"+t):t<0?0:t}function tH(t){return(e,r)=>-t(-e,r)}function tW(t){let e,r,n=t(tR,tB),i=n.domain,a=10;function o(){var o,l;return e=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),t=>Math.log(t)/o),r=10===(l=a)?tK:l===Math.E?Math.exp:t=>Math.pow(l,t),i()[0]<0?(e=tH(e),r=tH(r),t(tF,tG)):t(tR,tB),n}return n.base=function(t){return arguments.length?(a=+t,o()):a},n.domain=function(t){return arguments.length?(i(t),o()):i()},n.ticks=t=>{let n,o,l=i(),c=l[0],u=l[l.length-1],s=u<c;s&&([c,u]=[u,c]);let f=e(c),h=e(u),d=null==t?10:+t,p=[];if(!(a%1)&&h-f<d){if(f=Math.floor(f),h=Math.ceil(h),c>0){for(;f<=h;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<c)){if(o>u)break;p.push(o)}}else for(;f<=h;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<c)){if(o>u)break;p.push(o)}2*p.length<d&&(p=x(c,u,d))}else p=x(f,h,Math.min(h-f,d)).map(r);return s?p.reverse():p},n.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=tE(i)).precision||(i.trim=!0),i=ne(i)),t===1/0)return i;let o=Math.max(1,a*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*a<a-.5&&(n*=a),n<=o?i(t):""}},n.nice=()=>i(tU(i(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function tZ(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function tq(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function tV(t){var e=1,r=t(tZ(1),tq(e));return r.constant=function(r){return arguments.length?t(tZ(e=+r),tq(e)):e},t$(r)}function tY(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function tJ(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function tX(t){return t<0?-t*t:t*t}function tQ(t){var e=t(tx,tx),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(tx,tx):.5===r?t(tJ,tX):t(tY(r),tY(1/r)):r},t$(e)}function t0(){var t=tQ(tM());return t.copy=function(){return tP(t,t0()).exponent(t.exponent())},l.apply(t,arguments),t}function t1(){return t0.apply(null,arguments).exponent(.5)}function t2(t){return Math.sign(t)*t*t}function t4(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function t3(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}ne=(nt=function(t){var e,r,n,i=void 0===t.grouping||void 0===t.thousands?tN:(e=tI.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,a=[],o=0,l=e[0],c=0;i>0&&l>0&&(c+l+1>n&&(l=Math.max(1,n-c)),a.push(t.substring(i-=l,i+l)),!((c+=l+1)>n));)l=e[o=(o+1)%e.length];return a.reverse().join(r)}),a=void 0===t.currency?"":t.currency[0]+"",o=void 0===t.currency?"":t.currency[1]+"",l=void 0===t.decimal?".":t.decimal+"",c=void 0===t.numerals?tN:(n=tI.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),u=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function h(t){var e=(t=tE(t)).fill,r=t.align,n=t.sign,h=t.symbol,d=t.zero,p=t.width,y=t.comma,v=t.precision,g=t.trim,m=t.type;"n"===m?(y=!0,m="g"):tD[m]||(void 0===v&&(v=12),g=!0,m="g"),(d||"0"===e&&"="===r)&&(d=!0,e="0",r="=");var b="$"===h?a:"#"===h&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",x="$"===h?o:/[%p]/.test(m)?u:"",w=tD[m],O=/[defgprs%]/.test(m);function j(t){var a,o,u,h=b,j=x;if("c"===m)j=w(t)+j,t="";else{var P=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:w(Math.abs(t),v),g&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),P&&0==+t&&"+"!==n&&(P=!1),h=(P?"("===n?n:s:"-"===n||"("===n?"":n)+h,j=("s"===m?tz[8+r7/3]:"")+j+(P&&"("===n?")":""),O){for(a=-1,o=t.length;++a<o;)if(48>(u=t.charCodeAt(a))||u>57){j=(46===u?l+t.slice(a+1):t.slice(a))+j,t=t.slice(0,a);break}}}y&&!d&&(t=i(t,1/0));var M=h.length+t.length+j.length,S=M<p?Array(p-M+1).join(e):"";switch(y&&d&&(t=i(S+t,S.length?p-j.length:1/0),S=""),r){case"<":t=h+t+j+S;break;case"=":t=h+S+t+j;break;case"^":t=S.slice(0,M=S.length>>1)+h+t+j+S.slice(M);break;default:t=S+h+t+j}return c(t)}return v=void 0===v?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:h,formatPrefix:function(t,e){var r=h(((t=tE(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(tT(e)/3))),i=Math.pow(10,-n),a=tz[8+n/3];return function(t){return r(i*t)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,nr=nt.formatPrefix;function t6(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function t5(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let t8=new Date,t9=new Date;function t7(t,e,r,n){function i(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{let e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),e(r,a),t(r);while(o<r&&r<n);return l},i.filter=r=>t7(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(i.count=(e,n)=>(t8.setTime(+e),t9.setTime(+n),t(t8),t(t9),Math.floor(r(t8,t9))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null),i}let et=t7(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);et.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?t7(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):et:null,et.range;let ee=t7(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());ee.range;let er=t7(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());er.range;let en=t7(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());en.range;let ei=t7(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());ei.range;let ea=t7(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());ea.range;let eo=t7(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);eo.range;let el=t7(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);el.range;let ec=t7(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function eu(t){return t7(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}ec.range;let es=eu(0),ef=eu(1),eh=eu(2),ed=eu(3),ep=eu(4),ey=eu(5),ev=eu(6);function eg(t){return t7(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}es.range,ef.range,eh.range,ed.range,ep.range,ey.range,ev.range;let em=eg(0),eb=eg(1),ex=eg(2),ew=eg(3),eO=eg(4),ej=eg(5),eP=eg(6);em.range,eb.range,ex.range,ew.range,eO.range,ej.range,eP.range;let eM=t7(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());eM.range;let eS=t7(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eS.range;let eA=t7(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());eA.every=t=>isFinite(t=Math.floor(t))&&t>0?t7(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,eA.range;let eE=t7(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function e_(t,e,r,n,i,a){let o=[[ee,1,1e3],[ee,5,5e3],[ee,15,15e3],[ee,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function l(e,r,n){let i=Math.abs(r-e)/n,a=M(([,,t])=>t).right(o,i);if(a===o.length)return t.every(O(e/31536e6,r/31536e6,n));if(0===a)return et.every(Math.max(O(e,r,n),1));let[l,c]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(c)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let i=r&&"function"==typeof r.range?r:l(t,e,r),a=i?i.range(t,+e+1):[];return n?a.reverse():a},l]}eE.every=t=>isFinite(t=Math.floor(t))&&t>0?t7(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,eE.range;let[ek,eT]=e_(eE,eS,em,ec,ea,en),[eC,eD]=e_(eA,eM,es,eo,ei,er);function eN(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eI(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function ez(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var eL={"-":"",_:" ",0:"0"},e$=/^\s*\d+/,eU=/^%/,eR=/[\\^$*+?|[\]().{}]/g;function eB(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",a=i.length;return n+(a<r?Array(r-a+1).join(e)+i:i)}function eF(t){return t.replace(eR,"\\$&")}function eG(t){return RegExp("^(?:"+t.map(eF).join("|")+")","i")}function eK(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eH(t,e,r){var n=e$.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eW(t,e,r){var n=e$.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function eZ(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function eq(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function eV(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function eY(t,e,r){var n=e$.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function eJ(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function eX(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function eQ(t,e,r){var n=e$.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function e0(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function e1(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function e2(t,e,r){var n=e$.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function e4(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function e3(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function e6(t,e,r){var n=e$.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function e5(t,e,r){var n=e$.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function e8(t,e,r){var n=e$.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function e9(t,e,r){var n=eU.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function e7(t,e,r){var n=e$.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function rt(t,e,r){var n=e$.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function re(t,e){return eB(t.getDate(),e,2)}function rr(t,e){return eB(t.getHours(),e,2)}function rn(t,e){return eB(t.getHours()%12||12,e,2)}function ri(t,e){return eB(1+eo.count(eA(t),t),e,3)}function ra(t,e){return eB(t.getMilliseconds(),e,3)}function ro(t,e){return ra(t,e)+"000"}function rl(t,e){return eB(t.getMonth()+1,e,2)}function rc(t,e){return eB(t.getMinutes(),e,2)}function ru(t,e){return eB(t.getSeconds(),e,2)}function rs(t){var e=t.getDay();return 0===e?7:e}function rf(t,e){return eB(es.count(eA(t)-1,t),e,2)}function rh(t){var e=t.getDay();return e>=4||0===e?ep(t):ep.ceil(t)}function rd(t,e){return t=rh(t),eB(ep.count(eA(t),t)+(4===eA(t).getDay()),e,2)}function rp(t){return t.getDay()}function ry(t,e){return eB(ef.count(eA(t)-1,t),e,2)}function rv(t,e){return eB(t.getFullYear()%100,e,2)}function rg(t,e){return eB((t=rh(t)).getFullYear()%100,e,2)}function rm(t,e){return eB(t.getFullYear()%1e4,e,4)}function rb(t,e){var r=t.getDay();return eB((t=r>=4||0===r?ep(t):ep.ceil(t)).getFullYear()%1e4,e,4)}function rx(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+eB(e/60|0,"0",2)+eB(e%60,"0",2)}function rw(t,e){return eB(t.getUTCDate(),e,2)}function rO(t,e){return eB(t.getUTCHours(),e,2)}function rj(t,e){return eB(t.getUTCHours()%12||12,e,2)}function rP(t,e){return eB(1+el.count(eE(t),t),e,3)}function rM(t,e){return eB(t.getUTCMilliseconds(),e,3)}function rS(t,e){return rM(t,e)+"000"}function rA(t,e){return eB(t.getUTCMonth()+1,e,2)}function rE(t,e){return eB(t.getUTCMinutes(),e,2)}function r_(t,e){return eB(t.getUTCSeconds(),e,2)}function rk(t){var e=t.getUTCDay();return 0===e?7:e}function rT(t,e){return eB(em.count(eE(t)-1,t),e,2)}function rC(t){var e=t.getUTCDay();return e>=4||0===e?eO(t):eO.ceil(t)}function rD(t,e){return t=rC(t),eB(eO.count(eE(t),t)+(4===eE(t).getUTCDay()),e,2)}function rN(t){return t.getUTCDay()}function rI(t,e){return eB(eb.count(eE(t)-1,t),e,2)}function rz(t,e){return eB(t.getUTCFullYear()%100,e,2)}function rL(t,e){return eB((t=rC(t)).getUTCFullYear()%100,e,2)}function r$(t,e){return eB(t.getUTCFullYear()%1e4,e,4)}function rU(t,e){var r=t.getUTCDay();return eB((t=r>=4||0===r?eO(t):eO.ceil(t)).getUTCFullYear()%1e4,e,4)}function rR(){return"+0000"}function rB(){return"%"}function rF(t){return+t}function rG(t){return Math.floor(t/1e3)}function rK(t){return new Date(t)}function rH(t){return t instanceof Date?+t:+new Date(+t)}function rW(t,e,r,n,i,a,o,l,c,u){var s=tS(),f=s.invert,h=s.domain,d=u(".%L"),p=u(":%S"),y=u("%I:%M"),v=u("%I %p"),g=u("%a %d"),m=u("%b %d"),b=u("%B"),x=u("%Y");function w(t){return(c(t)<t?d:l(t)<t?p:o(t)<t?y:a(t)<t?v:n(t)<t?i(t)<t?g:m:r(t)<t?b:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?h(Array.from(t,rH)):h().map(rK)},s.ticks=function(e){var r=h();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:u(e)},s.nice=function(t){var r=h();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?h(tU(r,t)):s},s.copy=function(){return tP(s,rW(t,e,r,n,i,a,o,l,c,u))},s}function rZ(){return l.apply(rW(eC,eD,eA,eM,es,eo,ei,er,ee,ni).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rq(){return l.apply(rW(ek,eT,eE,eS,em,el,ea,en,ee,na).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function rV(){var t,e,r,n,i,a=0,o=1,l=tx,c=!1;function u(e){return null==e||isNaN(e*=1)?i:l(0===r?.5:(e=(n(e)-t)*r,c?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,l=t(r,n),u):[l(0),l(1)]}}return u.domain=function(i){return arguments.length?([a,o]=i,t=n(a*=1),e=n(o*=1),r=t===e?0:1/(e-t),u):[a,o]},u.clamp=function(t){return arguments.length?(c=!!t,u):c},u.interpolator=function(t){return arguments.length?(l=t,u):l},u.range=s(tv),u.rangeRound=s(tg),u.unknown=function(t){return arguments.length?(i=t,u):i},function(i){return n=i,t=i(a),e=i(o),r=t===e?0:1/(e-t),u}}function rY(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function rJ(){var t=tQ(rV());return t.copy=function(){return rY(t,rJ()).exponent(t.exponent())},c.apply(t,arguments)}function rX(){return rJ.apply(null,arguments).exponent(.5)}function rQ(){var t,e,r,n,i,a,o,l=0,c=.5,u=1,s=1,f=tx,h=!1;function d(t){return isNaN(t*=1)?o:(t=.5+((t=+a(t))-e)*(s*t<s*e?n:i),f(h?Math.max(0,Math.min(1,t)):t))}function p(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=function(t,e){void 0===e&&(e=t,t=tv);for(var r=0,n=e.length-1,i=e[0],a=Array(n<0?0:n);r<n;)a[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return a[e](t-e)}}(t,[r,n,i]),d):[f(0),f(.5),f(1)]}}return d.domain=function(o){return arguments.length?([l,c,u]=o,t=a(l*=1),e=a(c*=1),r=a(u*=1),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,d):[l,c,u]},d.clamp=function(t){return arguments.length?(h=!!t,d):h},d.interpolator=function(t){return arguments.length?(f=t,d):f},d.range=p(tv),d.rangeRound=p(tg),d.unknown=function(t){return arguments.length?(o=t,d):o},function(o){return a=o,t=o(l),e=o(c),r=o(u),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,d}}function r0(){var t=tQ(rQ());return t.copy=function(){return rY(t,r0()).exponent(t.exponent())},c.apply(t,arguments)}function r1(){return r0.apply(null,arguments).exponent(.5)}ni=(nn=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,a=t.days,o=t.shortDays,l=t.months,c=t.shortMonths,u=eG(i),s=eK(i),f=eG(a),h=eK(a),d=eG(o),p=eK(o),y=eG(l),v=eK(l),g=eG(c),m=eK(c),b={a:function(t){return o[t.getDay()]},A:function(t){return a[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return l[t.getMonth()]},c:null,d:re,e:re,f:ro,g:rg,G:rb,H:rr,I:rn,j:ri,L:ra,m:rl,M:rc,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:rF,s:rG,S:ru,u:rs,U:rf,V:rd,w:rp,W:ry,x:null,X:null,y:rv,Y:rm,Z:rx,"%":rB},x={a:function(t){return o[t.getUTCDay()]},A:function(t){return a[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return l[t.getUTCMonth()]},c:null,d:rw,e:rw,f:rS,g:rL,G:rU,H:rO,I:rj,j:rP,L:rM,m:rA,M:rE,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:rF,s:rG,S:r_,u:rk,U:rT,V:rD,w:rN,W:rI,x:null,X:null,y:rz,Y:r$,Z:rR,"%":rB},w={a:function(t,e,r){var n=d.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=g.exec(e.slice(r));return n?(t.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return P(t,e,r,n)},d:e1,e:e1,f:e8,g:eJ,G:eY,H:e4,I:e4,j:e2,L:e5,m:e0,M:e3,p:function(t,e,r){var n=u.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:eQ,Q:e7,s:rt,S:e6,u:eW,U:eZ,V:eq,w:eH,W:eV,x:function(t,e,n){return P(t,r,e,n)},X:function(t,e,r){return P(t,n,e,r)},y:eJ,Y:eY,Z:eX,"%":e9};function O(t,e){return function(r){var n,i,a,o=[],l=-1,c=0,u=t.length;for(r instanceof Date||(r=new Date(+r));++l<u;)37===t.charCodeAt(l)&&(o.push(t.slice(c,l)),null!=(i=eL[n=t.charAt(++l)])?n=t.charAt(++l):i="e"===n?" ":"0",(a=e[n])&&(n=a(r,i)),o.push(n),c=l+1);return o.push(t.slice(c,l)),o.join("")}}function j(t,e){return function(r){var n,i,a=ez(1900,void 0,1);if(P(a,t,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!e||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=eI(ez(a.y,0,1))).getUTCDay())>4||0===i?eb.ceil(n):eb(n),n=el.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=eN(ez(a.y,0,1))).getDay())>4||0===i?ef.ceil(n):ef(n),n=eo.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?eI(ez(a.y,0,1)).getUTCDay():eN(ez(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,eI(a)):eN(a)}}function P(t,e,r,n){for(var i,a,o=0,l=e.length,c=r.length;o<l;){if(n>=c)return -1;if(37===(i=e.charCodeAt(o++))){if(!(a=w[(i=e.charAt(o++))in eL?e.charAt(o++):i])||(n=a(t,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(e,b),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",b);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,nn.parse,na=nn.utcFormat,nn.utcParse;var r2=r(51426),r4=r(64279),r3=r(57282),r6=r(22989),r5=r(12128);function r8(t){if(Array.isArray(t)&&2===t.length){var[e,r]=t;if((0,r5.H)(e)&&(0,r5.H)(r))return!0}return!1}function r9(t,e,r){return r?t:[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}var r7,nt,ne,nr,nn,ni,na,no,nl,nc=!0,nu="[DecimalError] ",ns=nu+"Invalid argument: ",nf=nu+"Exponent out of range: ",nh=Math.floor,nd=Math.pow,np=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ny=nh(1286742750677284.5),nv={};function ng(t,e){var r,n,i,a,o,l,c,u,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),nc?nA(e,f):e;if(c=t.d,u=e.d,o=t.e,i=e.e,c=c.slice(),a=o-i){for(a<0?(n=c,a=-a,l=u.length):(n=u,i=o,l=c.length),a>(l=(o=Math.ceil(f/7))>l?o+1:l+1)&&(a=l,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((l=c.length)-(a=u.length)<0&&(a=l,n=u,u=c,c=n),r=0;a;)r=(c[--a]=c[a]+u[a]+r)/1e7|0,c[a]%=1e7;for(r&&(c.unshift(r),++i),l=c.length;0==c[--l];)c.pop();return e.d=c,e.e=i,nc?nA(e,f):e}function nm(t,e,r){if(t!==~~t||t<e||t>r)throw Error(ns+t)}function nb(t){var e,r,n,i=t.length-1,a="",o=t[0];if(i>0){for(a+=o,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(a+=nP(r)),a+=n;(r=7-(n=(o=t[e])+"").length)&&(a+=nP(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}nv.absoluteValue=nv.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},nv.comparedTo=nv.cmp=function(t){var e,r,n,i;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(i=t.d.length)?n:i;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},nv.decimalPlaces=nv.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},nv.dividedBy=nv.div=function(t){return nx(this,new this.constructor(t))},nv.dividedToIntegerBy=nv.idiv=function(t){var e=this.constructor;return nA(nx(this,new e(t),0,1),e.precision)},nv.equals=nv.eq=function(t){return!this.cmp(t)},nv.exponent=function(){return nO(this)},nv.greaterThan=nv.gt=function(t){return this.cmp(t)>0},nv.greaterThanOrEqualTo=nv.gte=function(t){return this.cmp(t)>=0},nv.isInteger=nv.isint=function(){return this.e>this.d.length-2},nv.isNegative=nv.isneg=function(){return this.s<0},nv.isPositive=nv.ispos=function(){return this.s>0},nv.isZero=function(){return 0===this.s},nv.lessThan=nv.lt=function(t){return 0>this.cmp(t)},nv.lessThanOrEqualTo=nv.lte=function(t){return 1>this.cmp(t)},nv.logarithm=nv.log=function(t){var e,r=this.constructor,n=r.precision,i=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(nl))throw Error(nu+"NaN");if(this.s<1)throw Error(nu+(this.s?"NaN":"-Infinity"));return this.eq(nl)?new r(0):(nc=!1,e=nx(nM(this,i),nM(t,i),i),nc=!0,nA(e,n))},nv.minus=nv.sub=function(t){return t=new this.constructor(t),this.s==t.s?nE(this,t):ng(this,(t.s=-t.s,t))},nv.modulo=nv.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(nu+"NaN");return this.s?(nc=!1,e=nx(this,t,0,1).times(t),nc=!0,this.minus(e)):nA(new r(this),n)},nv.naturalExponential=nv.exp=function(){return nw(this)},nv.naturalLogarithm=nv.ln=function(){return nM(this)},nv.negated=nv.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},nv.plus=nv.add=function(t){return t=new this.constructor(t),this.s==t.s?ng(this,t):nE(this,(t.s=-t.s,t))},nv.precision=nv.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(ns+t);if(e=nO(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},nv.squareRoot=nv.sqrt=function(){var t,e,r,n,i,a,o,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(nu+"NaN")}for(t=nO(this),nc=!1,0==(i=Math.sqrt(+this))||i==1/0?(((e=nb(this.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=nh((t+1)/2)-(t<0||t%2),n=new l(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new l(i.toString()),i=o=(r=l.precision)+3;;)if(n=(a=n).plus(nx(this,a,o+2)).times(.5),nb(a.d).slice(0,o)===(e=nb(n.d)).slice(0,o)){if(e=e.slice(o-3,o+1),i==o&&"4999"==e){if(nA(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=e)break;o+=4}return nc=!0,nA(n,r)},nv.times=nv.mul=function(t){var e,r,n,i,a,o,l,c,u,s=this.constructor,f=this.d,h=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(c=f.length)<(u=h.length)&&(a=f,f=h,h=a,o=c,c=u,u=o),a=[],n=o=c+u;n--;)a.push(0);for(n=u;--n>=0;){for(e=0,i=c+n;i>n;)l=a[i]+h[n]*f[i-n-1]+e,a[i--]=l%1e7|0,e=l/1e7|0;a[i]=(a[i]+e)%1e7|0}for(;!a[--o];)a.pop();return e?++r:a.shift(),t.d=a,t.e=r,nc?nA(t,s.precision):t},nv.toDecimalPlaces=nv.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(nm(t,0,1e9),void 0===e?e=n.rounding:nm(e,0,8),nA(r,t+nO(r)+1,e))},nv.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=n_(n,!0):(nm(t,0,1e9),void 0===e?e=i.rounding:nm(e,0,8),r=n_(n=nA(new i(n),t+1,e),!0,t+1)),r},nv.toFixed=function(t,e){var r,n,i=this.constructor;return void 0===t?n_(this):(nm(t,0,1e9),void 0===e?e=i.rounding:nm(e,0,8),r=n_((n=nA(new i(this),t+nO(this)+1,e)).abs(),!1,t+nO(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},nv.toInteger=nv.toint=function(){var t=this.constructor;return nA(new t(this),nO(this)+1,t.rounding)},nv.toNumber=function(){return+this},nv.toPower=nv.pow=function(t){var e,r,n,i,a,o,l=this,c=l.constructor,u=+(t=new c(t));if(!t.s)return new c(nl);if(!(l=new c(l)).s){if(t.s<1)throw Error(nu+"Infinity");return l}if(l.eq(nl))return l;if(n=c.precision,t.eq(nl))return nA(l,n);if(o=(e=t.e)>=(r=t.d.length-1),a=l.s,o){if((r=u<0?-u:u)<=0x1fffffffffffff){for(i=new c(nl),e=Math.ceil(n/7+4),nc=!1;r%2&&nk((i=i.times(l)).d,e),0!==(r=nh(r/2));)nk((l=l.times(l)).d,e);return nc=!0,t.s<0?new c(nl).div(i):nA(i,n)}}else if(a<0)throw Error(nu+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,l.s=1,nc=!1,i=t.times(nM(l,n+12)),nc=!0,(i=nw(i)).s=a,i},nv.toPrecision=function(t,e){var r,n,i=this,a=i.constructor;return void 0===t?(r=nO(i),n=n_(i,r<=a.toExpNeg||r>=a.toExpPos)):(nm(t,1,1e9),void 0===e?e=a.rounding:nm(e,0,8),r=nO(i=nA(new a(i),t,e)),n=n_(i,t<=r||r<=a.toExpNeg,t)),n},nv.toSignificantDigits=nv.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(nm(t,1,1e9),void 0===e?e=r.rounding:nm(e,0,8)),nA(new r(this),t,e)},nv.toString=nv.valueOf=nv.val=nv.toJSON=nv[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=nO(this),e=this.constructor;return n_(this,t<=e.toExpNeg||t>=e.toExpPos)};var nx=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(t[i]!=e[i]){a=t[i]>e[i]?1:-1;break}return a}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,a,o){var l,c,u,s,f,h,d,p,y,v,g,m,b,x,w,O,j,P,M=n.constructor,S=n.s==i.s?1:-1,A=n.d,E=i.d;if(!n.s)return new M(n);if(!i.s)throw Error(nu+"Division by zero");for(u=0,c=n.e-i.e,j=E.length,w=A.length,p=(d=new M(S)).d=[];E[u]==(A[u]||0);)++u;if(E[u]>(A[u]||0)&&--c,(m=null==a?a=M.precision:o?a+(nO(n)-nO(i))+1:a)<0)return new M(0);if(m=m/7+2|0,u=0,1==j)for(s=0,E=E[0],m++;(u<w||s)&&m--;u++)b=1e7*s+(A[u]||0),p[u]=b/E|0,s=b%E|0;else{for((s=1e7/(E[0]+1)|0)>1&&(E=t(E,s),A=t(A,s),j=E.length,w=A.length),x=j,v=(y=A.slice(0,j)).length;v<j;)y[v++]=0;(P=E.slice()).unshift(0),O=E[0],E[1]>=1e7/2&&++O;do s=0,(l=e(E,y,j,v))<0?(g=y[0],j!=v&&(g=1e7*g+(y[1]||0)),(s=g/O|0)>1?(s>=1e7&&(s=1e7-1),h=(f=t(E,s)).length,v=y.length,1==(l=e(f,y,h,v))&&(s--,r(f,j<h?P:E,h))):(0==s&&(l=s=1),f=E.slice()),(h=f.length)<v&&f.unshift(0),r(y,f,v),-1==l&&(v=y.length,(l=e(E,y,j,v))<1&&(s++,r(y,j<v?P:E,v))),v=y.length):0===l&&(s++,y=[0]),p[u++]=s,l&&y[0]?y[v++]=A[x]||0:(y=[A[x]],v=1);while((x++<w||void 0!==y[0])&&m--)}return p[0]||p.shift(),d.e=c,nA(d,o?a+nO(d)+1:a)}}();function nw(t,e){var r,n,i,a,o,l=0,c=0,u=t.constructor,s=u.precision;if(nO(t)>16)throw Error(nf+nO(t));if(!t.s)return new u(nl);for(null==e?(nc=!1,o=s):o=e,a=new u(.03125);t.abs().gte(.1);)t=t.times(a),c+=5;for(o+=Math.log(nd(2,c))/Math.LN10*2+5|0,r=n=i=new u(nl),u.precision=o;;){if(n=nA(n.times(t),o),r=r.times(++l),nb((a=i.plus(nx(n,r,o))).d).slice(0,o)===nb(i.d).slice(0,o)){for(;c--;)i=nA(i.times(i),o);return u.precision=s,null==e?(nc=!0,nA(i,s)):i}i=a}}function nO(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function nj(t,e,r){if(e>t.LN10.sd())throw nc=!0,r&&(t.precision=r),Error(nu+"LN10 precision limit exceeded");return nA(new t(t.LN10),e)}function nP(t){for(var e="";t--;)e+="0";return e}function nM(t,e){var r,n,i,a,o,l,c,u,s,f=1,h=t,d=h.d,p=h.constructor,y=p.precision;if(h.s<1)throw Error(nu+(h.s?"NaN":"-Infinity"));if(h.eq(nl))return new p(0);if(null==e?(nc=!1,u=y):u=e,h.eq(10))return null==e&&(nc=!0),nj(p,u);if(p.precision=u+=10,n=(r=nb(d)).charAt(0),!(15e14>Math.abs(a=nO(h))))return c=nj(p,u+2,y).times(a+""),h=nM(new p(n+"."+r.slice(1)),u-10).plus(c),p.precision=y,null==e?(nc=!0,nA(h,y)):h;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=nb((h=h.times(t)).d)).charAt(0),f++;for(a=nO(h),n>1?(h=new p("0."+r),a++):h=new p(n+"."+r.slice(1)),l=o=h=nx(h.minus(nl),h.plus(nl),u),s=nA(h.times(h),u),i=3;;){if(o=nA(o.times(s),u),nb((c=l.plus(nx(o,new p(i),u))).d).slice(0,u)===nb(l.d).slice(0,u))return l=l.times(2),0!==a&&(l=l.plus(nj(p,u+2,y).times(a+""))),l=nx(l,new p(f),u),p.precision=y,null==e?(nc=!0,nA(l,y)):l;l=c,i+=2}}function nS(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,t.e=nh((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),nc&&(t.e>ny||t.e<-ny))throw Error(nf+r)}else t.s=0,t.e=0,t.d=[0];return t}function nA(t,e,r){var n,i,a,o,l,c,u,s,f=t.d;for(o=1,a=f[0];a>=10;a/=10)o++;if((n=e-o)<0)n+=7,i=e,u=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(a=f.length))return t;for(o=1,u=a=f[s];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(l=u/(a=nd(10,o-i-1))%10|0,c=e<0||void 0!==f[s+1]||u%a,c=r<4?(l||c)&&(0==r||r==(t.s<0?3:2)):l>5||5==l&&(4==r||c||6==r&&(n>0?i>0?u/nd(10,o-i):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return c?(a=nO(t),f.length=1,e=e-a-1,f[0]=nd(10,(7-e%7)%7),t.e=nh(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,a=1,s--):(f.length=s+1,a=nd(10,7-n),f[s]=i>0?(u/nd(10,o-i)%nd(10,i)|0)*a:0),c)for(;;)if(0==s){1e7==(f[0]+=a)&&(f[0]=1,++t.e);break}else{if(f[s]+=a,1e7!=f[s])break;f[s--]=0,a=1}for(n=f.length;0===f[--n];)f.pop();if(nc&&(t.e>ny||t.e<-ny))throw Error(nf+nO(t));return t}function nE(t,e){var r,n,i,a,o,l,c,u,s,f,h=t.constructor,d=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),nc?nA(e,d):e;if(c=t.d,f=e.d,n=e.e,u=t.e,c=c.slice(),o=u-n){for((s=o<0)?(r=c,o=-o,l=f.length):(r=f,n=u,l=c.length),o>(i=Math.max(Math.ceil(d/7),l)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((s=(i=c.length)<(l=f.length))&&(l=i),i=0;i<l;i++)if(c[i]!=f[i]){s=c[i]<f[i];break}o=0}for(s&&(r=c,c=f,f=r,e.s=-e.s),l=c.length,i=f.length-l;i>0;--i)c[l++]=0;for(i=f.length;i>o;){if(c[--i]<f[i]){for(a=i;a&&0===c[--a];)c[a]=1e7-1;--c[a],c[i]+=1e7}c[i]-=f[i]}for(;0===c[--l];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(e.d=c,e.e=n,nc?nA(e,d):e):new h(0)}function n_(t,e,r){var n,i=nO(t),a=nb(t.d),o=a.length;return e?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+nP(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+nP(-i-1)+a,r&&(n=r-o)>0&&(a+=nP(n))):i>=o?(a+=nP(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+nP(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=nP(n))),t.s<0?"-"+a:a}function nk(t,e){if(t.length>e)return t.length=e,!0}function nT(t){if(!t||"object"!=typeof t)throw Error(nu+"Object expected");var e,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]]))if(nh(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(ns+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(ns+r+": "+n);return this}var no=function t(e){var r,n,i;function a(t){if(!(this instanceof a))return new a(t);if(this.constructor=a,t instanceof a){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(ns+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return nS(this,t.toString())}if("string"!=typeof t)throw Error(ns+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,np.test(t))nS(this,t);else throw Error(ns+t)}if(a.prototype=nv,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=t,a.config=a.set=nT,void 0===e&&(e={}),e)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return a.config(e),a}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});nl=new no(1);let nC=no;var nD=t=>t,nN={},nI=t=>t===nN,nz=t=>function e(){return 0==arguments.length||1==arguments.length&&nI(arguments.length<=0?void 0:arguments[0])?e:t(...arguments)},nL=(t,e)=>1===t?e:nz(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(t=>t!==nN).length;return a>=t?e(...n):nL(t-a,nz(function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];return e(...n.map(t=>nI(t)?r.shift():t),...r)}))}),n$=t=>nL(t.length,t),nU=(t,e)=>{for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},nR=n$((t,e)=>Array.isArray(e)?e.map(t):Object.keys(e).map(t=>e[t]).map(t)),nB=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return nD;var n=e.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((t,e)=>e(t),i(...arguments))}},nF=t=>Array.isArray(t)?t.reverse():t.split("").reverse().join(""),nG=t=>{var e=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return e&&i.every((t,r)=>{var n;return t===(null==(n=e)?void 0:n[r])})?r:(e=i,r=t(...i))}};function nK(t){var e;return 0===t?1:Math.floor(new nC(t).abs().log(10).toNumber())+1}function nH(t,e,r){for(var n=new nC(t),i=0,a=[];n.lt(e)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}n$((t,e,r)=>{var n=+t;return n+r*(e-n)}),n$((t,e,r)=>{var n=e-t;return(r-t)/(n=n||1/0)}),n$((t,e,r)=>{var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});var nW=t=>{var[e,r]=t,[n,i]=[e,r];return e>r&&([n,i]=[r,e]),[n,i]},nZ=(t,e,r)=>{if(t.lte(0))return new nC(0);var n=nK(t.toNumber()),i=new nC(10).pow(n),a=t.div(i),o=1!==n?.05:.1,l=new nC(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new nC(e?l.toNumber():Math.ceil(l.toNumber()))},nq=(t,e,r)=>{var n=new nC(1),i=new nC(t);if(!i.isint()&&r){var a=Math.abs(t);a<1?(n=new nC(10).pow(nK(t)-1),i=new nC(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new nC(Math.floor(t)))}else 0===t?i=new nC(Math.floor((e-1)/2)):r||(i=new nC(Math.floor(t)));var o=Math.floor((e-1)/2);return nB(nR(t=>i.add(new nC(t-o).mul(n)).toNumber()),nU)(0,e)},nV=function(t,e,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new nC(0),tickMin:new nC(0),tickMax:new nC(0)};var o=nZ(new nC(e).sub(t).div(r-1),n,a),l=Math.ceil((i=t<=0&&e>=0?new nC(0):(i=new nC(t).add(e).div(2)).sub(new nC(i).mod(o))).sub(t).div(o).toNumber()),c=Math.ceil(new nC(e).sub(i).div(o).toNumber()),u=l+c+1;return u>r?nV(t,e,r,n,a+1):(u<r&&(c=e>0?c+(r-u):c,l=e>0?l:l+(r-u)),{step:o,tickMin:i.sub(new nC(l).mul(o)),tickMax:i.add(new nC(c).mul(o))})},nY=nG(function(t){var[e,r]=t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=nW([e,r]);if(o===-1/0||l===1/0){var c=l===1/0?[o,...nU(0,n-1).map(()=>1/0)]:[...nU(0,n-1).map(()=>-1/0),l];return e>r?nF(c):c}if(o===l)return nq(o,n,i);var{step:u,tickMin:s,tickMax:f}=nV(o,l,a,i,0),h=nH(s,f.add(new nC(.1).mul(u)),u);return e>r?nF(h):h}),nJ=nG(function(t,e){var[r,n]=t,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=nW([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(e,2),c=nZ(new nC(o).sub(a).div(l-1),i,0),u=[...nH(new nC(a),new nC(o).sub(new nC(.99).mul(c)),c),o];return r>n?nF(u):u}),nX=r(86445),nQ=r(23814),n0=r(69107),n1=r(94728),n2=r(97350),n4=r(8920),n3=r(36166),n6=r(60559),n5=r(53416),n8=r(75601);function n9(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function n7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n9(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n9(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var it=[0,"auto"],ie={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},ir=(t,e)=>{var r=t.cartesianAxis.xAxis[e];return null==r?ie:r},ii={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:it,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:n8.tQ},ia=(t,e)=>{var r=t.cartesianAxis.yAxis[e];return null==r?ii:r},io={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},il=(t,e)=>{var r=t.cartesianAxis.zAxis[e];return null==r?io:r},ic=(t,e,r)=>{switch(e){case"xAxis":return ir(t,r);case"yAxis":return ia(t,r);case"zAxis":return il(t,r);case"angleAxis":return(0,n4.Be)(t,r);case"radiusAxis":return(0,n4.Gl)(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},iu=(t,e,r)=>{switch(e){case"xAxis":return ir(t,r);case"yAxis":return ia(t,r);case"angleAxis":return(0,n4.Be)(t,r);case"radiusAxis":return(0,n4.Gl)(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},is=t=>t.graphicalItems.countOfBars>0;function ih(t,e){return r=>{switch(t){case"xAxis":return"xAxisId"in r&&r.xAxisId===e;case"yAxis":return"yAxisId"in r&&r.yAxisId===e;case"zAxis":return"zAxisId"in r&&r.zAxisId===e;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===e;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===e;default:return!1}}}var id=t=>t.graphicalItems.cartesianItems,ip=(0,i.Mz)([n3.N,n6.E],ih),iy=(t,e,r)=>t.filter(r).filter(t=>(null==e?void 0:e.includeHidden)===!0||!t.hide),iv=(0,i.Mz)([id,ic,ip],iy),ig=t=>t.filter(t=>void 0===t.stackId),im=(0,i.Mz)([iv],ig),ib=t=>t.map(t=>t.data).filter(Boolean).flat(1),ix=(0,i.Mz)([iv],ib),iw=(t,e)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=e;return t.length>0?t:r.slice(n,i+1)},iO=(0,i.Mz)([ix,r3.HS],iw),ij=(t,e,r)=>(null==e?void 0:e.dataKey)!=null?t.map(t=>({value:(0,r4.kr)(t,e.dataKey)})):r.length>0?r.map(t=>t.dataKey).flatMap(e=>t.map(t=>({value:(0,r4.kr)(t,e)}))):t.map(t=>({value:t})),iP=(0,i.Mz)([iO,ic,iv],ij);function iM(t,e){switch(t){case"xAxis":return"x"===e.direction;case"yAxis":return"y"===e.direction;default:return!1}}function iS(t){return t.filter(t=>(0,r6.vh)(t)||t instanceof Date).map(Number).filter(t=>!1===(0,r6.M8)(t))}var iA=(t,e,r)=>Object.fromEntries(Object.entries(e.reduce((t,e)=>(null==e.stackId||(null==t[e.stackId]&&(t[e.stackId]=[]),t[e.stackId].push(e)),t),{})).map(e=>{var[n,i]=e,a=i.map(t=>t.dataKey);return[n,{stackedData:(0,r4.yy)(t,a,r),graphicalItems:i}]})),iE=(0,i.Mz)([iO,iv,n2.eC],iA),i_=(t,e,r)=>{var{dataStartIndex:n,dataEndIndex:i}=e;if("zAxis"!==r){var a=(0,r4.Mk)(t,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},ik=(0,i.Mz)([iE,r3.LF,n3.N],i_),iT=(t,e,r,n)=>r.length>0?t.flatMap(t=>r.flatMap(r=>{var i,a,o=null==(i=r.errorBars)?void 0:i.filter(t=>iM(n,t)),l=(0,r4.kr)(t,null!=(a=e.dataKey)?a:r.dataKey);return{value:l,errorDomain:function(t,e,r){return!r||"number"!=typeof e||(0,r6.M8)(e)||!r.length?[]:iS(r.flatMap(r=>{var n,i,a=(0,r4.kr)(t,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,(0,r5.H)(n)&&(0,r5.H)(i))return[e-n,e+i]}))}(t,l,o)}})).filter(Boolean):(null==e?void 0:e.dataKey)!=null?t.map(t=>({value:(0,r4.kr)(t,e.dataKey),errorDomain:[]})):t.map(t=>({value:t,errorDomain:[]})),iC=(0,i.Mz)(iO,ic,im,n3.N,iT);function iD(t){var{value:e}=t;if((0,r6.vh)(e)||e instanceof Date)return e}var iN=t=>{var e=iS(t.flatMap(t=>[t.value,t.errorDomain]).flat(1));if(0!==e.length)return[Math.min(...e),Math.max(...e)]},iI=(t,e,r)=>{var n=t.map(iD).filter(t=>null!=t);return r&&(null==e.dataKey||e.allowDuplicatedCategory&&(0,r6.CG)(n))?o()(0,t.length):e.allowDuplicatedCategory?n:Array.from(new Set(n))},iz=t=>{var e;if(null==t||!("domain"in t))return it;if(null!=t.domain)return t.domain;if(null!=t.ticks){if("number"===t.type){var r=iS(t.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===t.type)return t.ticks.map(String)}return null!=(e=null==t?void 0:t.domain)?e:it},iL=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},i$=t=>t.referenceElements.dots,iU=(t,e,r)=>t.filter(t=>"extendDomain"===t.ifOverflow).filter(t=>"xAxis"===e?t.xAxisId===r:t.yAxisId===r),iR=(0,i.Mz)([i$,n3.N,n6.E],iU),iB=t=>t.referenceElements.areas,iF=(0,i.Mz)([iB,n3.N,n6.E],iU),iG=t=>t.referenceElements.lines,iK=(0,i.Mz)([iG,n3.N,n6.E],iU),iH=(t,e)=>{var r=iS(t.map(t=>"xAxis"===e?t.x:t.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iW=(0,i.Mz)(iR,n3.N,iH),iZ=(t,e)=>{var r=iS(t.flatMap(t=>["xAxis"===e?t.x1:t.y1,"xAxis"===e?t.x2:t.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iq=(0,i.Mz)([iF,n3.N],iZ),iV=(t,e)=>{var r=iS(t.map(t=>"xAxis"===e?t.x:t.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},iY=(0,i.Mz)(iK,n3.N,iV),iJ=(0,i.Mz)(iW,iY,iq,(t,e,r)=>iL(t,r,e)),iX=(0,i.Mz)([ic],iz),iQ=(t,e,r,n,i)=>{var a=function(t,e){if(e&&"function"!=typeof t&&Array.isArray(t)&&2===t.length){var r,n,[i,a]=t;if((0,r5.H)(i))r=i;else if("function"==typeof i)return;if((0,r5.H)(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(r8(o))return o}}(e,t.allowDataOverflow);return null!=a?a:function(t,e,r){if(r||null!=e){if("function"==typeof t&&null!=e)try{var n=t(e,r);if(r8(n))return r9(n,e,r)}catch(t){}if(Array.isArray(t)&&2===t.length){var i,a,[o,l]=t;if("auto"===o)null!=e&&(i=Math.min(...e));else if((0,r6.Et)(o))i=o;else if("function"==typeof o)try{null!=e&&(i=o(null==e?void 0:e[0]))}catch(t){}else if("string"==typeof o&&r4.IH.test(o)){var c=r4.IH.exec(o);if(null==c||null==e)i=void 0;else{var u=+c[1];i=e[0]-u}}else i=null==e?void 0:e[0];if("auto"===l)null!=e&&(a=Math.max(...e));else if((0,r6.Et)(l))a=l;else if("function"==typeof l)try{null!=e&&(a=l(null==e?void 0:e[1]))}catch(t){}else if("string"==typeof l&&r4.qx.test(l)){var s=r4.qx.exec(l);if(null==s||null==e)a=void 0;else{var f=+s[1];a=e[1]+f}}else a=null==e?void 0:e[1];var h=[i,a];if(r8(h))return null==e?h:r9(h,e,r)}}}(e,iL(r,i,iN(n)),t.allowDataOverflow)},i0=(0,i.Mz)([ic,iX,ik,iC,iJ],iQ),i1=[0,1],i2=(t,e,r,n,i,a,l)=>{if(null!=t&&null!=r&&0!==r.length){var{dataKey:c,type:u}=t,s=(0,r4._L)(e,a);return s&&null==c?o()(0,r.length):"category"===u?iI(n,t,s):"expand"===i?i1:l}},i4=(0,i.Mz)([ic,r2.fz,iO,iP,n2.eC,n3.N,i0],i2),i3=(t,e,r,i,a)=>{if(null!=t){var{scale:o,type:l}=t;if("auto"===o)return"radial"===e&&"radiusAxis"===a?"band":"radial"===e&&"angleAxis"===a?"linear":"category"===l&&i&&(i.indexOf("LineChart")>=0||i.indexOf("AreaChart")>=0||i.indexOf("ComposedChart")>=0&&!r)?"point":"category"===l?"band":"linear";if("string"==typeof o){var c="scale".concat((0,r6.Zb)(o));return c in n?c:"point"}}},i6=(0,i.Mz)([ic,r2.fz,is,n2.iO,n3.N],i3);function i5(t,e,r,i){if(null!=r&&null!=i){if("function"==typeof t.scale)return t.scale.copy().domain(r).range(i);var a=function(t){if(null!=t){if(t in n)return n[t]();var e="scale".concat((0,r6.Zb)(t));if(e in n)return n[e]()}}(e);if(null!=a){var o=a.domain(r).range(i);return(0,r4.YB)(o),o}}}var i8=(t,e,r)=>{var n=iz(e);if("auto"===r||"linear"===r){if(null!=e&&e.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&r8(t))return nY(t,e.tickCount,e.allowDecimals);if(null!=e&&e.tickCount&&"number"===e.type&&null!=t)return nJ(t,e.tickCount,e.allowDecimals)}},i9=(0,i.Mz)([i4,iu,i6],i8),i7=(t,e,r,n)=>"angleAxis"!==n&&(null==t?void 0:t.type)==="number"&&r8(e)&&Array.isArray(r)&&r.length>0?[Math.min(e[0],r[0]),Math.max(e[1],r[r.length-1])]:e,at=(0,i.Mz)([ic,i4,i9,n3.N],i7),ae=(0,i.Mz)(iP,ic,(t,e)=>{if(e&&"number"===e.type){var r=1/0,n=Array.from(iS(t.map(t=>t.value))).sort((t,e)=>t-e);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),ar=(0,i.Mz)(ae,r2.fz,n2.gY,n0.GO,(t,e,r,n)=>n,(t,e,r,n,i)=>{if(!(0,r5.H)(t))return 0;var a="vertical"===e?n.height:n.width;if("gap"===i)return t*a/2;if("no-gap"===i){var o=(0,r6.F4)(r,t*a),l=t*a/2;return l-o-(l-o)/a*o}return 0}),an=(0,i.Mz)(ir,(t,e)=>{var r=ir(t,e);return null==r||"string"!=typeof r.padding?0:ar(t,"xAxis",e,r.padding)},(t,e)=>{if(null==t)return{left:0,right:0};var r,n,{padding:i}=t;return"string"==typeof i?{left:e,right:e}:{left:(null!=(r=i.left)?r:0)+e,right:(null!=(n=i.right)?n:0)+e}}),ai=(0,i.Mz)(ia,(t,e)=>{var r=ia(t,e);return null==r||"string"!=typeof r.padding?0:ar(t,"yAxis",e,r.padding)},(t,e)=>{if(null==t)return{top:0,bottom:0};var r,n,{padding:i}=t;return"string"==typeof i?{top:e,bottom:e}:{top:(null!=(r=i.top)?r:0)+e,bottom:(null!=(n=i.bottom)?n:0)+e}}),aa=(0,i.Mz)([n0.GO,an,n1.U,n1.C,(t,e,r)=>r],(t,e,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[t.left+e.left,t.left+t.width-e.right]}),ao=(0,i.Mz)([n0.GO,r2.fz,ai,n1.U,n1.C,(t,e,r)=>r],(t,e,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===e?[t.top+t.height-r.bottom,t.top+r.top]:[t.top+r.top,t.top+t.height-r.bottom]}),al=(t,e,r,n)=>{var i;switch(e){case"xAxis":return aa(t,r,n);case"yAxis":return ao(t,r,n);case"zAxis":return null==(i=il(t,r))?void 0:i.range;case"angleAxis":return(0,n4.Cv)(t);case"radiusAxis":return(0,n4.Dc)(t,r);default:return}},ac=(0,i.Mz)([ic,al],n5.I),au=(0,i.Mz)([ic,i6,at,ac],i5);function as(t,e){return t.id<e.id?-1:+(t.id>e.id)}(0,i.Mz)(iv,n3.N,(t,e)=>t.flatMap(t=>{var e;return null!=(e=t.errorBars)?e:[]}).filter(t=>iM(e,t)));var af=(t,e)=>e,ah=(t,e,r)=>r,ad=(0,i.Mz)(nQ.h,af,ah,(t,e,r)=>t.filter(t=>t.orientation===e).filter(t=>t.mirror===r).sort(as)),ap=(0,i.Mz)(nQ.W,af,ah,(t,e,r)=>t.filter(t=>t.orientation===e).filter(t=>t.mirror===r).sort(as)),ay=(t,e)=>({width:t.width,height:e.height}),av=(t,e)=>({width:"number"==typeof e.width?e.width:n8.tQ,height:t.height}),ag=(0,i.Mz)(n0.GO,ir,ay),am=(t,e,r)=>{switch(e){case"top":return t.top;case"bottom":return r-t.bottom;default:return 0}},ab=(t,e,r)=>{switch(e){case"left":return t.left;case"right":return r-t.right;default:return 0}},ax=(0,i.Mz)(nX.A$,n0.GO,ad,af,ah,(t,e,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=ay(e,r);null==a&&(a=am(e,n,t));var c="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(c)*l.height,a+=(c?-1:1)*l.height}),o}),aw=(0,i.Mz)(nX.Lp,n0.GO,ap,af,ah,(t,e,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=av(e,r);null==a&&(a=ab(e,n,t));var c="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(c)*l.width,a+=(c?-1:1)*l.width}),o}),aO=(t,e)=>{var r=(0,n0.GO)(t),n=ir(t,e);if(null!=n){var i=ax(t,n.orientation,n.mirror)[e];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},aj=(t,e)=>{var r=(0,n0.GO)(t),n=ia(t,e);if(null!=n){var i=aw(t,n.orientation,n.mirror)[e];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},aP=(0,i.Mz)(n0.GO,ia,(t,e)=>({width:"number"==typeof e.width?e.width:n8.tQ,height:t.height})),aM=(t,e,r)=>{switch(e){case"xAxis":return ag(t,r).width;case"yAxis":return aP(t,r).height;default:return}},aS=(t,e,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=(0,r4._L)(t,n),c=e.map(t=>t.value);if(o&&l&&"category"===a&&i&&(0,r6.CG)(c))return c}},aA=(0,i.Mz)([r2.fz,iP,ic,n3.N],aS),aE=(t,e,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if((0,r4._L)(t,n)&&("number"===i||"auto"!==a))return e.map(t=>t.value)}},a_=(0,i.Mz)([r2.fz,iP,iu,n3.N],aE),ak=(0,i.Mz)([r2.fz,(t,e,r)=>{switch(e){case"xAxis":return ir(t,r);case"yAxis":return ia(t,r);default:throw Error("Unexpected axis type: ".concat(e))}},i6,au,aA,a_,al,i9,n3.N],(t,e,r,n,i,a,o,l,c)=>{if(null==e)return null;var u=(0,r4._L)(t,c);return{angle:e.angle,interval:e.interval,minTickGap:e.minTickGap,orientation:e.orientation,tick:e.tick,tickCount:e.tickCount,tickFormatter:e.tickFormatter,ticks:e.ticks,type:e.type,unit:e.unit,axisType:c,categoricalDomain:a,duplicateDomain:i,isCategorical:u,niceTicks:l,range:o,realScaleType:r,scale:n}}),aT=(0,i.Mz)([r2.fz,iu,i6,au,i9,al,aA,a_,n3.N],(t,e,r,n,i,a,o,l,c)=>{if(null!=e&&null!=n){var u=(0,r4._L)(t,c),{type:s,ticks:f,tickCount:h}=e,d="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/d:0;p="angleAxis"===c&&null!=a&&a.length>=2?2*(0,r6.sA)(a[0]-a[1])*p:p;var y=f||i;return y?y.map((t,e)=>({index:e,coordinate:n(o?o.indexOf(t):t)+p,value:t,offset:p})).filter(t=>!(0,r6.M8)(t.coordinate)):u&&l?l.map((t,e)=>({coordinate:n(t)+p,value:t,index:e,offset:p})):n.ticks?n.ticks(h).map(t=>({coordinate:n(t)+p,value:t,offset:p})):n.domain().map((t,e)=>({coordinate:n(t)+p,value:o?o[t]:t,index:e,offset:p}))}}),aC=(0,i.Mz)([r2.fz,iu,au,al,aA,a_,n3.N],(t,e,r,n,i,a,o)=>{if(null!=e&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,r4._L)(t,o),{tickCount:c}=e,u=0;return(u="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*(0,r6.sA)(n[0]-n[1])*u:u,l&&a)?a.map((t,e)=>({coordinate:r(t)+u,value:t,index:e,offset:u})):r.ticks?r.ticks(c).map(t=>({coordinate:r(t)+u,value:t,offset:u})):r.domain().map((t,e)=>({coordinate:r(t)+u,value:i?i[t]:t,index:e,offset:u}))}}),aD=(0,i.Mz)(ic,au,(t,e)=>{if(null!=t&&null!=e)return n7(n7({},t),{},{scale:e})}),aN=(0,i.Mz)([ic,i6,i4,ac],i5);(0,i.Mz)((t,e,r)=>il(t,r),aN,(t,e)=>{if(null!=t&&null!=e)return n7(n7({},t),{},{scale:e})});var aI=(0,i.Mz)([r2.fz,nQ.h,nQ.W],(t,e,r)=>{switch(t){case"horizontal":return e.some(t=>t.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(t=>t.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},86445:(t,e,r)=>{"use strict";r.d(e,{A$:()=>i,HK:()=>o,Lp:()=>n,et:()=>a});var n=t=>t.layout.width,i=t=>t.layout.height,a=t=>t.layout.scale,o=t=>t.layout.margin},87509:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(35314),i=r(76431),a=r(53038),o=r(43574);e.get=function t(e,r,l){if(null==e)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=e[r];if(void 0===a)if(i.isDeepKey(r))return t(e,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let t=e[r];if(void 0===t)return l;return t}default:{if(Array.isArray(r)){var c=e,u=r,s=l;if(0===u.length)return s;let t=c;for(let e=0;e<u.length;e++){if(null==t||n.isUnsafeProperty(u[e]))return s;t=t[u[e]]}return void 0===t?s:t}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let t=e[r];if(void 0===t)return l;return t}}}},90015:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.noop=function(){}},90830:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(26349),i=r(49899);e.isArrayLikeObject=function(t){return i.isObjectLike(t)&&n.isArrayLike(t)}},91428:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.getTag=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}},91653:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.getSymbols=function(t){return Object.getOwnPropertySymbols(t).filter(e=>Object.prototype.propertyIsEnumerable.call(t,e))}},92292:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(69404),i=r(90015);e.isEqual=function(t,e){return n.isEqualWith(t,e,i.noop)}},92681:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.identity=function(t){return t}},92867:(t,e,r)=>{t.exports=r(60324).isPlainObject},92923:(t,e,r)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let n=r(91653),i=r(91428),a=r(27469),o=r(23457),l=r(21251);function c(t,e,r,n=new Map,s){let f=s?.(t,e,r,n);if(null!=f)return f;if(o.isPrimitive(t))return t;if(n.has(t))return n.get(t);if(Array.isArray(t)){let e=Array(t.length);n.set(t,e);for(let i=0;i<t.length;i++)e[i]=c(t[i],i,r,n,s);return Object.hasOwn(t,"index")&&(e.index=t.index),Object.hasOwn(t,"input")&&(e.input=t.input),e}if(t instanceof Date)return new Date(t.getTime());if(t instanceof RegExp){let e=new RegExp(t.source,t.flags);return e.lastIndex=t.lastIndex,e}if(t instanceof Map){let e=new Map;for(let[i,a]of(n.set(t,e),t))e.set(i,c(a,i,r,n,s));return e}if(t instanceof Set){let e=new Set;for(let i of(n.set(t,e),t))e.add(c(i,void 0,r,n,s));return e}if("undefined"!=typeof Buffer&&Buffer.isBuffer(t))return t.subarray();if(l.isTypedArray(t)){let e=new(Object.getPrototypeOf(t)).constructor(t.length);n.set(t,e);for(let i=0;i<t.length;i++)e[i]=c(t[i],i,r,n,s);return e}if(t instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&t instanceof SharedArrayBuffer)return t.slice(0);if(t instanceof DataView){let e=new DataView(t.buffer.slice(0),t.byteOffset,t.byteLength);return n.set(t,e),u(e,t,r,n,s),e}if("undefined"!=typeof File&&t instanceof File){let e=new File([t],t.name,{type:t.type});return n.set(t,e),u(e,t,r,n,s),e}if(t instanceof Blob){let e=new Blob([t],{type:t.type});return n.set(t,e),u(e,t,r,n,s),e}if(t instanceof Error){let e=new t.constructor;return n.set(t,e),e.message=t.message,e.name=t.name,e.stack=t.stack,e.cause=t.cause,u(e,t,r,n,s),e}if("object"==typeof t&&function(t){switch(i.getTag(t)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(t)){let e=Object.create(Object.getPrototypeOf(t));return n.set(t,e),u(e,t,r,n,s),e}return t}function u(t,e,r=t,i,a){let o=[...Object.keys(e),...n.getSymbols(e)];for(let n=0;n<o.length;n++){let l=o[n],u=Object.getOwnPropertyDescriptor(t,l);(null==u||u.writable)&&(t[l]=c(e[l],l,r,i,a))}}e.cloneDeepWith=function(t,e){return c(t,void 0,t,new Map,e)},e.cloneDeepWithImpl=c,e.copyProperties=u},94728:(t,e,r)=>{"use strict";r.d(e,{C:()=>l,U:()=>c});var n=r(84648),i=r(69107),a=r(86445),o=r(22989),l=t=>t.brush,c=(0,n.Mz)([l,i.GO,a.HK],(t,e,r)=>({height:t.height,x:(0,o.Et)(t.x)?t.x:e.left,y:(0,o.Et)(t.y)?t.y:e.top+e.height+e.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.Et)(t.width)?t.width:e.width}))},95819:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.isSymbol=function(t){return"symbol"==typeof t||t instanceof Symbol}},96075:(t,e,r)=>{"use strict";r.d(e,{P:()=>u});var n=r(20237);function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var o={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},c="recharts_measurement_span",u=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.m.isSsr)return{width:0,height:0};var i=(Object.keys(e=a({},r)).forEach(t=>{e[t]||delete e[t]}),e),u=JSON.stringify({text:t,copyStyle:i});if(o.widthCache[u])return o.widthCache[u];try{var s=document.getElementById(c);s||((s=document.createElement("span")).setAttribute("id",c),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var f=a(a({},l),i);Object.assign(s.style,f),s.textContent="".concat(t);var h=s.getBoundingClientRect(),d={width:h.width,height:h.height};return o.widthCache[u]=d,++o.cacheCount>2e3&&(o.cacheCount=0,o.widthCache={}),d}catch(t){return{width:0,height:0}}}},97350:(t,e,r)=>{"use strict";r.d(e,{JN:()=>n,_5:()=>i,eC:()=>l,gY:()=>a,hX:()=>s,iO:()=>c,lZ:()=>u,pH:()=>f,x3:()=>o});var n=t=>t.rootProps.maxBarSize,i=t=>t.rootProps.barGap,a=t=>t.rootProps.barCategoryGap,o=t=>t.rootProps.barSize,l=t=>t.rootProps.stackOffset,c=t=>t.options.chartName,u=t=>t.rootProps.syncId,s=t=>t.rootProps.syncMethod,f=t=>t.options.eventEmitter},97371:(t,e,r)=>{"use strict";r.d(e,{P:()=>i});var n=r(12128),i=(t,e)=>{var r=null==t?void 0:t.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var a=Infinity;return e.length>0&&(a=e.length-1),String(Math.max(0,Math.min(i,a)))}},97633:(t,e,r)=>{"use strict";r.d(e,{J:()=>x,Z:()=>p});var n=r(43210),i=r(49384),a=r(23561),o=r(54186),l=r(22989),c=r(19335),u=["offset"];function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(){return(h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var d=t=>{var{value:e,formatter:r}=t,n=(0,l.uy)(t.children)?e:t.children;return"function"==typeof r?r(n):n},p=t=>null!=t&&"function"==typeof t,y=(t,e)=>(0,l.sA)(e-t)*Math.min(Math.abs(e-t),360),v=(t,e,r)=>{var a,o,{position:u,viewBox:s,offset:f,className:d}=t,{cx:p,cy:v,innerRadius:g,outerRadius:m,startAngle:b,endAngle:x,clockWise:w}=s,O=(g+m)/2,j=y(b,x),P=j>=0?1:-1;"insideStart"===u?(a=b+P*f,o=w):"insideEnd"===u?(a=x-P*f,o=!w):"end"===u&&(a=x+P*f,o=w),o=j<=0?o:!o;var M=(0,c.IZ)(p,v,O,a),S=(0,c.IZ)(p,v,O,a+(o?1:-1)*359),A="M".concat(M.x,",").concat(M.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(+!o,",\n    ").concat(S.x,",").concat(S.y),E=(0,l.uy)(t.id)?(0,l.NF)("recharts-radial-line-"):t.id;return n.createElement("text",h({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",d)}),n.createElement("defs",null,n.createElement("path",{id:E,d:A})),n.createElement("textPath",{xlinkHref:"#".concat(E)},e))},g=t=>{var{viewBox:e,offset:r,position:n}=t,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:u,endAngle:s}=e,f=(u+s)/2;if("outside"===n){var{x:h,y:d}=(0,c.IZ)(i,a,l+r,f);return{x:h,y:d,textAnchor:h>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:p,y}=(0,c.IZ)(i,a,(o+l)/2,f);return{x:p,y,textAnchor:"middle",verticalAnchor:"middle"}},m=t=>{var{viewBox:e,parentViewBox:r,offset:n,position:i}=t,{x:a,y:o,width:c,height:u}=e,s=u>=0?1:-1,h=s*n,d=s>0?"end":"start",p=s>0?"start":"end",y=c>=0?1:-1,v=y*n,g=y>0?"end":"start",m=y>0?"start":"end";if("top"===i)return f(f({},{x:a+c/2,y:o-s*n,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(o-r.y,0),width:c}:{});if("bottom"===i)return f(f({},{x:a+c/2,y:o+u+h,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(o+u),0),width:c}:{});if("left"===i){var b={x:a-v,y:o+u/2,textAnchor:g,verticalAnchor:"middle"};return f(f({},b),r?{width:Math.max(b.x-r.x,0),height:u}:{})}if("right"===i){var x={x:a+c+v,y:o+u/2,textAnchor:m,verticalAnchor:"middle"};return f(f({},x),r?{width:Math.max(r.x+r.width-x.x,0),height:u}:{})}var w=r?{width:c,height:u}:{};return"insideLeft"===i?f({x:a+v,y:o+u/2,textAnchor:m,verticalAnchor:"middle"},w):"insideRight"===i?f({x:a+c-v,y:o+u/2,textAnchor:g,verticalAnchor:"middle"},w):"insideTop"===i?f({x:a+c/2,y:o+h,textAnchor:"middle",verticalAnchor:p},w):"insideBottom"===i?f({x:a+c/2,y:o+u-h,textAnchor:"middle",verticalAnchor:d},w):"insideTopLeft"===i?f({x:a+v,y:o+h,textAnchor:m,verticalAnchor:p},w):"insideTopRight"===i?f({x:a+c-v,y:o+h,textAnchor:g,verticalAnchor:p},w):"insideBottomLeft"===i?f({x:a+v,y:o+u-h,textAnchor:m,verticalAnchor:d},w):"insideBottomRight"===i?f({x:a+c-v,y:o+u-h,textAnchor:g,verticalAnchor:d},w):i&&"object"==typeof i&&((0,l.Et)(i.x)||(0,l._3)(i.x))&&((0,l.Et)(i.y)||(0,l._3)(i.y))?f({x:a+(0,l.F4)(i.x,c),y:o+(0,l.F4)(i.y,u),textAnchor:"end",verticalAnchor:"end"},w):f({x:a+c/2,y:o+u/2,textAnchor:"middle",verticalAnchor:"middle"},w)},b=t=>"cx"in t&&(0,l.Et)(t.cx);function x(t){var e,{offset:r=5}=t,c=f({offset:r},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,u)),{viewBox:s,position:p,value:y,children:x,content:w,className:O="",textBreakAll:j,labelRef:P}=c;if(!s||(0,l.uy)(y)&&(0,l.uy)(x)&&!(0,n.isValidElement)(w)&&"function"!=typeof w)return null;if((0,n.isValidElement)(w))return(0,n.cloneElement)(w,c);if("function"==typeof w){if(e=(0,n.createElement)(w,c),(0,n.isValidElement)(e))return e}else e=d(c);var M=b(s),S=(0,o.J9)(c,!0);if(M&&("insideStart"===p||"insideEnd"===p||"end"===p))return v(c,e,S);var A=M?g(c):m(c);return n.createElement(a.E,h({ref:P,className:(0,i.$)("recharts-label",O)},S,A,{breakAll:j}),e)}x.displayName="Label";var w=t=>{var{cx:e,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:c,innerRadius:u,outerRadius:s,x:f,y:h,top:d,left:p,width:y,height:v,clockWise:g,labelViewBox:m}=t;if(m)return m;if((0,l.Et)(y)&&(0,l.Et)(v)){if((0,l.Et)(f)&&(0,l.Et)(h))return{x:f,y:h,width:y,height:v};if((0,l.Et)(d)&&(0,l.Et)(p))return{x:d,y:p,width:y,height:v}}return(0,l.Et)(f)&&(0,l.Et)(h)?{x:f,y:h,width:0,height:0}:(0,l.Et)(e)&&(0,l.Et)(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:u||0,outerRadius:s||c||o||0,clockWise:g}:t.viewBox?t.viewBox:{}},O=(t,e,r)=>{if(!t)return null;var i={viewBox:e,labelRef:r};return!0===t?n.createElement(x,h({key:"label-implicit"},i)):(0,l.vh)(t)?n.createElement(x,h({key:"label-implicit",value:t},i)):(0,n.isValidElement)(t)?t.type===x?(0,n.cloneElement)(t,f({key:"label-implicit"},i)):n.createElement(x,h({key:"label-implicit",content:t},i)):p(t)?n.createElement(x,h({key:"label-implicit",content:t},i)):t&&"object"==typeof t?n.createElement(x,h({},t,{key:"label-implicit"},i)):null};x.parseViewBox=w,x.renderCallByParent=function(t,e){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var{children:i,labelRef:a}=t,l=w(t),c=(0,o.aS)(i,x).map((t,r)=>(0,n.cloneElement)(t,{viewBox:e||l,key:"label-".concat(r)}));return r?[O(t.label,e||l,a),...c]:c}},97668:(t,e)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,a=r?Symbol.for("react.fragment"):60107,o=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,s=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,h=r?Symbol.for("react.forward_ref"):60112,d=r?Symbol.for("react.suspense"):60113,p=(r&&Symbol.for("react.suspense_list"),r?Symbol.for("react.memo"):60115),y=r?Symbol.for("react.lazy"):60116;function v(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case s:case f:case a:case l:case o:case d:return t;default:switch(t=t&&t.$$typeof){case u:case h:case y:case p:case c:return t;default:return e}}case i:return e}}}r&&Symbol.for("react.block"),r&&Symbol.for("react.fundamental"),r&&Symbol.for("react.responder"),r&&Symbol.for("react.scope");e.isFragment=function(t){return v(t)===a}},97711:(t,e,r)=>{"use strict";r.d(e,{$:()=>i,X:()=>a});var n=r(43210),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},97766:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e.last=function(t){return t[t.length-1]}},98009:(t,e,r)=>{"use strict";r.d(e,{l3:()=>u,m7:()=>s}),r(43210);var n=r(43209),i=r(97350);new(r(11117)),r(49605),r(17118);var a=r(21426),o=r(69009);function l(t){return t.tooltip.syncInteraction}var c=r(51426);function u(){(0,n.j)(),(0,n.G)(i.lZ),(0,n.G)(i.pH),(0,n.j)(),(0,n.G)(i.hX),(0,n.G)(o.R4),(0,c.WX)(),(0,c.sk)(),(0,n.G)(t=>t.rootProps.className),(0,n.G)(i.lZ),(0,n.G)(i.pH),(0,n.j)()}function s(t,e,r,o,c,u){(0,n.G)(r=>(0,a.dp)(r,t,e)),(0,n.G)(i.pH),(0,n.G)(i.lZ),(0,n.G)(i.hX);var s=(0,n.G)(l);null==s||s.active}r(64267)},98150:(t,e)=>{"use strict";Object.defineProperty(e,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;e.isIndex=function(t,e=Number.MAX_SAFE_INTEGER){switch(typeof t){case"number":return Number.isInteger(t)&&t>=0&&t<e;case"symbol":return!1;case"string":return r.test(t)}}},98845:(t,e,r)=>{"use strict";r.d(e,{Z:()=>m});var n=r(43210),i=r(9474),a=r.n(i),o=r(97633),l=r(98986),c=r(54186),u=r(64279),s=r(22989),f=["valueAccessor"],h=["data","dataKey","clockWise","id","textBreakAll"];function d(){return(d=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach(function(e){var n,i,a;n=t,i=e,a=r[e],(i=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function v(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var g=t=>Array.isArray(t.value)?a()(t.value):t.value;function m(t){var{valueAccessor:e=g}=t,r=v(t,f),{data:i,dataKey:a,clockWise:p,id:m,textBreakAll:b}=r,x=v(r,h);return i&&i.length?n.createElement(l.W,{className:"recharts-label-list"},i.map((t,r)=>{var i=(0,s.uy)(a)?e(t,r):(0,u.kr)(t&&t.payload,a),l=(0,s.uy)(m)?{}:{id:"".concat(m,"-").concat(r)};return n.createElement(o.J,d({},(0,c.J9)(t,!0),x,l,{parentViewBox:t.parentViewBox,value:i,textBreakAll:b,viewBox:o.J.parseViewBox((0,s.uy)(p)?t:y(y({},t),{},{clockWise:p})),key:"label-".concat(r),index:r}))})):null}m.displayName="LabelList",m.renderCallByParent=function(t,e){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var{children:a}=t,l=(0,c.aS)(a,m).map((t,r)=>(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)}));return i?[(r=t.label,r?!0===r?n.createElement(m,{key:"labelList-implicit",data:e}):n.isValidElement(r)||(0,o.Z)(r)?n.createElement(m,{key:"labelList-implicit",data:e,content:r}):"object"==typeof r?n.createElement(m,d({data:e},r,{key:"labelList-implicit"})):null:null),...l]:l}},98986:(t,e,r)=>{"use strict";r.d(e,{W:()=>c});var n=r(43210),i=r(49384),a=r(54186),o=["children","className"];function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)({}).hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(null,arguments)}var c=n.forwardRef((t,e)=>{var{children:r,className:c}=t,u=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(({}).hasOwnProperty.call(t,n)){if(-1!==e.indexOf(n))continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(n=0;n<a.length;n++)r=a[n],-1===e.indexOf(r)&&({}).propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,o),s=(0,i.$)("recharts-layer",c);return n.createElement("g",l({className:s},(0,a.J9)(u,!0),{ref:e}),r)})}};