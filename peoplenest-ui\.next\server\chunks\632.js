"use strict";exports.id=632,exports.ids=[632],exports.modules={43:(e,t,n)=>{n.d(t,{jH:()=>o});var r=n(43210);n(60687);var l=r.createContext(void 0);function o(e){let t=r.useContext(l);return e||t||"ltr"}},363:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},1359:(e,t,n)=>{n.d(t,{Oh:()=>o});var r=n(43210),l=0;function o(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),l++,()=>{1===l&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),l--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},1994:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]])},2030:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],l=n[0];if(Array.isArray(r)&&Array.isArray(l)){if(r[0]!==l[0]||r[2]!==l[2])return!0}else if(r!==l)return!0;if(t[4])return!n[4];if(n[4])return!0;let o=Object.values(t[1])[0],a=Object.values(n[1])[0];return!o||!a||e(o,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return l}});let r=n(19169);function l(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},3589:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},5144:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return c}});let r=n(51550),l=n(59656);var o=l._("_maxConcurrency"),a=l._("_runningCount"),i=l._("_queue"),u=l._("_processNext");class c{enqueue(e){let t,n,l=new Promise((e,r)=>{t=e,n=r}),o=async()=>{try{r._(this,a)[a]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,a)[a]--,r._(this,u)[u]()}};return r._(this,i)[i].push({promiseFn:l,task:o}),r._(this,u)[u](),l}bump(e){let t=r._(this,i)[i].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,i)[i].splice(t,1)[0];r._(this,i)[i].unshift(e),r._(this,u)[u](!0)}}constructor(e=5){Object.defineProperty(this,u,{value:s}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,i,{writable:!0,value:void 0}),r._(this,o)[o]=e,r._(this,a)[a]=0,r._(this,i)[i]=[]}}function s(e){if(void 0===e&&(e=!1),(r._(this,a)[a]<r._(this,o)[o]||e)&&r._(this,i)[i].length>0){var t;null==(t=r._(this,i)[i].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DYNAMIC_STALETIME_MS:function(){return f},STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return c},getOrCreatePrefetchCacheEntry:function(){return u},prunePrefetchCache:function(){return d}});let r=n(59008),l=n(59154),o=n(75076);function a(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function i(e,t,n){return a(e,t===l.PrefetchKind.FULL,n)}function u(e){let{url:t,nextUrl:n,tree:r,prefetchCache:o,kind:i,allowAliasing:u=!0}=e,c=function(e,t,n,r,o){for(let i of(void 0===t&&(t=l.PrefetchKind.TEMPORARY),[n,null])){let n=a(e,!0,i),u=a(e,!1,i),c=e.search?n:u,s=r.get(c);if(s&&o){if(s.url.pathname===e.pathname&&s.url.search!==e.search)return{...s,aliased:!0};return s}let d=r.get(u);if(o&&e.search&&t!==l.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==l.PrefetchKind.FULL&&o){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,i,n,o,u);return c?(c.status=h(c),c.kind!==l.PrefetchKind.FULL&&i===l.PrefetchKind.FULL&&c.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return s({tree:r,url:t,nextUrl:n,prefetchCache:o,kind:null!=i?i:l.PrefetchKind.TEMPORARY})}),i&&c.kind===l.PrefetchKind.TEMPORARY&&(c.kind=i),c):s({tree:r,url:t,nextUrl:n,prefetchCache:o,kind:i||l.PrefetchKind.TEMPORARY})}function c(e){let{nextUrl:t,tree:n,prefetchCache:r,url:o,data:a,kind:u}=e,c=a.couldBeIntercepted?i(o,u,t):i(o,u),s={treeAtTimeOfPrefetch:n,data:Promise.resolve(a),kind:u,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:c,status:l.PrefetchCacheEntryStatus.fresh,url:o};return r.set(c,s),s}function s(e){let{url:t,kind:n,tree:a,nextUrl:u,prefetchCache:c}=e,s=i(t,n),d=o.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:a,nextUrl:u,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:l}=e,o=r.get(l);if(!o)return;let a=i(t,o.kind,n);return r.set(a,{...o,key:a}),r.delete(l),a}({url:t,existingCacheKey:s,nextUrl:u,prefetchCache:c})),e.prerendered){let t=c.get(null!=n?n:s);t&&(t.kind=l.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),f={treeAtTimeOfPrefetch:a,data:d,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:s,status:l.PrefetchCacheEntryStatus.fresh,url:t};return c.set(s,f),f}function d(e){for(let[t,n]of e)h(n)===l.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("0"),p=1e3*Number("300");function h(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:o}=e;return -1!==o?Date.now()<n+o?l.PrefetchCacheEntryStatus.fresh:l.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+f?r?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.fresh:t===l.PrefetchKind.AUTO&&Date.now()<n+p?l.PrefetchCacheEntryStatus.stale:t===l.PrefetchKind.FULL&&Date.now()<n+p?l.PrefetchCacheEntryStatus.reusable:l.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6361:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return l}});let r=n(96127);function l(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8830:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(59154),n(25232),n(29651),n(28627),n(78866),n(75076),n(97936),n(35429);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9510:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(43210),l=n(11273),o=n(98599),a=n(8730),i=n(60687);function u(e){let t=e+"CollectionProvider",[n,u]=(0,l.A)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,l=r.useRef(null),o=r.useRef(new Map).current;return(0,i.jsx)(c,{scope:t,itemMap:o,collectionRef:l,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=(0,a.TL)(f),h=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,l=s(f,n),a=(0,o.s)(t,l.collectionRef);return(0,i.jsx)(p,{ref:a,children:r})});h.displayName=f;let y=e+"CollectionItemSlot",v="data-radix-collection-item",m=(0,a.TL)(y),g=r.forwardRef((e,t)=>{let{scope:n,children:l,...a}=e,u=r.useRef(null),c=(0,o.s)(t,u),d=s(y,n);return r.useEffect(()=>(d.itemMap.set(u,{ref:u,...a}),()=>void d.itemMap.delete(u))),(0,i.jsx)(m,{...{[v]:""},ref:c,children:l})});return g.displayName=y,[{Provider:d,Slot:h,ItemSlot:g},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}var c=new WeakMap;function s(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=d(t),l=r>=0?r:n+r;return l<0||l>=n?-1:l}(e,t);return -1===n?void 0:e[n]}function d(e){return e!=e||0===e?0:Math.trunc(e)}},9707:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return s}});let r=n(83913),l=n(89752),o=n(86770),a=n(57391),i=n(33123),u=n(33898),c=n(59435);function s(e,t,n,s,f){let p,h=t.tree,y=t.cache,v=(0,a.createHrefFromUrl)(s);if("string"==typeof n)return!1;for(let t of n){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(t.seedData))continue;let n=t.tree;n=d(n,Object.fromEntries(s.searchParams));let{seedData:a,isRootRender:c,pathToSegment:f}=t,m=["",...f];n=d(n,Object.fromEntries(s.searchParams));let g=(0,o.applyRouterStatePatchToTree)(m,h,n,v),b=(0,l.createEmptyCacheNode)();if(c&&a){let t=a[1];b.loading=a[3],b.rsc=t,function e(t,n,l,o,a){if(0!==Object.keys(o[1]).length)for(let u in o[1]){let c,s=o[1][u],d=s[0],f=(0,i.createRouterCacheKey)(d),p=null!==a&&void 0!==a[2][u]?a[2][u]:null;if(null!==p){let e=p[1],n=p[3];c={lazyData:null,rsc:d.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let h=n.parallelRoutes.get(u);h?h.set(f,c):n.parallelRoutes.set(u,new Map([[f,c]])),e(t,c,l,s,p)}}(e,b,y,n,a)}else b.rsc=y.rsc,b.prefetchRsc=y.prefetchRsc,b.loading=y.loading,b.parallelRoutes=new Map(y.parallelRoutes),(0,u.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,y,t);g&&(h=g,y=b,p=!0)}return!!p&&(f.patchedTree=h,f.cache=y,f.canonicalUrl=v,f.hashFragment=s.hash,(0,c.handleMutable)(t,f))}function d(e,t){let[n,l,...o]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),l,...o];let a={};for(let[e,n]of Object.entries(l))a[e]=d(n,t);return[n,a,...o]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10022:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11096:(e,t,n)=>{n.d(t,{H4:()=>_,_V:()=>E,bL:()=>R});var r=n(43210),l=n(11273),o=n(13495),a=n(66156),i=n(14163),u=n(57379);function c(){return()=>{}}var s=n(60687),d="Avatar",[f,p]=(0,l.A)(d),[h,y]=f(d),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...l}=e,[o,a]=r.useState("idle");return(0,s.jsx)(h,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:a,children:(0,s.jsx)(i.sG.span,{...l,ref:t})})});v.displayName=d;var m="AvatarImage",g=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:l,onLoadingStatusChange:d=()=>{},...f}=e,p=y(m,n),h=function(e,{referrerPolicy:t,crossOrigin:n}){let l=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),o=r.useRef(null),i=l?(o.current||(o.current=new window.Image),o.current):null,[s,d]=r.useState(()=>x(i,e));return(0,a.N)(()=>{d(x(i,e))},[i,e]),(0,a.N)(()=>{let e=e=>()=>{d(e)};if(!i)return;let r=e("loaded"),l=e("error");return i.addEventListener("load",r),i.addEventListener("error",l),t&&(i.referrerPolicy=t),"string"==typeof n&&(i.crossOrigin=n),()=>{i.removeEventListener("load",r),i.removeEventListener("error",l)}},[i,n,t]),s}(l,f),v=(0,o.c)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==h&&v(h)},[h,v]),"loaded"===h?(0,s.jsx)(i.sG.img,{...f,ref:t,src:l}):null});g.displayName=m;var b="AvatarFallback",w=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:l,...o}=e,a=y(b,n),[u,c]=r.useState(void 0===l);return r.useEffect(()=>{if(void 0!==l){let e=window.setTimeout(()=>c(!0),l);return()=>window.clearTimeout(e)}},[l]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(i.sG.span,{...o,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=b;var R=v,E=g,_=w},11273:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>o});var r=n(43210),l=n(60687);function o(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,a=r.useMemo(()=>o,Object.values(o));return(0,l.jsx)(n.Provider,{value:a,children:t})};return o.displayName=e+"Provider",[o,function(l){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let l=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:l}}),[n,l])}};return o.scopeName=e,[function(t,o){let a=r.createContext(o),i=n.length;n=[...n,o];let u=t=>{let{scope:n,children:o,...u}=t,c=n?.[e]?.[i]||a,s=r.useMemo(()=>u,Object.values(u));return(0,l.jsx)(c.Provider,{value:s,children:o})};return u.displayName=t+"Provider",[u,function(n,l){let u=l?.[e]?.[i]||a,c=r.useContext(u);if(c)return c;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=n.reduce((t,{useScope:n,scopeName:r})=>{let l=n(e)[`__scope${r}`];return{...t,...l}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}(o,...t)]}},11437:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},11860:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12941:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},13495:(e,t,n)=>{n.d(t,{c:()=>l});var r=n(43210);function l(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},13964:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14163:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>i});var r=n(43210),l=n(51215),o=n(8730),a=n(60687),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:l,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l?n:t,{...o,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function u(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},14952:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},18468:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,o){let a=o.length<=2,[i,u]=o,c=(0,r.createRouterCacheKey)(u),s=n.parallelRoutes.get(i);if(!s)return;let d=t.parallelRoutes.get(i);if(d&&d!==s||(d=new Map(s),t.parallelRoutes.set(i,d)),a)return void d.delete(c);let f=s.get(c),p=d.get(c);p&&f&&(p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},d.set(c,p)),e(p,f,(0,l.getNextFlightSegmentPath)(o)))}}});let r=n(33123),l=n(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},18853:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(43210),l=n(66156);function o(e){let[t,n]=r.useState(void 0);return(0,l.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,l;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,l=t.blockSize}else r=e.offsetWidth,l=e.offsetHeight;n({width:r,height:l})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},19169:(e,t)=>{function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},21134:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},22308:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,l,,a]=t;for(let i in r.includes(o.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=n,t[3]="refresh"),l)e(l[i],n)}},refreshInactiveParallelSegments:function(){return a}});let r=n(56928),l=n(59008),o=n(83913);async function a(e){let t=new Set;await i({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function i(e){let{navigatedAt:t,state:n,updatedTree:o,updatedCache:a,includeNextUrl:u,fetchedSegments:c,rootTree:s=o,canonicalUrl:d}=e,[,f,p,h]=o,y=[];if(p&&p!==d&&"refresh"===h&&!c.has(p)){c.add(p);let e=(0,l.fetchServerResponse)(new URL(p,location.origin),{flightRouterState:[s[0],s[1],s[2],"refetch"],nextUrl:u?n.nextUrl:null}).then(e=>{let{flightData:n}=e;if("string"!=typeof n)for(let e of n)(0,r.applyFlightData)(t,a,a,e)});y.push(e)}for(let e in f){let r=i({navigatedAt:t,state:n,updatedTree:f[e],updatedCache:a,includeNextUrl:u,fetchedSegments:c,rootTree:s,canonicalUrl:d});y.push(r)}await Promise.all(y)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23928:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},24642:(e,t)=>{function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},25028:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(43210),l=n(51215),o=n(14163),a=n(66156),i=n(60687),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[c,s]=r.useState(!1);(0,a.N)(()=>s(!0),[]);let d=n||c&&globalThis?.document?.body;return d?l.createPortal((0,i.jsx)(o.sG.div,{...u,ref:t}),d):null});u.displayName="Portal"},25232:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,n){let{url:x,isExternalUrl:R,navigateType:E,shouldScroll:_,allowAliasing:P}=n,S={},{hash:T}=x,M=(0,l.createHrefFromUrl)(x),j="push"===E;if((0,v.prunePrefetchCache)(t.prefetchCache),S.preserveCustomHistoryState=!1,S.pendingPush=j,R)return b(t,S,x.toString(),j);if(document.getElementById("__next-page-redirect"))return b(t,S,M,j);let O=(0,v.getOrCreatePrefetchCacheEntry)({url:x,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:P}),{treeAtTimeOfPrefetch:A,data:C}=O;return f.prefetchQueue.bump(C),C.then(f=>{let{flightData:v,canonicalUrl:R,postponed:E}=f,P=Date.now(),C=!1;if(O.lastUsedTime||(O.lastUsedTime=P,C=!0),O.aliased){let r=(0,g.handleAliasedPrefetchEntry)(P,t,v,x,S);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof v)return b(t,S,v,j);let N=R?(0,l.createHrefFromUrl)(R):M;if(T&&t.canonicalUrl.split("#",1)[0]===N.split("#",1)[0])return S.onlyHashChange=!0,S.canonicalUrl=N,S.shouldScroll=_,S.hashFragment=T,S.scrollableSegments=[],(0,s.handleMutable)(t,S);let k=t.tree,L=t.cache,D=[];for(let e of v){let{pathToSegment:n,seedData:l,head:s,isHeadPartial:f,isRootRender:v}=e,g=e.tree,R=["",...n],_=(0,a.applyRouterStatePatchToTree)(R,k,g,M);if(null===_&&(_=(0,a.applyRouterStatePatchToTree)(R,A,g,M)),null!==_){if(l&&v&&E){let e=(0,y.startPPRNavigation)(P,L,k,g,l,s,f,!1,D);if(null!==e){if(null===e.route)return b(t,S,M,j);_=e.route;let n=e.node;null!==n&&(S.cache=n);let l=e.dynamicRequestTree;if(null!==l){let n=(0,r.fetchServerResponse)(x,{flightRouterState:l,nextUrl:t.nextUrl});(0,y.listenForDynamicRequest)(e,n)}}else _=g}else{if((0,u.isNavigatingToNewRootLayout)(k,_))return b(t,S,M,j);let r=(0,p.createEmptyCacheNode)(),l=!1;for(let t of(O.status!==c.PrefetchCacheEntryStatus.stale||C?l=(0,d.applyFlightData)(P,L,r,e,O):(l=function(e,t,n,r){let l=!1;for(let o of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),w(r).map(e=>[...n,...e])))(0,m.clearCacheNodeDataForSegmentPath)(e,t,o),l=!0;return l}(r,L,n,g),O.lastUsedTime=P),(0,i.shouldHardNavigate)(R,k)?(r.rsc=L.rsc,r.prefetchRsc=L.prefetchRsc,(0,o.invalidateCacheBelowFlightSegmentPath)(r,L,n),S.cache=r):l&&(S.cache=r,L=r),w(g))){let e=[...n,...t];e[e.length-1]!==h.DEFAULT_SEGMENT_KEY&&D.push(e)}}k=_}}return S.patchedTree=k,S.canonicalUrl=N,S.scrollableSegments=D,S.hashFragment=T,S.shouldScroll=_,(0,s.handleMutable)(t,S)},()=>t)}}});let r=n(59008),l=n(57391),o=n(18468),a=n(86770),i=n(65951),u=n(2030),c=n(59154),s=n(59435),d=n(56928),f=n(75076),p=n(89752),h=n(83913),y=n(65956),v=n(5334),m=n(97464),g=n(9707);function b(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,s.handleMutable)(e,t)}function w(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,l]of Object.entries(r))for(let r of w(l))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25541:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},25942:(e,t,n)=>{function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26134:(e,t,n)=>{n.d(t,{UC:()=>en,VY:()=>el,ZL:()=>ee,bL:()=>J,bm:()=>eo,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(43210),l=n(70569),o=n(98599),a=n(11273),i=n(96963),u=n(65551),c=n(31355),s=n(32547),d=n(25028),f=n(46059),p=n(14163),h=n(1359),y=n(42247),v=n(63376),m=n(8730),g=n(60687),b="Dialog",[w,x]=(0,a.A)(b),[R,E]=w(b),_=e=>{let{__scopeDialog:t,children:n,open:l,defaultOpen:o,onOpenChange:a,modal:c=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.i)({prop:l,defaultProp:o??!1,onChange:a,caller:b});return(0,g.jsx)(R,{scope:t,triggerRef:s,contentRef:d,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};_.displayName=b;var P="DialogTrigger",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=E(P,n),i=(0,o.s)(t,a.triggerRef);return(0,g.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":G(a.open),...r,ref:i,onClick:(0,l.m)(e.onClick,a.onOpenToggle)})});S.displayName=P;var T="DialogPortal",[M,j]=w(T,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:n,children:l,container:o}=e,a=E(T,t);return(0,g.jsx)(M,{scope:t,forceMount:n,children:r.Children.map(l,e=>(0,g.jsx)(f.C,{present:n||a.open,children:(0,g.jsx)(d.Z,{asChild:!0,container:o,children:e})}))})};O.displayName=T;var A="DialogOverlay",C=r.forwardRef((e,t)=>{let n=j(A,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,o=E(A,e.__scopeDialog);return o.modal?(0,g.jsx)(f.C,{present:r||o.open,children:(0,g.jsx)(k,{...l,ref:t})}):null});C.displayName=A;var N=(0,m.TL)("DialogOverlay.RemoveScroll"),k=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(A,n);return(0,g.jsx)(y.A,{as:N,allowPinchZoom:!0,shards:[l.contentRef],children:(0,g.jsx)(p.sG.div,{"data-state":G(l.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),L="DialogContent",D=r.forwardRef((e,t)=>{let n=j(L,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,o=E(L,e.__scopeDialog);return(0,g.jsx)(f.C,{present:r||o.open,children:o.modal?(0,g.jsx)(I,{...l,ref:t}):(0,g.jsx)(U,{...l,ref:t})})});D.displayName=L;var I=r.forwardRef((e,t)=>{let n=E(L,e.__scopeDialog),a=r.useRef(null),i=(0,o.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,g.jsx)(H,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,l.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,l.m)(e.onFocusOutside,e=>e.preventDefault())})}),U=r.forwardRef((e,t)=>{let n=E(L,e.__scopeDialog),l=r.useRef(!1),o=r.useRef(!1);return(0,g.jsx)(H,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(l.current||n.triggerRef.current?.focus(),t.preventDefault()),l.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(l.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),H=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:l,onOpenAutoFocus:a,onCloseAutoFocus:i,...u}=e,d=E(L,n),f=r.useRef(null),p=(0,o.s)(t,f);return(0,h.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(s.n,{asChild:!0,loop:!0,trapped:l,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,g.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":G(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(X,{titleId:d.titleId}),(0,g.jsx)(Z,{contentRef:f,descriptionId:d.descriptionId})]})]})}),F="DialogTitle",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(F,n);return(0,g.jsx)(p.sG.h2,{id:l.titleId,...r,ref:t})});B.displayName=F;var z="DialogDescription",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(z,n);return(0,g.jsx)(p.sG.p,{id:l.descriptionId,...r,ref:t})});W.displayName=z;var K="DialogClose",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(K,n);return(0,g.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,l.m)(e.onClick,()=>o.onOpenChange(!1))})});function G(e){return e?"open":"closed"}V.displayName=K;var q="DialogTitleWarning",[$,Y]=(0,a.q)(q,{contentName:L,titleName:F,docsSlug:"dialog"}),X=({titleId:e})=>{let t=Y(q),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Z=({contentRef:e,descriptionId:t})=>{let n=Y("DialogDescriptionWarning"),l=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(l))},[l,e,t]),null},J=_,Q=S,ee=O,et=C,en=D,er=B,el=W,eo=V},26736:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return l}});let r=n(2255);function l(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28627:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return o}});let r=n(57391),l=n(70642);function o(e,t){var n;let{url:o,tree:a}=t,i=(0,r.createHrefFromUrl)(o),u=a||e.tree,c=e.cache;return{canonicalUrl:i,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:c,prefetchCache:e.prefetchCache,tree:u,nextUrl:null!=(n=(0,l.extractPathFromFlightRouterState)(u))?n:o.pathname}}n(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29651:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return s}});let r=n(57391),l=n(86770),o=n(2030),a=n(25232),i=n(56928),u=n(59435),c=n(89752);function s(e,t){let{serverResponse:{flightData:n,canonicalUrl:s},navigatedAt:d}=t,f={};if(f.preserveCustomHistoryState=!1,"string"==typeof n)return(0,a.handleExternalUrl)(e,f,n,e.pushRef.pendingPush);let p=e.tree,h=e.cache;for(let t of n){let{segmentPath:n,tree:u}=t,y=(0,l.applyRouterStatePatchToTree)(["",...n],p,u,e.canonicalUrl);if(null===y)return e;if((0,o.isNavigatingToNewRootLayout)(p,y))return(0,a.handleExternalUrl)(e,f,e.canonicalUrl,e.pushRef.pendingPush);let v=s?(0,r.createHrefFromUrl)(s):void 0;v&&(f.canonicalUrl=v);let m=(0,c.createEmptyCacheNode)();(0,i.applyFlightData)(d,h,m,t),f.patchedTree=y,f.cache=m,h=m,p=y}return(0,u.handleMutable)(e,f)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return i},urlObjectKeys:function(){return a}});let r=n(40740)._(n(76715)),l=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:n}=e,o=e.protocol||"",a=e.pathname||"",i=e.hash||"",u=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:n&&(c=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(c+=":"+e.port)),u&&"object"==typeof u&&(u=String(r.urlQueryToSearchParams(u)));let s=e.search||u&&"?"+u||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||l.test(o))&&!1!==c?(c="//"+(c||""),a&&"/"!==a[0]&&(a="/"+a)):c||(c=""),i&&"#"!==i[0]&&(i="#"+i),s&&"?"!==s[0]&&(s="?"+s),""+o+c+(a=a.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+i}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return o(e)}},30657:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return l}});let r=n(43210);function l(e,t){let n=(0,r.useRef)(null),l=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(n.current=o(e,r)),t&&(l.current=o(t,r))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31158:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},31355:(e,t,n)=>{n.d(t,{qW:()=>f});var r,l=n(43210),o=n(70569),a=n(14163),i=n(98599),u=n(13495),c=n(60687),s="dismissableLayer.update",d=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:y,onFocusOutside:v,onInteractOutside:m,onDismiss:g,...b}=e,w=l.useContext(d),[x,R]=l.useState(null),E=x?.ownerDocument??globalThis?.document,[,_]=l.useState({}),P=(0,i.s)(t,e=>R(e)),S=Array.from(w.layers),[T]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),M=S.indexOf(T),j=x?S.indexOf(x):-1,O=w.layersWithOutsidePointerEventsDisabled.size>0,A=j>=M,C=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){h("dismissableLayer.pointerDownOutside",n,l,{discrete:!0})},l={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},l=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(l),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...w.branches].some(e=>e.contains(t));A&&!n&&(y?.(e),m?.(e),e.defaultPrevented||g?.())},E),N=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!r.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...w.branches].some(e=>e.contains(t))&&(v?.(e),m?.(e),e.defaultPrevented||g?.())},E);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{j===w.layers.size-1&&(f?.(e),!e.defaultPrevented&&g&&(e.preventDefault(),g()))},E),l.useEffect(()=>{if(x)return n&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(r=E.body.style.pointerEvents,E.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(x)),w.layers.add(x),p(),()=>{n&&1===w.layersWithOutsidePointerEventsDisabled.size&&(E.body.style.pointerEvents=r)}},[x,E,n,w]),l.useEffect(()=>()=>{x&&(w.layers.delete(x),w.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,w]),l.useEffect(()=>{let e=()=>_({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(a.sG.div,{...b,ref:P,style:{pointerEvents:O?A?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,C.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,{discrete:r}){let l=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&l.addEventListener(e,t,{once:!0}),r?(0,a.hO)(l,o):l.dispatchEvent(o)}f.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(d),r=l.useRef(null),o=(0,i.s)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(a.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},32192:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},32547:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(43210),l=n(98599),o=n(14163),a=n(13495),i=n(60687),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:m,...g}=e,[b,w]=r.useState(null),x=(0,a.c)(v),R=(0,a.c)(m),E=r.useRef(null),_=(0,l.s)(t,e=>w(e)),P=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(P.paused||!b)return;let t=e.target;b.contains(t)?E.current=t:h(E.current,{select:!0})},t=function(e){if(P.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||h(E.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,b,P.paused]),r.useEffect(()=>{if(b){y.add(P);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(u,s);b.addEventListener(u,x),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(b))}return()=>{b.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);b.addEventListener(c,R),b.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),b.removeEventListener(c,R),y.remove(P)},0)}}},[b,x,R,P]);let S=r.useCallback(e=>{if(!n&&!d||P.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[l,o]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);l&&o?e.shiftKey||r!==o?e.shiftKey&&r===l&&(e.preventDefault(),n&&h(o,{select:!0})):(e.preventDefault(),n&&h(l,{select:!0})):r===t&&e.preventDefault()}},[n,d,P.paused]);return(0,i.jsx)(o.sG.div,{tabIndex:-1,...g,ref:_,onKeyDown:S})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var y=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},32708:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},33898:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return u},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return c}});let r=n(34400),l=n(41500),o=n(33123),a=n(83913);function i(e,t,n,i,u,c){let{segmentPath:s,seedData:d,tree:f,head:p}=i,h=t,y=n;for(let t=0;t<s.length;t+=2){let n=s[t],i=s[t+1],v=t===s.length-2,m=(0,o.createRouterCacheKey)(i),g=y.parallelRoutes.get(n);if(!g)continue;let b=h.parallelRoutes.get(n);b&&b!==g||(b=new Map(g),h.parallelRoutes.set(n,b));let w=g.get(m),x=b.get(m);if(v){if(d&&(!x||!x.lazyData||x===w)){let t=d[0],n=d[1],o=d[3];x={lazyData:null,rsc:c||t!==a.PAGE_SEGMENT_KEY?n:null,prefetchRsc:null,head:null,prefetchHead:null,loading:o,parallelRoutes:c&&w?new Map(w.parallelRoutes):new Map,navigatedAt:e},w&&c&&(0,r.invalidateCacheByRouterState)(x,w,f),c&&(0,l.fillLazyItemsTillLeafWithHead)(e,x,w,f,d,p,u),b.set(m,x)}continue}x&&w&&(x===w&&(x={lazyData:x.lazyData,rsc:x.rsc,prefetchRsc:x.prefetchRsc,head:x.head,prefetchHead:x.prefetchHead,parallelRoutes:new Map(x.parallelRoutes),loading:x.loading},b.set(m,x)),h=x,y=w)}}function u(e,t,n,r,l){i(e,t,n,r,l,!0)}function c(e,t,n,r,l){i(e,t,n,r,l,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return l}});let r=n(33123);function l(e,t,n){for(let l in n[1]){let o=n[1][l][0],a=(0,r.createRouterCacheKey)(o),i=t.parallelRoutes.get(l);if(i){let t=new Map(i);t.delete(a),e.parallelRoutes.set(l,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35416:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return o},getBotType:function(){return u},isBot:function(){return i}});let r=n(95796),l=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,o=r.HTML_LIMITED_BOT_UA_RE.source;function a(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function i(e){return l.test(e)||a(e)}function u(e){return l.test(e)?"dom":a(e)?"html":void 0}},35429:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return j}});let r=n(11264),l=n(11448),o=n(91563),a=n(59154),i=n(6361),u=n(57391),c=n(25232),s=n(86770),d=n(2030),f=n(59435),p=n(41500),h=n(89752),y=n(68214),v=n(96493),m=n(22308),g=n(74007),b=n(36875),w=n(97860),x=n(5334),R=n(25942),E=n(26736),_=n(24642);n(50593);let{createFromFetch:P,createTemporaryReferenceSet:S,encodeReply:T}=n(19357);async function M(e,t,n){let a,u,{actionId:c,actionArgs:s}=n,d=S(),f=(0,_.extractInfoFromServerReferenceId)(c),p="use-cache"===f.type?(0,_.omitUnusedArgs)(s,f):s,h=await T(p,{temporaryReferences:d}),y=await fetch("",{method:"POST",headers:{Accept:o.RSC_CONTENT_TYPE_HEADER,[o.ACTION_HEADER]:c,[o.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[o.NEXT_URL]:t}:{}},body:h}),v=y.headers.get("x-action-redirect"),[m,b]=(null==v?void 0:v.split(";"))||[];switch(b){case"push":a=w.RedirectType.push;break;case"replace":a=w.RedirectType.replace;break;default:a=void 0}let x=!!y.headers.get(o.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(y.headers.get("x-action-revalidated")||"[[],0,0]");u={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){u={paths:[],tag:!1,cookie:!1}}let R=m?(0,i.assignLocation)(m,new URL(e.canonicalUrl,window.location.href)):void 0,E=y.headers.get("content-type");if(null==E?void 0:E.startsWith(o.RSC_CONTENT_TYPE_HEADER)){let e=await P(Promise.resolve(y),{callServer:r.callServer,findSourceMapURL:l.findSourceMapURL,temporaryReferences:d});return m?{actionFlightData:(0,g.normalizeFlightData)(e.f),redirectLocation:R,redirectType:a,revalidatedParts:u,isPrerender:x}:{actionResult:e.a,actionFlightData:(0,g.normalizeFlightData)(e.f),redirectLocation:R,redirectType:a,revalidatedParts:u,isPrerender:x}}if(y.status>=400)throw Object.defineProperty(Error("text/plain"===E?await y.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:R,redirectType:a,revalidatedParts:u,isPrerender:x}}function j(e,t){let{resolve:n,reject:r}=t,l={},o=e.tree;l.preserveCustomHistoryState=!1;let i=e.nextUrl&&(0,y.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,g=Date.now();return M(e,i,t).then(async y=>{let _,{actionResult:P,actionFlightData:S,redirectLocation:T,redirectType:M,isPrerender:j,revalidatedParts:O}=y;if(T&&(M===w.RedirectType.replace?(e.pushRef.pendingPush=!1,l.pendingPush=!1):(e.pushRef.pendingPush=!0,l.pendingPush=!0),l.canonicalUrl=_=(0,u.createHrefFromUrl)(T,!1)),!S)return(n(P),T)?(0,c.handleExternalUrl)(e,l,T.href,e.pushRef.pendingPush):e;if("string"==typeof S)return n(P),(0,c.handleExternalUrl)(e,l,S,e.pushRef.pendingPush);let A=O.paths.length>0||O.tag||O.cookie;for(let r of S){let{tree:a,seedData:u,head:f,isRootRender:y}=r;if(!y)return console.log("SERVER ACTION APPLY FAILED"),n(P),e;let b=(0,s.applyRouterStatePatchToTree)([""],o,a,_||e.canonicalUrl);if(null===b)return n(P),(0,v.handleSegmentMismatch)(e,t,a);if((0,d.isNavigatingToNewRootLayout)(o,b))return n(P),(0,c.handleExternalUrl)(e,l,_||e.canonicalUrl,e.pushRef.pendingPush);if(null!==u){let t=u[1],n=(0,h.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=u[3],(0,p.fillLazyItemsTillLeafWithHead)(g,n,void 0,a,u,f,void 0),l.cache=n,l.prefetchCache=new Map,A&&await (0,m.refreshInactiveParallelSegments)({navigatedAt:g,state:e,updatedTree:b,updatedCache:n,includeNextUrl:!!i,canonicalUrl:l.canonicalUrl||e.canonicalUrl})}l.patchedTree=b,o=b}return T&&_?(A||((0,x.createSeededPrefetchCacheEntry)({url:T,data:{flightData:S,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:j?a.PrefetchKind.FULL:a.PrefetchKind.AUTO}),l.prefetchCache=e.prefetchCache),r((0,b.getRedirectError)((0,E.hasBasePath)(_)?(0,R.removeBasePath)(_):_,M||w.RedirectType.push))):n(P),(0,f.handleMutable)(e,l)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},40083:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},40228:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41500:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,o,a,i,u,c){if(0===Object.keys(a[1]).length){n.head=u;return}for(let s in a[1]){let d,f=a[1][s],p=f[0],h=(0,r.createRouterCacheKey)(p),y=null!==i&&void 0!==i[2][s]?i[2][s]:null;if(o){let r=o.parallelRoutes.get(s);if(r){let o,a=(null==c?void 0:c.kind)==="auto"&&c.status===l.PrefetchCacheEntryStatus.reusable,i=new Map(r),d=i.get(h);o=null!==y?{lazyData:null,rsc:y[1],prefetchRsc:null,head:null,prefetchHead:null,loading:y[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),navigatedAt:t}:a&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),loading:null,navigatedAt:t},i.set(h,o),e(t,o,d,f,y||null,u,c),n.parallelRoutes.set(s,i);continue}}if(null!==y){let e=y[1],n=y[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let v=n.parallelRoutes.get(s);v?v.set(h,d):n.parallelRoutes.set(s,new Map([[h,d]])),e(t,d,void 0,f,y,u,c)}}}});let r=n(33123),l=n(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42133:(e,t,n)=>{n.d(t,{UC:()=>t7,YJ:()=>ne,In:()=>t4,q7:()=>nn,VF:()=>nl,p4:()=>nr,JU:()=>nt,ZL:()=>t9,bL:()=>t6,wn:()=>na,PP:()=>no,wv:()=>ni,l9:()=>t3,WT:()=>t5,LM:()=>t8});var r=n(43210),l=n(51215);function o(e,[t,n]){return Math.min(n,Math.max(t,e))}var a=n(70569),i=n(9510),u=n(98599),c=n(11273),s=n(43),d=n(31355),f=n(1359),p=n(32547),h=n(96963);let y=["top","right","bottom","left"],v=Math.min,m=Math.max,g=Math.round,b=Math.floor,w=e=>({x:e,y:e}),x={left:"right",right:"left",bottom:"top",top:"bottom"},R={start:"end",end:"start"};function E(e,t){return"function"==typeof e?e(t):e}function _(e){return e.split("-")[0]}function P(e){return e.split("-")[1]}function S(e){return"x"===e?"y":"x"}function T(e){return"y"===e?"height":"width"}function M(e){return["top","bottom"].includes(_(e))?"y":"x"}function j(e){return e.replace(/start|end/g,e=>R[e])}function O(e){return e.replace(/left|right|bottom|top/g,e=>x[e])}function A(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function C(e){let{x:t,y:n,width:r,height:l}=e;return{width:r,height:l,top:n,left:t,right:t+r,bottom:n+l,x:t,y:n}}function N(e,t,n){let r,{reference:l,floating:o}=e,a=M(t),i=S(M(t)),u=T(i),c=_(t),s="y"===a,d=l.x+l.width/2-o.width/2,f=l.y+l.height/2-o.height/2,p=l[u]/2-o[u]/2;switch(c){case"top":r={x:d,y:l.y-o.height};break;case"bottom":r={x:d,y:l.y+l.height};break;case"right":r={x:l.x+l.width,y:f};break;case"left":r={x:l.x-o.width,y:f};break;default:r={x:l.x,y:l.y}}switch(P(t)){case"start":r[i]-=p*(n&&s?-1:1);break;case"end":r[i]+=p*(n&&s?-1:1)}return r}let k=async(e,t,n)=>{let{placement:r="bottom",strategy:l="absolute",middleware:o=[],platform:a}=n,i=o.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:l}),{x:s,y:d}=N(c,r,u),f=r,p={},h=0;for(let n=0;n<i.length;n++){let{name:o,fn:y}=i[n],{x:v,y:m,data:g,reset:b}=await y({x:s,y:d,initialPlacement:r,placement:f,strategy:l,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=m?m:d,p={...p,[o]:{...p[o],...g}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(c=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:l}):b.rects),{x:s,y:d}=N(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:l,middlewareData:p}};async function L(e,t){var n;void 0===t&&(t={});let{x:r,y:l,platform:o,rects:a,elements:i,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=E(t,e),h=A(p),y=i[f?"floating"===d?"reference":"floating":d],v=C(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(y)))||n?y:y.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(i.floating)),boundary:c,rootBoundary:s,strategy:u})),m="floating"===d?{x:r,y:l,width:a.floating.width,height:a.floating.height}:a.reference,g=await (null==o.getOffsetParent?void 0:o.getOffsetParent(i.floating)),b=await (null==o.isElement?void 0:o.isElement(g))&&await (null==o.getScale?void 0:o.getScale(g))||{x:1,y:1},w=C(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:m,offsetParent:g,strategy:u}):m);return{top:(v.top-w.top+h.top)/b.y,bottom:(w.bottom-v.bottom+h.bottom)/b.y,left:(v.left-w.left+h.left)/b.x,right:(w.right-v.right+h.right)/b.x}}function D(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function I(e){return y.some(t=>e[t]>=0)}async function U(e,t){let{placement:n,platform:r,elements:l}=e,o=await (null==r.isRTL?void 0:r.isRTL(l.floating)),a=_(n),i=P(n),u="y"===M(n),c=["left","top"].includes(a)?-1:1,s=o&&u?-1:1,d=E(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return i&&"number"==typeof h&&(p="end"===i?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function H(){return"undefined"!=typeof window}function F(e){return W(e)?(e.nodeName||"").toLowerCase():"#document"}function B(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function z(e){var t;return null==(t=(W(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function W(e){return!!H()&&(e instanceof Node||e instanceof B(e).Node)}function K(e){return!!H()&&(e instanceof Element||e instanceof B(e).Element)}function V(e){return!!H()&&(e instanceof HTMLElement||e instanceof B(e).HTMLElement)}function G(e){return!!H()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof B(e).ShadowRoot)}function q(e){let{overflow:t,overflowX:n,overflowY:r,display:l}=J(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(l)}function $(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function Y(e){let t=X(),n=K(e)?J(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function X(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function Z(e){return["html","body","#document"].includes(F(e))}function J(e){return B(e).getComputedStyle(e)}function Q(e){return K(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ee(e){if("html"===F(e))return e;let t=e.assignedSlot||e.parentNode||G(e)&&e.host||z(e);return G(t)?t.host:t}function et(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let l=function e(t){let n=ee(t);return Z(n)?t.ownerDocument?t.ownerDocument.body:t.body:V(n)&&q(n)?n:e(n)}(e),o=l===(null==(r=e.ownerDocument)?void 0:r.body),a=B(l);if(o){let e=en(a);return t.concat(a,a.visualViewport||[],q(l)?l:[],e&&n?et(e):[])}return t.concat(l,et(l,[],n))}function en(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function er(e){let t=J(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,l=V(e),o=l?e.offsetWidth:n,a=l?e.offsetHeight:r,i=g(n)!==o||g(r)!==a;return i&&(n=o,r=a),{width:n,height:r,$:i}}function el(e){return K(e)?e:e.contextElement}function eo(e){let t=el(e);if(!V(t))return w(1);let n=t.getBoundingClientRect(),{width:r,height:l,$:o}=er(t),a=(o?g(n.width):n.width)/r,i=(o?g(n.height):n.height)/l;return a&&Number.isFinite(a)||(a=1),i&&Number.isFinite(i)||(i=1),{x:a,y:i}}let ea=w(0);function ei(e){let t=B(e);return X()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ea}function eu(e,t,n,r){var l;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),a=el(e),i=w(1);t&&(r?K(r)&&(i=eo(r)):i=eo(e));let u=(void 0===(l=n)&&(l=!1),r&&(!l||r===B(a))&&l)?ei(a):w(0),c=(o.left+u.x)/i.x,s=(o.top+u.y)/i.y,d=o.width/i.x,f=o.height/i.y;if(a){let e=B(a),t=r&&K(r)?B(r):r,n=e,l=en(n);for(;l&&r&&t!==n;){let e=eo(l),t=l.getBoundingClientRect(),r=J(l),o=t.left+(l.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(l.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=o,s+=a,l=en(n=B(l))}}return C({width:d,height:f,x:c,y:s})}function ec(e,t){let n=Q(e).scrollLeft;return t?t.left+n:eu(z(e)).left+n}function es(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ec(e,r)),y:r.top+t.scrollTop}}function ed(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=B(e),r=z(e),l=n.visualViewport,o=r.clientWidth,a=r.clientHeight,i=0,u=0;if(l){o=l.width,a=l.height;let e=X();(!e||e&&"fixed"===t)&&(i=l.offsetLeft,u=l.offsetTop)}return{width:o,height:a,x:i,y:u}}(e,n);else if("document"===t)r=function(e){let t=z(e),n=Q(e),r=e.ownerDocument.body,l=m(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=m(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ec(e),i=-n.scrollTop;return"rtl"===J(r).direction&&(a+=m(t.clientWidth,r.clientWidth)-l),{width:l,height:o,x:a,y:i}}(z(e));else if(K(t))r=function(e,t){let n=eu(e,!0,"fixed"===t),r=n.top+e.clientTop,l=n.left+e.clientLeft,o=V(e)?eo(e):w(1),a=e.clientWidth*o.x,i=e.clientHeight*o.y;return{width:a,height:i,x:l*o.x,y:r*o.y}}(t,n);else{let n=ei(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return C(r)}function ef(e){return"static"===J(e).position}function ep(e,t){if(!V(e)||"fixed"===J(e).position)return null;if(t)return t(e);let n=e.offsetParent;return z(e)===n&&(n=n.ownerDocument.body),n}function eh(e,t){let n=B(e);if($(e))return n;if(!V(e)){let t=ee(e);for(;t&&!Z(t);){if(K(t)&&!ef(t))return t;t=ee(t)}return n}let r=ep(e,t);for(;r&&["table","td","th"].includes(F(r))&&ef(r);)r=ep(r,t);return r&&Z(r)&&ef(r)&&!Y(r)?n:r||function(e){let t=ee(e);for(;V(t)&&!Z(t);){if(Y(t))return t;if($(t))break;t=ee(t)}return null}(e)||n}let ey=async function(e){let t=this.getOffsetParent||eh,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=V(t),l=z(t),o="fixed"===n,a=eu(e,!0,o,t),i={scrollLeft:0,scrollTop:0},u=w(0);if(r||!r&&!o)if(("body"!==F(t)||q(l))&&(i=Q(t)),r){let e=eu(t,!0,o,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else l&&(u.x=ec(l));o&&!r&&l&&(u.x=ec(l));let c=!l||r||o?w(0):es(l,i);return{x:a.left+i.scrollLeft-u.x-c.x,y:a.top+i.scrollTop-u.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ev={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:l}=e,o="fixed"===l,a=z(r),i=!!t&&$(t.floating);if(r===a||i&&o)return n;let u={scrollLeft:0,scrollTop:0},c=w(1),s=w(0),d=V(r);if((d||!d&&!o)&&(("body"!==F(r)||q(a))&&(u=Q(r)),V(r))){let e=eu(r);c=eo(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!a||d||o?w(0):es(a,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:z,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:l}=e,o=[..."clippingAncestors"===n?$(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=et(e,[],!1).filter(e=>K(e)&&"body"!==F(e)),l=null,o="fixed"===J(e).position,a=o?ee(e):e;for(;K(a)&&!Z(a);){let t=J(a),n=Y(a);n||"fixed"!==t.position||(l=null),(o?!n&&!l:!n&&"static"===t.position&&!!l&&["absolute","fixed"].includes(l.position)||q(a)&&!n&&function e(t,n){let r=ee(t);return!(r===n||!K(r)||Z(r))&&("fixed"===J(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):l=t,a=ee(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=o[0],i=o.reduce((e,n)=>{let r=ed(t,n,l);return e.top=m(r.top,e.top),e.right=v(r.right,e.right),e.bottom=v(r.bottom,e.bottom),e.left=m(r.left,e.left),e},ed(t,a,l));return{width:i.right-i.left,height:i.bottom-i.top,x:i.left,y:i.top}},getOffsetParent:eh,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=er(e);return{width:t,height:n}},getScale:eo,isElement:K,isRTL:function(e){return"rtl"===J(e).direction}};function em(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eg=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:l,rects:o,platform:a,elements:i,middlewareData:u}=t,{element:c,padding:s=0}=E(e,t)||{};if(null==c)return{};let d=A(s),f={x:n,y:r},p=S(M(l)),h=T(p),y=await a.getDimensions(c),g="y"===p,b=g?"clientHeight":"clientWidth",w=o.reference[h]+o.reference[p]-f[p]-o.floating[h],x=f[p]-o.reference[p],R=await (null==a.getOffsetParent?void 0:a.getOffsetParent(c)),_=R?R[b]:0;_&&await (null==a.isElement?void 0:a.isElement(R))||(_=i.floating[b]||o.floating[h]);let j=_/2-y[h]/2-1,O=v(d[g?"top":"left"],j),C=v(d[g?"bottom":"right"],j),N=_-y[h]-C,k=_/2-y[h]/2+(w/2-x/2),L=m(O,v(k,N)),D=!u.arrow&&null!=P(l)&&k!==L&&o.reference[h]/2-(k<O?O:C)-y[h]/2<0,I=D?k<O?k-O:k-N:0;return{[p]:f[p]+I,data:{[p]:L,centerOffset:k-L-I,...D&&{alignmentOffset:I}},reset:D}}}),eb=(e,t,n)=>{let r=new Map,l={platform:ev,...n},o={...l.platform,_c:r};return k(e,t,{...l,platform:o})};var ew="undefined"!=typeof document?r.useLayoutEffect:function(){};function ex(e,t){let n,r,l;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ex(e[r],t[r]))return!1;return!0}if((n=(l=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,l[r]))return!1;for(r=n;0!=r--;){let n=l[r];if(("_owner"!==n||!e.$$typeof)&&!ex(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eR(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eE(e,t){let n=eR(e);return Math.round(t*n)/n}function e_(e){let t=r.useRef(e);return ew(()=>{t.current=e}),t}let eP=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eg({element:n.current,padding:r}).fn(t):{}:n?eg({element:n,padding:r}).fn(t):{}}}),eS=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:l,y:o,placement:a,middlewareData:i}=t,u=await U(t,e);return a===(null==(n=i.offset)?void 0:n.placement)&&null!=(r=i.arrow)&&r.alignmentOffset?{}:{x:l+u.x,y:o+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eT=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:l}=t,{mainAxis:o=!0,crossAxis:a=!1,limiter:i={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=E(e,t),c={x:n,y:r},s=await L(t,u),d=M(_(l)),f=S(d),p=c[f],h=c[d];if(o){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=m(n,v(p,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=m(n,v(h,r))}let y=i.fn({...t,[f]:p,[d]:h});return{...y,data:{x:y.x-n,y:y.y-r,enabled:{[f]:o,[d]:a}}}}}}(e),options:[e,t]}),eM=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:l,rects:o,middlewareData:a}=t,{offset:i=0,mainAxis:u=!0,crossAxis:c=!0}=E(e,t),s={x:n,y:r},d=M(l),f=S(d),p=s[f],h=s[d],y=E(i,t),v="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(u){let e="y"===f?"height":"width",t=o.reference[f]-o.floating[e]+v.mainAxis,n=o.reference[f]+o.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var m,g;let e="y"===f?"width":"height",t=["top","left"].includes(_(l)),n=o.reference[d]-o.floating[e]+(t&&(null==(m=a.offset)?void 0:m[d])||0)+(t?0:v.crossAxis),r=o.reference[d]+o.reference[e]+(t?0:(null==(g=a.offset)?void 0:g[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,l,o,a;let{placement:i,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:y,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:g=!0,...b}=E(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let w=_(i),x=M(s),R=_(s)===s,A=await (null==d.isRTL?void 0:d.isRTL(f.floating)),C=y||(R||!g?[O(s)]:function(e){let t=O(e);return[j(e),t,j(t)]}(s)),N="none"!==m;!y&&N&&C.push(...function(e,t,n,r){let l=P(e),o=function(e,t,n){let r=["left","right"],l=["right","left"];switch(e){case"top":case"bottom":if(n)return t?l:r;return t?r:l;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(_(e),"start"===n,r);return l&&(o=o.map(e=>e+"-"+l),t&&(o=o.concat(o.map(j)))),o}(s,g,m,A));let k=[s,...C],D=await L(t,b),I=[],U=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&I.push(D[w]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=P(e),l=S(M(e)),o=T(l),a="x"===l?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(a=O(a)),[a,O(a)]}(i,c,A);I.push(D[e[0]],D[e[1]])}if(U=[...U,{placement:i,overflows:I}],!I.every(e=>e<=0)){let e=((null==(l=u.flip)?void 0:l.index)||0)+1,t=k[e];if(t&&("alignment"!==h||x===M(t)||U.every(e=>e.overflows[0]>0&&M(e.placement)===x)))return{data:{index:e,overflows:U},reset:{placement:t}};let n=null==(o=U.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=U.filter(e=>{if(N){let t=M(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(i!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let l,o,{placement:a,rects:i,platform:u,elements:c}=t,{apply:s=()=>{},...d}=E(e,t),f=await L(t,d),p=_(a),h=P(a),y="y"===M(a),{width:g,height:b}=i.floating;"top"===p||"bottom"===p?(l=p,o=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(o=p,l="end"===h?"top":"bottom");let w=b-f.top-f.bottom,x=g-f.left-f.right,R=v(b-f[l],w),S=v(g-f[o],x),T=!t.middlewareData.shift,j=R,O=S;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(O=x),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(j=w),T&&!h){let e=m(f.left,0),t=m(f.right,0),n=m(f.top,0),r=m(f.bottom,0);y?O=g-2*(0!==e||0!==t?e+t:m(f.left,f.right)):j=b-2*(0!==n||0!==r?n+r:m(f.top,f.bottom))}await s({...t,availableWidth:O,availableHeight:j});let A=await u.getDimensions(c.floating);return g!==A.width||b!==A.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eA=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...l}=E(e,t);switch(r){case"referenceHidden":{let e=D(await L(t,{...l,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:I(e)}}}case"escaped":{let e=D(await L(t,{...l,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:I(e)}}}default:return{}}}}}(e),options:[e,t]}),eC=(e,t)=>({...eP(e),options:[e,t]});var eN=n(14163),ek=n(60687),eL=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:l=5,...o}=e;return(0,ek.jsx)(eN.sG.svg,{...o,ref:t,width:r,height:l,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,ek.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eL.displayName="Arrow";var eD=n(13495),eI=n(66156),eU=n(18853),eH="Popper",[eF,eB]=(0,c.A)(eH),[ez,eW]=eF(eH),eK=e=>{let{__scopePopper:t,children:n}=e,[l,o]=r.useState(null);return(0,ek.jsx)(ez,{scope:t,anchor:l,onAnchorChange:o,children:n})};eK.displayName=eH;var eV="PopperAnchor",eG=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:l,...o}=e,a=eW(eV,n),i=r.useRef(null),c=(0,u.s)(t,i);return r.useEffect(()=>{a.onAnchorChange(l?.current||i.current)}),l?null:(0,ek.jsx)(eN.sG.div,{...o,ref:c})});eG.displayName=eV;var eq="PopperContent",[e$,eY]=eF(eq),eX=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:a=0,align:i="center",alignOffset:c=0,arrowPadding:s=0,avoidCollisions:d=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:h="partial",hideWhenDetached:y=!1,updatePositionStrategy:g="optimized",onPlaced:w,...x}=e,R=eW(eq,n),[E,_]=r.useState(null),P=(0,u.s)(t,e=>_(e)),[S,T]=r.useState(null),M=(0,eU.X)(S),j=M?.width??0,O=M?.height??0,A="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},C=Array.isArray(f)?f:[f],N=C.length>0,k={padding:A,boundary:C.filter(e0),altBoundary:N},{refs:L,floatingStyles:D,placement:I,isPositioned:U,middlewareData:H}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:a,elements:{reference:i,floating:u}={},transform:c=!0,whileElementsMounted:s,open:d}=e,[f,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,y]=r.useState(o);ex(h,o)||y(o);let[v,m]=r.useState(null),[g,b]=r.useState(null),w=r.useCallback(e=>{e!==_.current&&(_.current=e,m(e))},[]),x=r.useCallback(e=>{e!==P.current&&(P.current=e,b(e))},[]),R=i||v,E=u||g,_=r.useRef(null),P=r.useRef(null),S=r.useRef(f),T=null!=s,M=e_(s),j=e_(a),O=e_(d),A=r.useCallback(()=>{if(!_.current||!P.current)return;let e={placement:t,strategy:n,middleware:h};j.current&&(e.platform=j.current),eb(_.current,P.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};C.current&&!ex(S.current,t)&&(S.current=t,l.flushSync(()=>{p(t)}))})},[h,t,n,j,O]);ew(()=>{!1===d&&S.current.isPositioned&&(S.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let C=r.useRef(!1);ew(()=>(C.current=!0,()=>{C.current=!1}),[]),ew(()=>{if(R&&(_.current=R),E&&(P.current=E),R&&E){if(M.current)return M.current(R,E,A);A()}},[R,E,A,M,T]);let N=r.useMemo(()=>({reference:_,floating:P,setReference:w,setFloating:x}),[w,x]),k=r.useMemo(()=>({reference:R,floating:E}),[R,E]),L=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!k.floating)return e;let t=eE(k.floating,f.x),r=eE(k.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...eR(k.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,k.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:A,refs:N,elements:k,floatingStyles:L}),[f,A,N,k,L])}({strategy:"fixed",placement:o+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let l;void 0===r&&(r={});let{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:i="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=el(e),d=o||a?[...s?et(s):[],...et(t)]:[];d.forEach(e=>{o&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,l=z(e);function o(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(i,u){void 0===i&&(i=!1),void 0===u&&(u=1),o();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(i||t(),!f||!p)return;let h=b(d),y=b(l.clientWidth-(s+f)),g={rootMargin:-h+"px "+-y+"px "+-b(l.clientHeight-(d+p))+"px "+-b(s)+"px",threshold:m(0,v(1,u))||1},w=!0;function x(t){let r=t[0].intersectionRatio;if(r!==u){if(!w)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||em(c,e.getBoundingClientRect())||a(),w=!1}try{r=new IntersectionObserver(x,{...g,root:l.ownerDocument})}catch(e){r=new IntersectionObserver(x,g)}r.observe(e)}(!0),o}(s,n):null,p=-1,h=null;i&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let y=c?eu(e):null;return c&&function t(){let r=eu(e);y&&!em(y,r)&&n(),y=r,l=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{o&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(l)}})(...e,{animationFrame:"always"===g}),elements:{reference:R.anchor},middleware:[eS({mainAxis:a+O,alignmentAxis:c}),d&&eT({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?eM():void 0,...k}),d&&ej({...k}),eO({...k,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:l,height:o}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${l}px`),a.setProperty("--radix-popper-anchor-height",`${o}px`)}}),S&&eC({element:S,padding:s}),e1({arrowWidth:j,arrowHeight:O}),y&&eA({strategy:"referenceHidden",...k})]}),[F,B]=e2(I),W=(0,eD.c)(w);(0,eI.N)(()=>{U&&W?.()},[U,W]);let K=H.arrow?.x,V=H.arrow?.y,G=H.arrow?.centerOffset!==0,[q,$]=r.useState();return(0,eI.N)(()=>{E&&$(window.getComputedStyle(E).zIndex)},[E]),(0,ek.jsx)("div",{ref:L.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:U?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:q,"--radix-popper-transform-origin":[H.transformOrigin?.x,H.transformOrigin?.y].join(" "),...H.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,ek.jsx)(e$,{scope:n,placedSide:F,onArrowChange:T,arrowX:K,arrowY:V,shouldHideArrow:G,children:(0,ek.jsx)(eN.sG.div,{"data-side":F,"data-align":B,...x,ref:P,style:{...x.style,animation:U?void 0:"none"}})})})});eX.displayName=eq;var eZ="PopperArrow",eJ={top:"bottom",right:"left",bottom:"top",left:"right"},eQ=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,l=eY(eZ,n),o=eJ[l.placedSide];return(0,ek.jsx)("span",{ref:l.onArrowChange,style:{position:"absolute",left:l.arrowX,top:l.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[l.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[l.placedSide],visibility:l.shouldHideArrow?"hidden":void 0},children:(0,ek.jsx)(eL,{...r,ref:t,style:{...r.style,display:"block"}})})});function e0(e){return null!==e}eQ.displayName=eZ;var e1=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:l}=t,o=l.arrow?.centerOffset!==0,a=o?0:e.arrowWidth,i=o?0:e.arrowHeight,[u,c]=e2(n),s={start:"0%",center:"50%",end:"100%"}[c],d=(l.arrow?.x??0)+a/2,f=(l.arrow?.y??0)+i/2,p="",h="";return"bottom"===u?(p=o?s:`${d}px`,h=`${-i}px`):"top"===u?(p=o?s:`${d}px`,h=`${r.floating.height+i}px`):"right"===u?(p=`${-i}px`,h=o?s:`${f}px`):"left"===u&&(p=`${r.floating.width+i}px`,h=o?s:`${f}px`),{data:{x:p,y:h}}}});function e2(e){let[t,n="center"]=e.split("-");return[t,n]}var e6=n(25028),e3=n(8730),e5=n(65551),e4=n(83721),e9=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,ek.jsx)(eN.sG.span,{...e,ref:t,style:{...e9,...e.style}})).displayName="VisuallyHidden";var e7=n(63376),e8=n(42247),te=[" ","Enter","ArrowUp","ArrowDown"],tt=[" ","Enter"],tn="Select",[tr,tl,to]=(0,i.N)(tn),[ta,ti]=(0,c.A)(tn,[to,eB]),tu=eB(),[tc,ts]=ta(tn),[td,tf]=ta(tn),tp=e=>{let{__scopeSelect:t,children:n,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:u,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:y,required:v,form:m}=e,g=tu(t),[b,w]=r.useState(null),[x,R]=r.useState(null),[E,_]=r.useState(!1),P=(0,s.jH)(d),[S,T]=(0,e5.i)({prop:l,defaultProp:o??!1,onChange:a,caller:tn}),[M,j]=(0,e5.i)({prop:i,defaultProp:u,onChange:c,caller:tn}),O=r.useRef(null),A=!b||m||!!b.closest("form"),[C,N]=r.useState(new Set),k=Array.from(C).map(e=>e.props.value).join(";");return(0,ek.jsx)(eK,{...g,children:(0,ek.jsxs)(tc,{required:v,scope:t,trigger:b,onTriggerChange:w,valueNode:x,onValueNodeChange:R,valueNodeHasChildren:E,onValueNodeHasChildrenChange:_,contentId:(0,h.B)(),value:M,onValueChange:j,open:S,onOpenChange:T,dir:P,triggerPointerDownPosRef:O,disabled:y,children:[(0,ek.jsx)(tr.Provider,{scope:t,children:(0,ek.jsx)(td,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{N(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{N(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),A?(0,ek.jsxs)(tQ,{"aria-hidden":!0,required:v,tabIndex:-1,name:f,autoComplete:p,value:M,onChange:e=>j(e.target.value),disabled:y,form:m,children:[void 0===M?(0,ek.jsx)("option",{value:""}):null,Array.from(C)]},k):null]})})};tp.displayName=tn;var th="SelectTrigger",ty=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:l=!1,...o}=e,i=tu(n),c=ts(th,n),s=c.disabled||l,d=(0,u.s)(t,c.onTriggerChange),f=tl(n),p=r.useRef("touch"),[h,y,v]=t1(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===c.value),r=t2(t,e,n);void 0!==r&&c.onValueChange(r.value)}),m=e=>{s||(c.onOpenChange(!0),v()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,ek.jsx)(eG,{asChild:!0,...i,children:(0,ek.jsx)(eN.sG.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":t0(c.value)?"":void 0,...o,ref:d,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&m(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(m(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||y(e.key),(!t||" "!==e.key)&&te.includes(e.key)&&(m(),e.preventDefault())})})})});ty.displayName=th;var tv="SelectValue",tm=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:l,children:o,placeholder:a="",...i}=e,c=ts(tv,n),{onValueNodeHasChildrenChange:s}=c,d=void 0!==o,f=(0,u.s)(t,c.onValueNodeChange);return(0,eI.N)(()=>{s(d)},[s,d]),(0,ek.jsx)(eN.sG.span,{...i,ref:f,style:{pointerEvents:"none"},children:t0(c.value)?(0,ek.jsx)(ek.Fragment,{children:a}):o})});tm.displayName=tv;var tg=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...l}=e;return(0,ek.jsx)(eN.sG.span,{"aria-hidden":!0,...l,ref:t,children:r||"▼"})});tg.displayName="SelectIcon";var tb=e=>(0,ek.jsx)(e6.Z,{asChild:!0,...e});tb.displayName="SelectPortal";var tw="SelectContent",tx=r.forwardRef((e,t)=>{let n=ts(tw,e.__scopeSelect),[o,a]=r.useState();return((0,eI.N)(()=>{a(new DocumentFragment)},[]),n.open)?(0,ek.jsx)(tP,{...e,ref:t}):o?l.createPortal((0,ek.jsx)(tR,{scope:e.__scopeSelect,children:(0,ek.jsx)(tr.Slot,{scope:e.__scopeSelect,children:(0,ek.jsx)("div",{children:e.children})})}),o):null});tx.displayName=tw;var[tR,tE]=ta(tw),t_=(0,e3.TL)("SelectContent.RemoveScroll"),tP=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:c,side:s,sideOffset:h,align:y,alignOffset:v,arrowPadding:m,collisionBoundary:g,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:R,...E}=e,_=ts(tw,n),[P,S]=r.useState(null),[T,M]=r.useState(null),j=(0,u.s)(t,e=>S(e)),[O,A]=r.useState(null),[C,N]=r.useState(null),k=tl(n),[L,D]=r.useState(!1),I=r.useRef(!1);r.useEffect(()=>{if(P)return(0,e7.Eq)(P)},[P]),(0,f.Oh)();let U=r.useCallback(e=>{let[t,...n]=k().map(e=>e.ref.current),[r]=n.slice(-1),l=document.activeElement;for(let n of e)if(n===l||(n?.scrollIntoView({block:"nearest"}),n===t&&T&&(T.scrollTop=0),n===r&&T&&(T.scrollTop=T.scrollHeight),n?.focus(),document.activeElement!==l))return},[k,T]),H=r.useCallback(()=>U([O,P]),[U,O,P]);r.useEffect(()=>{L&&H()},[L,H]);let{onOpenChange:F,triggerPointerDownPosRef:B}=_;r.useEffect(()=>{if(P){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(B.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(B.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():P.contains(n.target)||F(!1),document.removeEventListener("pointermove",t),B.current=null};return null!==B.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[P,F,B]),r.useEffect(()=>{let e=()=>F(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[F]);let[z,W]=t1(e=>{let t=k().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=t2(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),K=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==_.value&&_.value===t||r)&&(A(e),r&&(I.current=!0))},[_.value]),V=r.useCallback(()=>P?.focus(),[P]),G=r.useCallback((e,t,n)=>{let r=!I.current&&!n;(void 0!==_.value&&_.value===t||r)&&N(e)},[_.value]),q="popper"===l?tT:tS,$=q===tT?{side:s,sideOffset:h,align:y,alignOffset:v,arrowPadding:m,collisionBoundary:g,collisionPadding:b,sticky:w,hideWhenDetached:x,avoidCollisions:R}:{};return(0,ek.jsx)(tR,{scope:n,content:P,viewport:T,onViewportChange:M,itemRefCallback:K,selectedItem:O,onItemLeave:V,itemTextRefCallback:G,focusSelectedItem:H,selectedItemText:C,position:l,isPositioned:L,searchRef:z,children:(0,ek.jsx)(e8.A,{as:t_,allowPinchZoom:!0,children:(0,ek.jsx)(p.n,{asChild:!0,trapped:_.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{_.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,ek.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>_.onOpenChange(!1),children:(0,ek.jsx)(q,{role:"listbox",id:_.contentId,"data-state":_.open?"open":"closed",dir:_.dir,onContextMenu:e=>e.preventDefault(),...E,...$,onPlaced:()=>D(!0),ref:j,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:(0,a.m)(E.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||W(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=k().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>U(t)),e.preventDefault()}})})})})})})});tP.displayName="SelectContentImpl";var tS=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:l,...a}=e,i=ts(tw,n),c=tE(tw,n),[s,d]=r.useState(null),[f,p]=r.useState(null),h=(0,u.s)(t,e=>p(e)),y=tl(n),v=r.useRef(!1),m=r.useRef(!0),{viewport:g,selectedItem:b,selectedItemText:w,focusSelectedItem:x}=c,R=r.useCallback(()=>{if(i.trigger&&i.valueNode&&s&&f&&g&&b&&w){let e=i.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),r=w.getBoundingClientRect();if("rtl"!==i.dir){let l=r.left-t.left,a=n.left-l,i=e.left-a,u=e.width+i,c=Math.max(u,t.width),d=o(a,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.left=d+"px"}else{let l=t.right-r.right,a=window.innerWidth-n.right-l,i=window.innerWidth-e.right-a,u=e.width+i,c=Math.max(u,t.width),d=o(a,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.right=d+"px"}let a=y(),u=window.innerHeight-20,c=g.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),m=parseInt(d.borderBottomWidth,10),x=p+h+c+parseInt(d.paddingBottom,10)+m,R=Math.min(5*b.offsetHeight,x),E=window.getComputedStyle(g),_=parseInt(E.paddingTop,10),P=parseInt(E.paddingBottom,10),S=e.top+e.height/2-10,T=b.offsetHeight/2,M=p+h+(b.offsetTop+T);if(M<=S){let e=a.length>0&&b===a[a.length-1].ref.current;s.style.bottom="0px";let t=Math.max(u-S,T+(e?P:0)+(f.clientHeight-g.offsetTop-g.offsetHeight)+m);s.style.height=M+t+"px"}else{let e=a.length>0&&b===a[0].ref.current;s.style.top="0px";let t=Math.max(S,p+g.offsetTop+(e?_:0)+T);s.style.height=t+(x-M)+"px",g.scrollTop=M-S+g.offsetTop}s.style.margin="10px 0",s.style.minHeight=R+"px",s.style.maxHeight=u+"px",l?.(),requestAnimationFrame(()=>v.current=!0)}},[y,i.trigger,i.valueNode,s,f,g,b,w,i.dir,l]);(0,eI.N)(()=>R(),[R]);let[E,_]=r.useState();(0,eI.N)(()=>{f&&_(window.getComputedStyle(f).zIndex)},[f]);let P=r.useCallback(e=>{e&&!0===m.current&&(R(),x?.(),m.current=!1)},[R,x]);return(0,ek.jsx)(tM,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:v,onScrollButtonChange:P,children:(0,ek.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,ek.jsx)(eN.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});tS.displayName="SelectItemAlignedPosition";var tT=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:l=10,...o}=e,a=tu(n);return(0,ek.jsx)(eX,{...a,...o,ref:t,align:r,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tT.displayName="SelectPopperPosition";var[tM,tj]=ta(tw,{}),tO="SelectViewport",tA=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:l,...o}=e,i=tE(tO,n),c=tj(tO,n),s=(0,u.s)(t,i.onViewportChange),d=r.useRef(0);return(0,ek.jsxs)(ek.Fragment,{children:[(0,ek.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,ek.jsx)(tr.Slot,{scope:n,children:(0,ek.jsx)(eN.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=c;if(r?.current&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,l=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(l<r){let o=l+e,a=Math.min(r,o),i=o-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=i>0?i:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tA.displayName=tO;var tC="SelectGroup",[tN,tk]=ta(tC),tL=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=(0,h.B)();return(0,ek.jsx)(tN,{scope:n,id:l,children:(0,ek.jsx)(eN.sG.div,{role:"group","aria-labelledby":l,...r,ref:t})})});tL.displayName=tC;var tD="SelectLabel",tI=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=tk(tD,n);return(0,ek.jsx)(eN.sG.div,{id:l.id,...r,ref:t})});tI.displayName=tD;var tU="SelectItem",[tH,tF]=ta(tU),tB=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:l,disabled:o=!1,textValue:i,...c}=e,s=ts(tU,n),d=tE(tU,n),f=s.value===l,[p,y]=r.useState(i??""),[v,m]=r.useState(!1),g=(0,u.s)(t,e=>d.itemRefCallback?.(e,l,o)),b=(0,h.B)(),w=r.useRef("touch"),x=()=>{o||(s.onValueChange(l),s.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,ek.jsx)(tH,{scope:n,value:l,disabled:o,textId:b,isSelected:f,onItemTextChange:r.useCallback(e=>{y(t=>t||(e?.textContent??"").trim())},[]),children:(0,ek.jsx)(tr.ItemSlot,{scope:n,value:l,disabled:o,textValue:p,children:(0,ek.jsx)(eN.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...c,ref:g,onFocus:(0,a.m)(c.onFocus,()=>m(!0)),onBlur:(0,a.m)(c.onBlur,()=>m(!1)),onClick:(0,a.m)(c.onClick,()=>{"mouse"!==w.current&&x()}),onPointerUp:(0,a.m)(c.onPointerUp,()=>{"mouse"===w.current&&x()}),onPointerDown:(0,a.m)(c.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,a.m)(c.onPointerMove,e=>{w.current=e.pointerType,o?d.onItemLeave?.():"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(c.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,a.m)(c.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(tt.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});tB.displayName=tU;var tz="SelectItemText",tW=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:o,style:a,...i}=e,c=ts(tz,n),s=tE(tz,n),d=tF(tz,n),f=tf(tz,n),[p,h]=r.useState(null),y=(0,u.s)(t,e=>h(e),d.onItemTextChange,e=>s.itemTextRefCallback?.(e,d.value,d.disabled)),v=p?.textContent,m=r.useMemo(()=>(0,ek.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:g,onNativeOptionRemove:b}=f;return(0,eI.N)(()=>(g(m),()=>b(m)),[g,b,m]),(0,ek.jsxs)(ek.Fragment,{children:[(0,ek.jsx)(eN.sG.span,{id:d.textId,...i,ref:y}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?l.createPortal(i.children,c.valueNode):null]})});tW.displayName=tz;var tK="SelectItemIndicator",tV=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return tF(tK,n).isSelected?(0,ek.jsx)(eN.sG.span,{"aria-hidden":!0,...r,ref:t}):null});tV.displayName=tK;var tG="SelectScrollUpButton",tq=r.forwardRef((e,t)=>{let n=tE(tG,e.__scopeSelect),l=tj(tG,e.__scopeSelect),[o,a]=r.useState(!1),i=(0,u.s)(t,l.onScrollButtonChange);return(0,eI.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){a(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,ek.jsx)(tX,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});tq.displayName=tG;var t$="SelectScrollDownButton",tY=r.forwardRef((e,t)=>{let n=tE(t$,e.__scopeSelect),l=tj(t$,e.__scopeSelect),[o,a]=r.useState(!1),i=(0,u.s)(t,l.onScrollButtonChange);return(0,eI.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,ek.jsx)(tX,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});tY.displayName=t$;var tX=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:l,...o}=e,i=tE("SelectScrollButton",n),u=r.useRef(null),c=tl(n),s=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>s(),[s]),(0,eI.N)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,ek.jsx)(eN.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{i.onItemLeave?.(),null===u.current&&(u.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{s()})})}),tZ=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,ek.jsx)(eN.sG.div,{"aria-hidden":!0,...r,ref:t})});tZ.displayName="SelectSeparator";var tJ="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=tu(n),o=ts(tJ,n),a=tE(tJ,n);return o.open&&"popper"===a.position?(0,ek.jsx)(eQ,{...l,...r,ref:t}):null}).displayName=tJ;var tQ=r.forwardRef(({__scopeSelect:e,value:t,...n},l)=>{let o=r.useRef(null),a=(0,u.s)(l,o),i=(0,e4.Z)(t);return r.useEffect(()=>{let e=o.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[i,t]),(0,ek.jsx)(eN.sG.select,{...n,style:{...e9,...n.style},ref:a,defaultValue:t})});function t0(e){return""===e||void 0===e}function t1(e){let t=(0,eD.c)(e),n=r.useRef(""),l=r.useRef(0),o=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),a=r.useCallback(()=>{n.current="",window.clearTimeout(l.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(l.current),[]),[n,o,a]}function t2(e,t,n){var r,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,i=(r=e,l=Math.max(a,0),r.map((e,t)=>r[(l+t)%r.length]));1===o.length&&(i=i.filter(e=>e!==n));let u=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}tQ.displayName="SelectBubbleInput";var t6=tp,t3=ty,t5=tm,t4=tg,t9=tb,t7=tx,t8=tA,ne=tL,nt=tI,nn=tB,nr=tW,nl=tV,no=tq,na=tY,ni=tZ},42247:(e,t,n)=>{n.d(t,{A:()=>G});var r,l,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var l in t=arguments[n])Object.prototype.hasOwnProperty.call(t,l)&&(e[l]=t[l]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(n[r[l]]=e[r[l]]);return n}Object.create;Object.create;var i=("function"==typeof SuppressedError&&SuppressedError,n(43210)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,l,a=(t=null,void 0===n&&(n=p),r=[],l=!1,{read:function(){if(l)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,l);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(l=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){l=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=o({async:!0,ssr:!1},e),a}(),y=function(){},v=i.forwardRef(function(e,t){var n,r,l,u,c=i.useRef(null),p=i.useState({onScrollCapture:y,onWheelCapture:y,onTouchMoveCapture:y}),v=p[0],m=p[1],g=e.forwardProps,b=e.children,w=e.className,x=e.removeScrollBar,R=e.enabled,E=e.shards,_=e.sideCar,P=e.noRelative,S=e.noIsolation,T=e.inert,M=e.allowPinchZoom,j=e.as,O=e.gapMode,A=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(l=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return l.value},set current(value){var e=l.value;e!==value&&(l.value=value,l.callback(value,e))}}}})[0]).callback=r,u=l.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),l=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,l)})}f.set(u,n)},[n]),u),N=o(o({},A),v);return i.createElement(i.Fragment,null,R&&i.createElement(_,{sideCar:h,removeScrollBar:x,shards:E,noRelative:P,noIsolation:S,inert:T,setCallbacks:m,allowPinchZoom:!!M,lockRef:c,gapMode:O}),g?i.cloneElement(i.Children.only(b),o(o({},N),{ref:C})):i.createElement(void 0===j?"div":j,o({},N,{className:w,ref:C}),b))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var m=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,o({},n))};m.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=l||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=g();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},R=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],l=t["padding"===e?"paddingRight":"marginRight"];return[R(n),R(r),R(l)]},_=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},P=w(),S="data-scroll-locked",T=function(e,t,n,r){var l=e.left,o=e.top,a=e.right,i=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(i,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(l,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},M=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},j=function(){i.useEffect(function(){return document.body.setAttribute(S,(M()+1).toString()),function(){var e=M()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},O=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,l=void 0===r?"margin":r;j();var o=i.useMemo(function(){return _(l)},[l]);return i.createElement(P,{styles:T(o,!t,l,n?"":"!important")})},A=!1;if("undefined"!=typeof window)try{var C=Object.defineProperty({},"passive",{get:function(){return A=!0,!0}});window.addEventListener("test",C,C),window.removeEventListener("test",C,C)}catch(e){A=!1}var N=!!A&&{passive:!1},k=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},L=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),D(e,r)){var l=I(e,r);if(l[1]>l[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},D=function(e,t){return"v"===e?k(t,"overflowY"):k(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},U=function(e,t,n,r,l){var o,a=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),i=a*r,u=n.target,c=t.contains(u),s=!1,d=i>0,f=0,p=0;do{if(!u)break;var h=I(e,u),y=h[0],v=h[1]-h[2]-a*y;(y||v)&&D(e,u)&&(f+=v,p+=y);var m=u.parentNode;u=m&&m.nodeType===Node.DOCUMENT_FRAGMENT_NODE?m.host:m}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(l&&1>Math.abs(f)||!l&&i>f)?s=!0:!d&&(l&&1>Math.abs(p)||!l&&-i>p)&&(s=!0),s},H=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},F=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},z=0,W=[];let K=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),l=i.useState(z++)[0],o=i.useState(w)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(l));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,l=0,o=t.length;l<o;l++)!r&&l in t||(r||(r=Array.prototype.slice.call(t,0,l)),r[l]=t[l]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(l))}),function(){document.body.classList.remove("block-interactivity-".concat(l)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(l))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var l,o=H(e),i=n.current,u="deltaX"in e?e.deltaX:i[0]-o[0],c="deltaY"in e?e.deltaY:i[1]-o[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=L(d,s);if(!f)return!0;if(f?l=d:(l="v"===d?"h":"v",f=L(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=l),!l)return!0;var p=r.current||l;return U(p,t,e,"h"===p?u:c,!0)},[]),c=i.useCallback(function(e){if(W.length&&W[W.length-1]===o){var n="deltaY"in e?F(e):H(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var l=(a.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(l.length>0?u(e,l[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,l){var o={name:e,delta:n,target:r,should:l,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=i.useCallback(function(e){n.current=H(e),r.current=void 0},[]),f=i.useCallback(function(t){s(t.type,F(t),t.target,u(t,e.lockRef.current))},[]),p=i.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return W.push(o),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,N),document.addEventListener("touchmove",c,N),document.addEventListener("touchstart",d,N),function(){W=W.filter(function(e){return e!==o}),document.removeEventListener("wheel",c,N),document.removeEventListener("touchmove",c,N),document.removeEventListener("touchstart",d,N)}},[]);var h=e.removeScrollBar,y=e.inert;return i.createElement(i.Fragment,null,y?i.createElement(o,{styles:"\n  .block-interactivity-".concat(l," {pointer-events: none;}\n  .allow-interactivity-").concat(l," {pointer-events: all;}\n")}):null,h?i.createElement(O,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),m);var V=i.forwardRef(function(e,t){return i.createElement(v,o({},e,{ref:t,sideCar:K}))});V.classNames=v.classNames;let G=V},44397:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return l}});let r=n(33123);function l(e,t){return function e(t,n,l){if(0===Object.keys(n).length)return[t,l];let o=Object.keys(n).filter(e=>"children"!==e);for(let a of("children"in n&&o.unshift("children"),o)){let[o,i]=n[a],u=t.parallelRoutes.get(a);if(!u)continue;let c=(0,r.createRouterCacheKey)(o),s=u.get(c);if(!s)continue;let d=e(s,i,l+"/"+c);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44709:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},46059:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(43210),l=n(98599),o=n(66156),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[l,a]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=i(u.current);s.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,l=i(t);e?f("MOUNT"):"none"===l||t?.display==="none"?f("UNMOUNT"):n&&r!==l?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,o.N)(()=>{if(l){let e,t=l.ownerDocument.defaultView??window,n=n=>{let r=i(u.current).includes(n.animationName);if(n.target===l&&r&&(f("ANIMATION_END"),!c.current)){let n=l.style.animationFillMode;l.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===l.style.animationFillMode&&(l.style.animationFillMode=n)})}},r=e=>{e.target===l&&(s.current=i(u.current))};return l.addEventListener("animationstart",r),l.addEventListener("animationcancel",n),l.addEventListener("animationend",n),()=>{t.clearTimeout(e),l.removeEventListener("animationstart",r),l.removeEventListener("animationcancel",n),l.removeEventListener("animationend",n)}}f("ANIMATION_END")},[l,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),c=(0,l.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:c}):null};function i(e){return e?.animationName||"none"}a.displayName="Presence"},47033:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48730:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49625:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},50593:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return f},cancelPrefetchTask:function(){return u},createCacheKey:function(){return s},getCurrentCacheVersion:function(){return a},navigate:function(){return l},prefetch:function(){return r},reschedulePrefetchTask:function(){return c},revalidateEntireCache:function(){return o},schedulePrefetchTask:function(){return i}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,l=n,o=n,a=n,i=n,u=n,c=n,s=n;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),f=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51550:(e,t,n)=>{function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},53332:(e,t,n)=>{var r=n(43210),l="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useState,a=r.useEffect,i=r.useLayoutEffect,u=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!l(e,n)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),l=r[0].inst,s=r[1];return i(function(){l.value=n,l.getSnapshot=t,c(l)&&s({inst:l})},[e,n,t]),a(function(){return c(l)&&s({inst:l}),e(function(){c(l)&&s({inst:l})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:s},53411:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},54674:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return o}});let r=n(84949),l=n(19169),o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:o}=(0,l.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+o};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56928:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return o}});let r=n(41500),l=n(33898);function o(e,t,n,o,a){let{tree:i,seedData:u,head:c,isRootRender:s}=o;if(null===u)return!1;if(s){let l=u[1];n.loading=u[3],n.rsc=l,n.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(e,n,t,i,u,c,a)}else n.rsc=t.rsc,n.prefetchRsc=t.prefetchRsc,n.parallelRoutes=new Map(t.parallelRoutes),n.loading=t.loading,(0,l.fillCacheWithNewSubTreeData)(e,n,t,o,a);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57379:(e,t,n)=>{e.exports=n(53332)},57800:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},58887:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},59435:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return o}});let r=n(70642);function l(e){return void 0!==e}function o(e,t){var n,o;let a=null==(n=t.shouldScroll)||n,i=e.nextUrl;if(l(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?i=n:i||(i=e.canonicalUrl)}return{canonicalUrl:l(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:l(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:l(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:l(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!a&&(!!l(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:a?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:a?null!=(o=null==t?void 0:t.scrollableSegments)?o:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:l(t.patchedTree)?t.patchedTree:e.tree,nextUrl:i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,n)=>{n.r(t),n.d(t,{_:()=>l});var r=0;function l(e){return"__private_"+r+++"_"+e}},61794:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let r=n(79289),l=n(26736);function o(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,l.hasBasePath)(n.pathname)}catch(e){return!1}}},63376:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},l=new WeakMap,o=new WeakMap,a={},i=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};c.forEach(h);var y=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))y(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,i=(l.get(e)||0)+1,u=(s.get(e)||0)+1;l.set(e,i),s.set(e,u),d.push(e),1===i&&a&&o.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return y(t),f.clear(),i++,function(){d.forEach(function(e){var t=l.get(e)-1,a=s.get(e)-1;l.set(e,t),s.set(e,a),t||(o.has(e)||e.removeAttribute(r),o.delete(e)),a||e.removeAttribute(n)}),--i||(l=new WeakMap,l=new WeakMap,o=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var l=Array.from(Array.isArray(e)?e:[e]),o=t||r(e);return o?(l.push.apply(l,Array.from(o.querySelectorAll("[aria-live], script"))),c(l,o,n,"aria-hidden")):function(){return null}}},63690:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return h},dispatchNavigateAction:function(){return m},dispatchTraverseAction:function(){return g},getCurrentAppRouterState:function(){return y},publicAppRouterInstance:function(){return b}});let r=n(59154),l=n(8830),o=n(43210),a=n(91992);n(50593);let i=n(19129),u=n(96127),c=n(89752),s=n(75076),d=n(73406);function f(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:n,setState:r}=e,l=t.state;t.pending=n;let o=n.payload,i=t.action(l,o);function u(e){n.discarded||(t.state=e,f(t,r),n.resolve(e))}(0,a.isThenable)(i)?i.then(u,e=>{f(t,r),n.reject(e)}):u(i)}function h(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let l={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{l={resolve:e,reject:t}});(0,o.startTransition)(()=>{n(e)})}let a={payload:t,next:null,resolve:l.resolve,reject:l.reject};null===e.pending?(e.last=a,p({actionQueue:e,action:a,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,a.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:a,setState:n})):(null!==e.last&&(e.last.next=a),e.last=a)})(n,e,t),action:async(e,t)=>(0,l.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return n}function y(){return null}function v(){return null}function m(e,t,n,l){let o=new URL((0,u.addBasePath)(e),location.href);(0,d.setLinkForCurrentNavigation)(l);(0,i.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:o,isExternalUrl:(0,c.isExternalURL)(o),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function g(e,t){(0,i.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),l=(0,c.createPrefetchURL)(e);if(null!==l){var o;(0,s.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:l,kind:null!=(o=null==t?void 0:t.kind)?o:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,o.startTransition)(()=>{var n;m(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,o.startTransition)(()=>{var n;m(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,o.startTransition)(()=>{(0,i.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65551:(e,t,n)=>{n.d(t,{i:()=>i});var r,l=n(43210),o=n(66156),a=(r||(r=n.t(l,2)))[" useInsertionEffect ".trim().toString()]||o.N;function i({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,u]=function({defaultProp:e,onChange:t}){let[n,r]=l.useState(e),o=l.useRef(n),i=l.useRef(t);return a(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,l.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else i(t)},[c,e,i,u])]}Symbol("RADIX:SYNC_STATE")},65951:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[o,a]=n,[i,u]=t;return(0,l.matchSegment)(i,o)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),a[u]):!!Array.isArray(i)}}});let r=n(74007),l=n(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return h},listenForDynamicRequest:function(){return p},startPPRNavigation:function(){return c},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],l=t.parallelRoutes,a=new Map(l);for(let t in r){let n=r[t],i=n[0],u=(0,o.createRouterCacheKey)(i),c=l.get(t);if(void 0!==c){let r=c.get(u);if(void 0!==r){let l=e(r,n),o=new Map(c);o.set(u,l),a.set(t,o)}}}let i=t.rsc,u=m(i)&&"pending"===i.status;return{lazyData:null,rsc:i,head:t.head,prefetchHead:u?t.prefetchHead:[null,null],prefetchRsc:u?t.prefetchRsc:null,loading:t.loading,parallelRoutes:a,navigatedAt:t.navigatedAt}}}});let r=n(83913),l=n(14077),o=n(33123),a=n(2030),i=n(5334),u={route:null,node:null,dynamicRequestTree:null,children:null};function c(e,t,n,a,i,c,f,p,h){return function e(t,n,a,i,c,f,p,h,y,v,m){let g=a[1],b=i[1],w=null!==f?f[2]:null;c||!0===i[4]&&(c=!0);let x=n.parallelRoutes,R=new Map(x),E={},_=null,P=!1,S={};for(let n in b){let a,i=b[n],d=g[n],f=x.get(n),T=null!==w?w[n]:null,M=i[0],j=v.concat([n,M]),O=(0,o.createRouterCacheKey)(M),A=void 0!==d?d[0]:void 0,C=void 0!==f?f.get(O):void 0;if(null!==(a=M===r.DEFAULT_SEGMENT_KEY?void 0!==d?{route:d,node:null,dynamicRequestTree:null,children:null}:s(t,d,i,C,c,void 0!==T?T:null,p,h,j,m):y&&0===Object.keys(i[1]).length?s(t,d,i,C,c,void 0!==T?T:null,p,h,j,m):void 0!==d&&void 0!==A&&(0,l.matchSegment)(M,A)&&void 0!==C&&void 0!==d?e(t,C,d,i,c,T,p,h,y,j,m):s(t,d,i,C,c,void 0!==T?T:null,p,h,j,m))){if(null===a.route)return u;null===_&&(_=new Map),_.set(n,a);let e=a.node;if(null!==e){let t=new Map(f);t.set(O,e),R.set(n,t)}let t=a.route;E[n]=t;let r=a.dynamicRequestTree;null!==r?(P=!0,S[n]=r):S[n]=t}else E[n]=i,S[n]=i}if(null===_)return null;let T={lazyData:null,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,loading:n.loading,parallelRoutes:R,navigatedAt:t};return{route:d(i,E),node:T,dynamicRequestTree:P?d(i,S):null,children:_}}(e,t,n,a,!1,i,c,f,p,[],h)}function s(e,t,n,r,l,c,s,p,h,y){return!l&&(void 0===t||(0,a.isNavigatingToNewRootLayout)(t,n))?u:function e(t,n,r,l,a,u,c,s){let p,h,y,v,m=n[1],g=0===Object.keys(m).length;if(void 0!==r&&r.navigatedAt+i.DYNAMIC_STALETIME_MS>t)p=r.rsc,h=r.loading,y=r.head,v=r.navigatedAt;else if(null===l)return f(t,n,null,a,u,c,s);else if(p=l[1],h=l[3],y=g?a:null,v=t,l[4]||u&&g)return f(t,n,l,a,u,c,s);let b=null!==l?l[2]:null,w=new Map,x=void 0!==r?r.parallelRoutes:null,R=new Map(x),E={},_=!1;if(g)s.push(c);else for(let n in m){let r=m[n],l=null!==b?b[n]:null,i=null!==x?x.get(n):void 0,d=r[0],f=c.concat([n,d]),p=(0,o.createRouterCacheKey)(d),h=e(t,r,void 0!==i?i.get(p):void 0,l,a,u,f,s);w.set(n,h);let y=h.dynamicRequestTree;null!==y?(_=!0,E[n]=y):E[n]=r;let v=h.node;if(null!==v){let e=new Map;e.set(p,v),R.set(n,e)}}return{route:n,node:{lazyData:null,rsc:p,prefetchRsc:null,head:y,prefetchHead:null,loading:h,parallelRoutes:R,navigatedAt:v},dynamicRequestTree:_?d(n,E):null,children:w}}(e,n,r,c,s,p,h,y)}function d(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function f(e,t,n,r,l,a,i){let u=d(t,t[1]);return u[3]="refetch",{route:t,node:function e(t,n,r,l,a,i,u){let c=n[1],s=null!==r?r[2]:null,d=new Map;for(let n in c){let r=c[n],f=null!==s?s[n]:null,p=r[0],h=i.concat([n,p]),y=(0,o.createRouterCacheKey)(p),v=e(t,r,void 0===f?null:f,l,a,h,u),m=new Map;m.set(y,v),d.set(n,m)}let f=0===d.size;f&&u.push(i);let p=null!==r?r[1]:null,h=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:d,prefetchRsc:void 0!==p?p:null,prefetchHead:f?l:[null,null],loading:void 0!==h?h:null,rsc:g(),head:f?g():null,navigatedAt:t}}(e,t,n,r,l,a,i),dynamicRequestTree:u,children:null}}function p(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:a,head:i}=t;a&&function(e,t,n,r,a){let i=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],o=i.children;if(null!==o){let e=o.get(n);if(void 0!==e){let t=e.route[0];if((0,l.matchSegment)(r,t)){i=e;continue}}}return}!function e(t,n,r,a){if(null===t.dynamicRequestTree)return;let i=t.children,u=t.node;if(null===i){null!==u&&(function e(t,n,r,a,i){let u=n[1],c=r[1],s=a[2],d=t.parallelRoutes;for(let t in u){let n=u[t],r=c[t],a=s[t],f=d.get(t),p=n[0],h=(0,o.createRouterCacheKey)(p),v=void 0!==f?f.get(h):void 0;void 0!==v&&(void 0!==r&&(0,l.matchSegment)(p,r[0])&&null!=a?e(v,n,r,a,i):y(n,v,null))}let f=t.rsc,p=a[1];null===f?t.rsc=p:m(f)&&f.resolve(p);let h=t.head;m(h)&&h.resolve(i)}(u,t.route,n,r,a),t.dynamicRequestTree=null);return}let c=n[1],s=r[2];for(let t in n){let n=c[t],r=s[t],o=i.get(t);if(void 0!==o){let t=o.route[0];if((0,l.matchSegment)(n[0],t)&&null!=r)return e(o,n,r,a)}}}(i,n,r,a)}(e,n,r,a,i)}h(e,null)}},t=>{h(e,t)})}function h(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)y(e.route,n,t);else for(let e of r.values())h(e,t);e.dynamicRequestTree=null}function y(e,t,n){let r=e[1],l=t.parallelRoutes;for(let e in r){let t=r[e],a=l.get(e);if(void 0===a)continue;let i=t[0],u=(0,o.createRouterCacheKey)(i),c=a.get(u);void 0!==c&&y(t,c,n)}let a=t.rsc;m(a)&&(null===n?a.resolve(null):a.reject(n));let i=t.head;m(i)&&i.resolve(null)}let v=Symbol();function m(e){return e&&e.tag===v}function g(){let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=v,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66156:(e,t,n)=>{n.d(t,{N:()=>l});var r=n(43210),l=globalThis?.document?r.useLayoutEffect:()=>{}},70569:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},70642:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return s},extractPathFromFlightRouterState:function(){return c},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],o=Array.isArray(t),a=o?t[1]:t;!a||a.startsWith(l.PAGE_SEGMENT_KEY)||(o&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):o&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(72859),l=n(83913),o=n(14077),a=e=>"/"===e[0]?e.slice(1):e,i=e=>"string"==typeof e?"children"===e?"":e:e[1];function u(e){return e.reduce((e,t)=>""===(t=a(t))||(0,l.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function c(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===l.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(l.PAGE_SEGMENT_KEY))return"";let o=[i(n)],a=null!=(t=e[1])?t:{},s=a.children?c(a.children):void 0;if(void 0!==s)o.push(s);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let n=c(t);void 0!==n&&o.push(n)}return u(o)}function s(e,t){let n=function e(t,n){let[l,a]=t,[u,s]=n,d=i(l),f=i(u);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,o.matchSegment)(l,u)){var p;return null!=(p=c(n))?p:""}for(let t in a)if(s[t]){let n=e(a[t],s[t]);if(null!==n)return i(u)+"/"+n}return null}(e,t);return null==n||"/"===n?n:u(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73406:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return c},PENDING_LINK_STATUS:function(){return u},mountFormInstance:function(){return g},mountLinkInstance:function(){return m},onLinkVisibilityChanged:function(){return w},onNavigationIntent:function(){return x},pingVisibleLinks:function(){return E},setLinkForCurrentNavigation:function(){return s},unmountLinkForCurrentNavigation:function(){return d},unmountPrefetchableInstance:function(){return b}}),n(63690);let r=n(89752),l=n(59154),o=n(50593),a=n(43210),i=null,u={pending:!0},c={pending:!1};function s(e){(0,a.startTransition)(()=>{null==i||i.setOptimisticLinkStatus(c),null==e||e.setOptimisticLinkStatus(u),i=e})}function d(e){i===e&&(i=null)}let f="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,h="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;w(t.target,e)}},{rootMargin:"200px"}):null;function y(e,t){void 0!==f.get(e)&&b(e),f.set(e,t),null!==h&&h.observe(e)}function v(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function m(e,t,n,r,l,o){if(l){let l=v(t);if(null!==l){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:l.href,setOptimisticLinkStatus:o};return y(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:o}}function g(e,t,n,r){let l=v(t);null!==l&&y(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:l.href,setOptimisticLinkStatus:null})}function b(e){let t=f.get(e);if(void 0!==t){f.delete(e),p.delete(t);let n=t.prefetchTask;null!==n&&(0,o.cancelPrefetchTask)(n)}null!==h&&h.unobserve(e)}function w(e,t){let n=f.get(e);void 0!==n&&(n.isVisible=t,t?p.add(n):p.delete(n),R(n))}function x(e,t){let n=f.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,R(n))}function R(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,o.cancelPrefetchTask)(t);return}}function E(e,t){let n=(0,o.getCurrentCacheVersion)();for(let r of p){let a=r.prefetchTask;if(null!==a&&r.cacheVersion===n&&a.key.nextUrl===e&&a.treeAtTimeOfPrefetch===t)continue;null!==a&&(0,o.cancelPrefetchTask)(a);let i=(0,o.createCacheKey)(r.prefetchHref,e),u=r.wasHoveredOrTouched?o.PrefetchPriority.Intent:o.PrefetchPriority.Default;r.prefetchTask=(0,o.schedulePrefetchTask)(i,t,r.kind===l.PrefetchKind.FULL,u),r.cacheVersion=(0,o.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75076:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return o},prefetchReducer:function(){return a}});let r=n(5144),l=n(5334),o=new r.PromiseQueue(5),a=function(e,t){(0,l.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,l.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76715:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function l(e){let t=new URLSearchParams;for(let[n,l]of Object.entries(e))if(Array.isArray(l))for(let e of l)t.append(n,r(e));else t.set(n,r(l));return t}function o(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return l}})},77022:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let r=n(43210),l=n(51215),o="next-route-announcer";function a(e){let{tree:t}=e,[n,a]=(0,r.useState)(null);(0,r.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(o)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(o);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(o)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[i,u]=(0,r.useState)(""),c=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==c.current&&c.current!==e&&u(e),c.current=e},[t]),n?(0,l.createPortal)(i,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78122:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78272:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},78866:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let r=n(59008),l=n(57391),o=n(86770),a=n(2030),i=n(25232),u=n(59435),c=n(41500),s=n(89752),d=n(96493),f=n(68214),p=n(22308);function h(e,t){let{origin:n}=t,h={},y=e.canonicalUrl,v=e.tree;h.preserveCustomHistoryState=!1;let m=(0,s.createEmptyCacheNode)(),g=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);m.lazyData=(0,r.fetchServerResponse)(new URL(y,n),{flightRouterState:[v[0],v[1],v[2],"refetch"],nextUrl:g?e.nextUrl:null});let b=Date.now();return m.lazyData.then(async n=>{let{flightData:r,canonicalUrl:s}=n;if("string"==typeof r)return(0,i.handleExternalUrl)(e,h,r,e.pushRef.pendingPush);for(let n of(m.lazyData=null,r)){let{tree:r,seedData:u,head:f,isRootRender:w}=n;if(!w)return console.log("REFRESH FAILED"),e;let x=(0,o.applyRouterStatePatchToTree)([""],v,r,e.canonicalUrl);if(null===x)return(0,d.handleSegmentMismatch)(e,t,r);if((0,a.isNavigatingToNewRootLayout)(v,x))return(0,i.handleExternalUrl)(e,h,y,e.pushRef.pendingPush);let R=s?(0,l.createHrefFromUrl)(s):void 0;if(s&&(h.canonicalUrl=R),null!==u){let e=u[1],t=u[3];m.rsc=e,m.prefetchRsc=null,m.loading=t,(0,c.fillLazyItemsTillLeafWithHead)(b,m,void 0,r,u,f,void 0),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:x,updatedCache:m,includeNextUrl:g,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=m,h.patchedTree=x,v=x}return(0,u.handleMutable)(e,h)},()=>e)}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return g},MissingStaticPage:function(){return m},NormalizeError:function(){return y},PageNotFoundError:function(){return v},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return u},getLocationOrigin:function(){return a},getURL:function(){return i},isAbsoluteUrl:function(){return o},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,l=Array(r),o=0;o<r;o++)l[o]=arguments[o];return n||(n=!0,t=e(...l)),t}}let l=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>l.test(e);function a(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function i(){let{href:e}=window.location,t=a();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&c(n))return r;if(!r)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class y extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class m extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class g extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},79410:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},80462:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},83721:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(43210);function l(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},84027:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84949:(e,t)=>{function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},85814:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return v},useLinkStatus:function(){return g}});let r=n(40740),l=n(60687),o=r._(n(43210)),a=n(30195),i=n(22142),u=n(59154),c=n(30657),s=n(79289),d=n(96127);n(50148);let f=n(73406),p=n(61794),h=n(63690);function y(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function v(e){let t,n,r,[a,v]=(0,o.useOptimistic)(f.IDLE_LINK_STATUS),g=(0,o.useRef)(null),{href:b,as:w,children:x,prefetch:R=null,passHref:E,replace:_,shallow:P,scroll:S,onClick:T,onMouseEnter:M,onTouchStart:j,legacyBehavior:O=!1,onNavigate:A,ref:C,unstable_dynamicOnHover:N,...k}=e;t=x,O&&("string"==typeof t||"number"==typeof t)&&(t=(0,l.jsx)("a",{children:t}));let L=o.default.useContext(i.AppRouterContext),D=!1!==R,I=null===R?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:U,as:H}=o.default.useMemo(()=>{let e=y(b);return{href:e,as:w?y(w):e}},[b,w]);O&&(n=o.default.Children.only(t));let F=O?n&&"object"==typeof n&&n.ref:C,B=o.default.useCallback(e=>(null!==L&&(g.current=(0,f.mountLinkInstance)(e,U,L,I,D,v)),()=>{g.current&&((0,f.unmountLinkForCurrentNavigation)(g.current),g.current=null),(0,f.unmountPrefetchableInstance)(e)}),[D,U,L,I,v]),z={ref:(0,c.useMergedRef)(B,F),onClick(e){O||"function"!=typeof T||T(e),O&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),L&&(e.defaultPrevented||function(e,t,n,r,l,a,i){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){l&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),o.default.startTransition(()=>{if(i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(n||t,l?"replace":"push",null==a||a,r.current)})}}(e,U,H,g,_,S,A))},onMouseEnter(e){O||"function"!=typeof M||M(e),O&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),L&&D&&(0,f.onNavigationIntent)(e.currentTarget,!0===N)},onTouchStart:function(e){O||"function"!=typeof j||j(e),O&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),L&&D&&(0,f.onNavigationIntent)(e.currentTarget,!0===N)}};return(0,s.isAbsoluteUrl)(H)?z.href=H:O&&!E&&("a"!==n.type||"href"in n.props)||(z.href=(0,d.addBasePath)(H)),r=O?o.default.cloneElement(n,z):(0,l.jsx)("a",{...k,...z,children:t}),(0,l.jsx)(m.Provider,{value:a,children:r})}n(32708);let m=(0,o.createContext)(f.IDLE_LINK_STATUS),g=()=>(0,o.useContext)(m);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86561:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},86770:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,u){let c,[s,d,f,p,h]=n;if(1===t.length){let e=i(n,r);return(0,a.addRefreshMarkerToActiveParallelSegments)(e,u),e}let[y,v]=t;if(!(0,o.matchSegment)(y,s))return null;if(2===t.length)c=i(d[v],r);else if(null===(c=e((0,l.getNextFlightSegmentPath)(t),d[v],r,u)))return null;let m=[t[0],{...d,[v]:c},f,p];return h&&(m[4]=!0),(0,a.addRefreshMarkerToActiveParallelSegments)(m,u),m}}});let r=n(83913),l=n(74007),o=n(14077),a=n(22308);function i(e,t){let[n,l]=e,[a,u]=t;if(a===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,o.matchSegment)(n,a)){let t={};for(let e in l)void 0!==u[e]?t[e]=i(l[e],u[e]):t[e]=l[e];for(let e in u)t[e]||(t[e]=u[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88920:(e,t,n)=>{n.d(t,{N:()=>g});var r=n(60687),l=n(43210),o=n(12157),a=n(72789),i=n(15124),u=n(21279),c=n(18171),s=n(32582);class d extends l.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,c.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f({children:e,isPresent:t,anchorX:n}){let o=(0,l.useId)(),a=(0,l.useRef)(null),i=(0,l.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,l.useContext)(s.Q);return(0,l.useInsertionEffect)(()=>{let{width:e,height:r,top:l,left:c,right:s}=i.current;if(t||!a.current||!e||!r)return;let d="left"===n?`left: ${c}`:`right: ${s}`;a.current.dataset.motionPopId=o;let f=document.createElement("style");return u&&(f.nonce=u),document.head.appendChild(f),f.sheet&&f.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${d}px !important;
            top: ${l}px !important;
          }
        `),()=>{document.head.contains(f)&&document.head.removeChild(f)}},[t]),(0,r.jsx)(d,{isPresent:t,childRef:a,sizeRef:i,children:l.cloneElement(e,{ref:a})})}let p=({children:e,initial:t,isPresent:n,onExitComplete:o,custom:i,presenceAffectsLayout:c,mode:s,anchorX:d})=>{let p=(0,a.M)(h),y=(0,l.useId)(),v=!0,m=(0,l.useMemo)(()=>(v=!1,{id:y,initial:t,isPresent:n,custom:i,onExitComplete:e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;o&&o()},register:e=>(p.set(e,!1),()=>p.delete(e))}),[n,p,o]);return c&&v&&(m={...m}),(0,l.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[n]),l.useEffect(()=>{n||p.size||!o||o()},[n]),"popLayout"===s&&(e=(0,r.jsx)(f,{isPresent:n,anchorX:d,children:e})),(0,r.jsx)(u.t.Provider,{value:m,children:e})};function h(){return new Map}var y=n(86044);let v=e=>e.key||"";function m(e){let t=[];return l.Children.forEach(e,e=>{(0,l.isValidElement)(e)&&t.push(e)}),t}let g=({children:e,custom:t,initial:n=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:s="sync",propagate:d=!1,anchorX:f="left"})=>{let[h,g]=(0,y.xQ)(d),b=(0,l.useMemo)(()=>m(e),[e]),w=d&&!h?[]:b.map(v),x=(0,l.useRef)(!0),R=(0,l.useRef)(b),E=(0,a.M)(()=>new Map),[_,P]=(0,l.useState)(b),[S,T]=(0,l.useState)(b);(0,i.E)(()=>{x.current=!1,R.current=b;for(let e=0;e<S.length;e++){let t=v(S[e]);w.includes(t)?E.delete(t):!0!==E.get(t)&&E.set(t,!1)}},[S,w.length,w.join("-")]);let M=[];if(b!==_){let e=[...b];for(let t=0;t<S.length;t++){let n=S[t],r=v(n);w.includes(r)||(e.splice(t,0,n),M.push(n))}return"wait"===s&&M.length&&(e=M),T(m(e)),P(b),null}let{forceRender:j}=(0,l.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:S.map(e=>{let l=v(e),o=(!d||!!h)&&(b===S||w.includes(l));return(0,r.jsx)(p,{isPresent:o,initial:(!x.current||!!n)&&void 0,custom:t,presenceAffectsLayout:c,mode:s,onExitComplete:o?void 0:()=>{if(!E.has(l))return;E.set(l,!0);let e=!0;E.forEach(t=>{t||(e=!1)}),e&&(j?.(),T(R.current),d&&g?.(),u&&u())},anchorX:f,children:e},l)})})}},89752:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return j},createPrefetchURL:function(){return T},default:function(){return N},isExternalURL:function(){return S}});let r=n(40740),l=n(60687),o=r._(n(43210)),a=n(22142),i=n(59154),u=n(57391),c=n(10449),s=n(19129),d=r._(n(35656)),f=n(35416),p=n(96127),h=n(77022),y=n(67086),v=n(44397),m=n(89330),g=n(25942),b=n(26736),w=n(70642),x=n(12776),R=n(63690),E=n(36875),_=n(97860);n(73406);let P={};function S(e){return e.origin!==window.location.origin}function T(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return S(t)?null:t}function M(e){let{appRouterState:t}=e;return(0,o.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,l={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,u.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(l,"",r)):window.history.replaceState(l,"",r)},[t]),(0,o.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function j(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function O(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function A(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,l=null!==r?r:n;return(0,o.useDeferredValue)(n,l)}function C(e){let t,{actionQueue:n,assetPrefix:r,globalError:u}=e,f=(0,s.useActionQueue)(n),{canonicalUrl:p}=f,{searchParams:x,pathname:S}=(0,o.useMemo)(()=>{let e=new URL(p,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,g.removeBasePath)(e.pathname):e.pathname}},[p]);(0,o.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(P.pendingMpaPath=void 0,(0,s.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,o.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,_.isRedirectError)(t)){e.preventDefault();let n=(0,E.getURLFromRedirectError)(t);(0,E.getRedirectTypeFromError)(t)===_.RedirectType.push?R.publicAppRouterInstance.push(n,{}):R.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:T}=f;if(T.mpaNavigation){if(P.pendingMpaPath!==p){let e=window.location;T.pendingPush?e.assign(p):e.replace(p),P.pendingMpaPath=p}(0,o.use)(m.unresolvedThenable)}(0,o.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,o.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,l){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=O(t),l&&n(l)),e(t,r,l)},window.history.replaceState=function(e,r,l){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=O(e),l&&n(l)),t(e,r,l)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,o.startTransition)(()=>{(0,R.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:j,tree:C,nextUrl:N,focusAndScrollRef:k}=f,L=(0,o.useMemo)(()=>(0,v.findHeadInCache)(j,C[1]),[j,C]),I=(0,o.useMemo)(()=>(0,w.getSelectedParams)(C),[C]),U=(0,o.useMemo)(()=>({parentTree:C,parentCacheNode:j,parentSegmentPath:null,url:p}),[C,j,p]),H=(0,o.useMemo)(()=>({tree:C,focusAndScrollRef:k,nextUrl:N}),[C,k,N]);if(null!==L){let[e,n]=L;t=(0,l.jsx)(A,{headCacheNode:e},n)}else t=null;let F=(0,l.jsxs)(y.RedirectBoundary,{children:[t,j.rsc,(0,l.jsx)(h.AppRouterAnnouncer,{tree:C})]});return F=(0,l.jsx)(d.ErrorBoundary,{errorComponent:u[0],errorStyles:u[1],children:F}),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(M,{appRouterState:f}),(0,l.jsx)(D,{}),(0,l.jsx)(c.PathParamsContext.Provider,{value:I,children:(0,l.jsx)(c.PathnameContext.Provider,{value:S,children:(0,l.jsx)(c.SearchParamsContext.Provider,{value:x,children:(0,l.jsx)(a.GlobalLayoutRouterContext.Provider,{value:H,children:(0,l.jsx)(a.AppRouterContext.Provider,{value:R.publicAppRouterInstance,children:(0,l.jsx)(a.LayoutRouterContext.Provider,{value:U,children:F})})})})})})]})}function N(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:o}=e;return(0,x.useNavFailureHandler)(),(0,l.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,l.jsx)(C,{actionQueue:t,assetPrefix:o,globalError:[n,r]})})}let k=new Set,L=new Set;function D(){let[,e]=o.default.useState(0),t=k.size;return(0,o.useEffect)(()=>{let n=()=>e(e=>e+1);return L.add(n),t!==k.size&&n(),()=>{L.delete(n)}},[t,e]),[...k].map((e,t)=>(0,l.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=k.size;return k.add(e),k.size!==t&&L.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92363:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},95796:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return o}});let r=n(98834),l=n(54674);function o(e,t){return(0,l.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96493:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return l}});let r=n(25232);function l(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96963:(e,t,n)=>{n.d(t,{B:()=>u});var r,l=n(43210),o=n(66156),a=(r||(r=n.t(l,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function u(e){let[t,n]=l.useState(a());return(0,o.N)(()=>{e||n(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},97051:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},97464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,o){let a=o.length<=2,[i,u]=o,c=(0,l.createRouterCacheKey)(u),s=n.parallelRoutes.get(i),d=t.parallelRoutes.get(i);d&&d!==s||(d=new Map(s),t.parallelRoutes.set(i,d));let f=null==s?void 0:s.get(c),p=d.get(c);if(a){p&&p.lazyData&&p!==f||d.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!f){p||d.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===f&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},d.set(c,p)),e(p,f,(0,r.getNextFlightSegmentPath)(o))}}});let r=n(74007),l=n(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(59008),n(57391),n(86770),n(2030),n(25232),n(59435),n(56928),n(89752),n(96493),n(68214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98834:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return l}});let r=n(19169);function l(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:l,hash:o}=(0,r.parsePath)(e);return""+t+n+l+o}},99270:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};