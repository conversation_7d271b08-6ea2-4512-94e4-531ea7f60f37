"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1647],{5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5845:(e,t,n)=>{n.d(t,{i:()=>a});var r,o=n(12115),i=n(52712),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function a({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),a=o.useRef(t);return l(()=>{a.current=t},[t]),o.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,r,a]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[c,e,a,u])]}Symbol("RADIX:SYNC_STATE")},11275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(12115),o=n(52712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},15452:(e,t,n)=>{n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ei,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(12115),o=n(85185),i=n(6101),l=n(46081),a=n(61285),u=n(5845),c=n(19178),s=n(25519),d=n(34378),f=n(28905),p=n(63655),h=n(92293),m=n(93795),v=n(38168),g=n(99708),y=n(95155),w="Dialog",[b,x]=(0,l.A)(w),[E,S]=b(w),C=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:l,modal:c=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.i)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:w});return(0,y.jsx)(E,{scope:t,triggerRef:s,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};C.displayName=w;var R="DialogTrigger",A=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=S(R,n),a=(0,i.s)(t,l.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":K(l.open),...r,ref:a,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});A.displayName=R;var T="DialogPortal",[N,k]=b(T,{forceMount:void 0}),L=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,l=S(T,t);return(0,y.jsx)(N,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||l.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};L.displayName=T;var M="DialogOverlay",P=r.forwardRef((e,t)=>{let n=k(M,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=S(M,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(O,{...o,ref:t})}):null});P.displayName=M;var D=(0,g.TL)("DialogOverlay.RemoveScroll"),O=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S(M,n);return(0,y.jsx)(m.A,{as:D,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":K(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),j="DialogContent",I=r.forwardRef((e,t)=>{let n=k(j,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=S(j,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||i.open,children:i.modal?(0,y.jsx)(F,{...o,ref:t}):(0,y.jsx)(W,{...o,ref:t})})});I.displayName=j;var F=r.forwardRef((e,t)=>{let n=S(j,e.__scopeDialog),l=r.useRef(null),a=(0,i.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(H,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),W=r.forwardRef((e,t)=>{let n=S(j,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(H,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let a=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),H=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:a,...u}=e,d=S(j,n),f=r.useRef(null),p=(0,i.s)(t,f);return(0,h.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:a,children:(0,y.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":K(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Z,{titleId:d.titleId}),(0,y.jsx)($,{contentRef:f,descriptionId:d.descriptionId})]})]})}),B="DialogTitle",_=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S(B,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});_.displayName=B;var z="DialogDescription",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=S(z,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});V.displayName=z;var G="DialogClose",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=S(G,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function K(e){return e?"open":"closed"}U.displayName=G;var q="DialogTitleWarning",[Y,X]=(0,l.q)(q,{contentName:j,titleName:B,docsSlug:"dialog"}),Z=e=>{let{titleId:t}=e,n=X(q),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},$=e=>{let{contentRef:t,descriptionId:n}=e,o=X("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},J=C,Q=A,ee=L,et=P,en=I,er=_,eo=V,ei=U},17951:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},19178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(12115),i=n(85185),l=n(63655),a=n(6101),u=n(39033),c=n(95155),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...x}=e,E=o.useContext(d),[S,C]=o.useState(null),R=null!=(f=null==S?void 0:S.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,A]=o.useState({}),T=(0,a.s)(t,e=>C(e)),N=Array.from(E.layers),[k]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),L=N.indexOf(k),M=S?N.indexOf(S):-1,P=E.layersWithOutsidePointerEventsDisabled.size>0,D=M>=L,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));D&&!n&&(null==g||g(e),null==w||w(e),e.defaultPrevented||null==b||b())},R),j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},R);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M===E.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},R),o.useEffect(()=>{if(S)return m&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),p(),()=>{m&&1===E.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[S,R,m,E]),o.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),p())},[S,E]),o.useEffect(()=>{let e=()=>A({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(l.sG.div,{...x,ref:T,style:{pointerEvents:P?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,O.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},22697:(e,t,n)=>{n.d(t,{UC:()=>t7,YJ:()=>ne,In:()=>t4,q7:()=>nn,VF:()=>no,p4:()=>nr,JU:()=>nt,ZL:()=>t3,bL:()=>t2,wn:()=>nl,PP:()=>ni,wv:()=>na,l9:()=>t9,WT:()=>t6,LM:()=>t8});var r=n(12115),o=n(47650);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(85185),a=n(37328),u=n(6101),c=n(46081),s=n(94315),d=n(19178),f=n(92293),p=n(25519),h=n(61285);let m=["top","right","bottom","left"],v=Math.min,g=Math.max,y=Math.round,w=Math.floor,b=e=>({x:e,y:e}),x={left:"right",right:"left",bottom:"top",top:"bottom"},E={start:"end",end:"start"};function S(e,t){return"function"==typeof e?e(t):e}function C(e){return e.split("-")[0]}function R(e){return e.split("-")[1]}function A(e){return"x"===e?"y":"x"}function T(e){return"y"===e?"height":"width"}function N(e){return["top","bottom"].includes(C(e))?"y":"x"}function k(e){return e.replace(/start|end/g,e=>E[e])}function L(e){return e.replace(/left|right|bottom|top/g,e=>x[e])}function M(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function P(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function D(e,t,n){let r,{reference:o,floating:i}=e,l=N(t),a=A(N(t)),u=T(a),c=C(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(R(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let O=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=D(c,r,u),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=D(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function j(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=S(t,e),h=M(p),m=a[f?"floating"===d?"reference":"floating":d],v=P(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=P(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(v.top-b.top+h.top)/w.y,bottom:(b.bottom-v.bottom+h.bottom)/w.y,left:(v.left-b.left+h.left)/w.x,right:(b.right-v.right+h.right)/w.x}}function I(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function F(e){return m.some(t=>e[t]>=0)}async function W(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=C(n),a=R(n),u="y"===N(n),c=["left","top"].includes(l)?-1:1,s=i&&u?-1:1,d=S(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function H(){return"undefined"!=typeof window}function B(e){return V(e)?(e.nodeName||"").toLowerCase():"#document"}function _(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function z(e){var t;return null==(t=(V(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function V(e){return!!H()&&(e instanceof Node||e instanceof _(e).Node)}function G(e){return!!H()&&(e instanceof Element||e instanceof _(e).Element)}function U(e){return!!H()&&(e instanceof HTMLElement||e instanceof _(e).HTMLElement)}function K(e){return!!H()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof _(e).ShadowRoot)}function q(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=J(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Y(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function X(e){let t=Z(),n=G(e)?J(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function $(e){return["html","body","#document"].includes(B(e))}function J(e){return _(e).getComputedStyle(e)}function Q(e){return G(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ee(e){if("html"===B(e))return e;let t=e.assignedSlot||e.parentNode||K(e)&&e.host||z(e);return K(t)?t.host:t}function et(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ee(t);return $(n)?t.ownerDocument?t.ownerDocument.body:t.body:U(n)&&q(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=_(o);if(i){let e=en(l);return t.concat(l,l.visualViewport||[],q(o)?o:[],e&&n?et(e):[])}return t.concat(o,et(o,[],n))}function en(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function er(e){let t=J(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=U(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=y(n)!==i||y(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eo(e){return G(e)?e:e.contextElement}function ei(e){let t=eo(e);if(!U(t))return b(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=er(t),l=(i?y(n.width):n.width)/r,a=(i?y(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let el=b(0);function ea(e){let t=_(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:el}function eu(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eo(e),a=b(1);t&&(r?G(r)&&(a=ei(r)):a=ei(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===_(l))&&o)?ea(l):b(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=_(l),t=r&&G(r)?_(r):r,n=e,o=en(n);for(;o&&r&&t!==n;){let e=ei(o),t=o.getBoundingClientRect(),r=J(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=en(n=_(o))}}return P({width:d,height:f,x:c,y:s})}function ec(e,t){let n=Q(e).scrollLeft;return t?t.left+n:eu(z(e)).left+n}function es(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ec(e,r)),y:r.top+t.scrollTop}}function ed(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=_(e),r=z(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=Z();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=z(e),n=Q(e),r=e.ownerDocument.body,o=g(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=g(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ec(e),a=-n.scrollTop;return"rtl"===J(r).direction&&(l+=g(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(z(e));else if(G(t))r=function(e,t){let n=eu(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=U(e)?ei(e):b(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=ea(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return P(r)}function ef(e){return"static"===J(e).position}function ep(e,t){if(!U(e)||"fixed"===J(e).position)return null;if(t)return t(e);let n=e.offsetParent;return z(e)===n&&(n=n.ownerDocument.body),n}function eh(e,t){let n=_(e);if(Y(e))return n;if(!U(e)){let t=ee(e);for(;t&&!$(t);){if(G(t)&&!ef(t))return t;t=ee(t)}return n}let r=ep(e,t);for(;r&&["table","td","th"].includes(B(r))&&ef(r);)r=ep(r,t);return r&&$(r)&&ef(r)&&!X(r)?n:r||function(e){let t=ee(e);for(;U(t)&&!$(t);){if(X(t))return t;if(Y(t))break;t=ee(t)}return null}(e)||n}let em=async function(e){let t=this.getOffsetParent||eh,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=U(t),o=z(t),i="fixed"===n,l=eu(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=b(0);if(r||!r&&!i)if(("body"!==B(t)||q(o))&&(a=Q(t)),r){let e=eu(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ec(o));i&&!r&&o&&(u.x=ec(o));let c=!o||r||i?b(0):es(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ev={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=z(r),a=!!t&&Y(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=b(1),s=b(0),d=U(r);if((d||!d&&!i)&&(("body"!==B(r)||q(l))&&(u=Q(r)),U(r))){let e=eu(r);c=ei(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?b(0):es(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:z,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?Y(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=et(e,[],!1).filter(e=>G(e)&&"body"!==B(e)),o=null,i="fixed"===J(e).position,l=i?ee(e):e;for(;G(l)&&!$(l);){let t=J(l),n=X(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||q(l)&&!n&&function e(t,n){let r=ee(t);return!(r===n||!G(r)||$(r))&&("fixed"===J(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=ee(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=ed(t,n,o);return e.top=g(r.top,e.top),e.right=v(r.right,e.right),e.bottom=v(r.bottom,e.bottom),e.left=g(r.left,e.left),e},ed(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eh,getElementRects:em,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=er(e);return{width:t,height:n}},getScale:ei,isElement:G,isRTL:function(e){return"rtl"===J(e).direction}};function eg(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ey=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=S(e,t)||{};if(null==c)return{};let d=M(s),f={x:n,y:r},p=A(N(o)),h=T(p),m=await l.getDimensions(c),y="y"===p,w=y?"clientHeight":"clientWidth",b=i.reference[h]+i.reference[p]-f[p]-i.floating[h],x=f[p]-i.reference[p],E=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),C=E?E[w]:0;C&&await (null==l.isElement?void 0:l.isElement(E))||(C=a.floating[w]||i.floating[h]);let k=C/2-m[h]/2-1,L=v(d[y?"top":"left"],k),P=v(d[y?"bottom":"right"],k),D=C-m[h]-P,O=C/2-m[h]/2+(b/2-x/2),j=g(L,v(O,D)),I=!u.arrow&&null!=R(o)&&O!==j&&i.reference[h]/2-(O<L?L:P)-m[h]/2<0,F=I?O<L?O-L:O-D:0;return{[p]:f[p]+F,data:{[p]:j,centerOffset:O-j-F,...I&&{alignmentOffset:F}},reset:I}}}),ew=(e,t,n)=>{let r=new Map,o={platform:ev,...n},i={...o.platform,_c:r};return O(e,t,{...o,platform:i})};var eb="undefined"!=typeof document?r.useLayoutEffect:function(){};function ex(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ex(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ex(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eE(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eS(e,t){let n=eE(e);return Math.round(t*n)/n}function eC(e){let t=r.useRef(e);return eb(()=>{t.current=e}),t}let eR=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ey({element:n.current,padding:r}).fn(t):{}:n?ey({element:n,padding:r}).fn(t):{}}}),eA=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await W(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),eT=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=S(e,t),c={x:n,y:r},s=await j(t,u),d=N(C(o)),f=A(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=g(n,v(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+s[e],r=h-s[t];h=g(n,v(h,r))}let m=a.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=S(e,t),s={x:n,y:r},d=N(o),f=A(d),p=s[f],h=s[d],m=S(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=["top","left"].includes(C(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=S(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let b=C(a),x=N(s),E=C(s)===s,M=await (null==d.isRTL?void 0:d.isRTL(f.floating)),P=m||(E||!y?[L(s)]:function(e){let t=L(e);return[k(e),t,k(t)]}(s)),D="none"!==g;!m&&D&&P.push(...function(e,t,n,r){let o=R(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(C(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(k)))),i}(s,y,g,M));let O=[s,...P],I=await j(t,w),F=[],W=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&F.push(I[b]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=R(e),o=A(N(e)),i=T(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=L(l)),[l,L(l)]}(a,c,M);F.push(I[e[0]],I[e[1]])}if(W=[...W,{placement:a,overflows:F}],!F.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=O[e];if(t&&("alignment"!==h||x===N(t)||W.every(e=>e.overflows[0]>0&&N(e.placement)===x)))return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(i=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(l=W.filter(e=>{if(D){let t=N(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:u,elements:c}=t,{apply:s=()=>{},...d}=S(e,t),f=await j(t,d),p=C(l),h=R(l),m="y"===N(l),{width:y,height:w}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let b=w-f.top-f.bottom,x=y-f.left-f.right,E=v(w-f[o],b),A=v(y-f[i],x),T=!t.middlewareData.shift,k=E,L=A;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(L=x),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=b),T&&!h){let e=g(f.left,0),t=g(f.right,0),n=g(f.top,0),r=g(f.bottom,0);m?L=y-2*(0!==e||0!==t?e+t:g(f.left,f.right)):k=w-2*(0!==n||0!==r?n+r:g(f.top,f.bottom))}await s({...t,availableWidth:L,availableHeight:k});let M=await u.getDimensions(c.floating);return y!==M.width||w!==M.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eM=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=S(e,t);switch(r){case"referenceHidden":{let e=I(await j(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:F(e)}}}case"escaped":{let e=I(await j(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:F(e)}}}default:return{}}}}}(e),options:[e,t]}),eP=(e,t)=>({...eR(e),options:[e,t]});var eD=n(63655),eO=n(95155),ej=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eO.jsx)(eD.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eO.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ej.displayName="Arrow";var eI=n(39033),eF=n(52712),eW=n(11275),eH="Popper",[eB,e_]=(0,c.A)(eH),[ez,eV]=eB(eH),eG=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eO.jsx)(ez,{scope:t,anchor:o,onAnchorChange:i,children:n})};eG.displayName=eH;var eU="PopperAnchor",eK=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eV(eU,n),a=r.useRef(null),c=(0,u.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eO.jsx)(eD.sG.div,{...i,ref:c})});eK.displayName=eU;var eq="PopperContent",[eY,eX]=eB(eq),eZ=r.forwardRef((e,t)=>{var n,i,l,a,c,s,d,f;let{__scopePopper:p,side:h="bottom",sideOffset:m=0,align:y="center",alignOffset:b=0,arrowPadding:x=0,avoidCollisions:E=!0,collisionBoundary:S=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:A=!1,updatePositionStrategy:T="optimized",onPlaced:N,...k}=e,L=eV(eq,p),[M,P]=r.useState(null),D=(0,u.s)(t,e=>P(e)),[O,j]=r.useState(null),I=(0,eW.X)(O),F=null!=(d=null==I?void 0:I.width)?d:0,W=null!=(f=null==I?void 0:I.height)?f:0,H="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},B=Array.isArray(S)?S:[S],_=B.length>0,V={padding:H,boundary:B.filter(e0),altBoundary:_},{refs:G,floatingStyles:U,placement:K,isPositioned:q,middlewareData:Y}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:u}={},transform:c=!0,whileElementsMounted:s,open:d}=e,[f,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=r.useState(i);ex(h,i)||m(i);let[v,g]=r.useState(null),[y,w]=r.useState(null),b=r.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=a||v,S=u||y,C=r.useRef(null),R=r.useRef(null),A=r.useRef(f),T=null!=s,N=eC(s),k=eC(l),L=eC(d),M=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:h};k.current&&(e.platform=k.current),ew(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};P.current&&!ex(A.current,t)&&(A.current=t,o.flushSync(()=>{p(t)}))})},[h,t,n,k,L]);eb(()=>{!1===d&&A.current.isPositioned&&(A.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let P=r.useRef(!1);eb(()=>(P.current=!0,()=>{P.current=!1}),[]),eb(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(N.current)return N.current(E,S,M);M()}},[E,S,M,N,T]);let D=r.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),O=r.useMemo(()=>({reference:E,floating:S}),[E,S]),j=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=eS(O.floating,f.x),r=eS(O.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...eE(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,O.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:M,refs:D,elements:O,floatingStyles:j}),[f,M,D,O,j])}({strategy:"fixed",placement:h+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eo(e),d=i||l?[...s?et(s):[],...et(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=z(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let h=w(d),m=w(o.clientWidth-(s+f)),y={rootMargin:-h+"px "+-m+"px "+-w(o.clientHeight-(d+p))+"px "+-w(s)+"px",threshold:g(0,v(1,u))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==u){if(!b)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eg(c,e.getBoundingClientRect())||l(),b=!1}try{r=new IntersectionObserver(x,{...y,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,y)}r.observe(e)}(!0),i}(s,n):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),s&&!c&&h.observe(s),h.observe(t));let m=c?eu(e):null;return c&&function t(){let r=eu(e);m&&!eg(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===T})},elements:{reference:L.anchor},middleware:[eA({mainAxis:m+W,alignmentAxis:b}),E&&eT({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?eN():void 0,...V}),E&&ek({...V}),eL({...V,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),O&&eP({element:O,padding:x}),e1({arrowWidth:F,arrowHeight:W}),A&&eM({strategy:"referenceHidden",...V})]}),[X,Z]=e5(K),$=(0,eI.c)(N);(0,eF.N)(()=>{q&&(null==$||$())},[q,$]);let J=null==(n=Y.arrow)?void 0:n.x,Q=null==(i=Y.arrow)?void 0:i.y,ee=(null==(l=Y.arrow)?void 0:l.centerOffset)!==0,[en,er]=r.useState();return(0,eF.N)(()=>{M&&er(window.getComputedStyle(M).zIndex)},[M]),(0,eO.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:q?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(a=Y.transformOrigin)?void 0:a.x,null==(c=Y.transformOrigin)?void 0:c.y].join(" "),...(null==(s=Y.hide)?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eO.jsx)(eY,{scope:p,placedSide:X,onArrowChange:j,arrowX:J,arrowY:Q,shouldHideArrow:ee,children:(0,eO.jsx)(eD.sG.div,{"data-side":X,"data-align":Z,...k,ref:D,style:{...k.style,animation:q?void 0:"none"}})})})});eZ.displayName=eq;var e$="PopperArrow",eJ={top:"bottom",right:"left",bottom:"top",left:"right"},eQ=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eX(e$,n),i=eJ[o.placedSide];return(0,eO.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eO.jsx)(ej,{...r,ref:t,style:{...r.style,display:"block"}})})});function e0(e){return null!==e}eQ.displayName=e$;var e1=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,h]=e5(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(l=null==(o=c.arrow)?void 0:o.y)?l:0)+f/2,y="",w="";return"bottom"===p?(y=s?m:"".concat(v,"px"),w="".concat(-f,"px")):"top"===p?(y=s?m:"".concat(v,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?m:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function e5(e){let[t,n="center"]=e.split("-");return[t,n]}var e2=n(34378),e9=n(99708),e6=n(5845),e4=n(45503),e3=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,eO.jsx)(eD.sG.span,{...e,ref:t,style:{...e3,...e.style}})).displayName="VisuallyHidden";var e7=n(38168),e8=n(93795),te=[" ","Enter","ArrowUp","ArrowDown"],tt=[" ","Enter"],tn="Select",[tr,to,ti]=(0,a.N)(tn),[tl,ta]=(0,c.A)(tn,[ti,e_]),tu=e_(),[tc,ts]=tl(tn),[td,tf]=tl(tn),tp=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:u,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:m,required:v,form:g}=e,y=tu(t),[w,b]=r.useState(null),[x,E]=r.useState(null),[S,C]=r.useState(!1),R=(0,s.jH)(d),[A,T]=(0,e6.i)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:tn}),[N,k]=(0,e6.i)({prop:a,defaultProp:u,onChange:c,caller:tn}),L=r.useRef(null),M=!w||g||!!w.closest("form"),[P,D]=r.useState(new Set),O=Array.from(P).map(e=>e.props.value).join(";");return(0,eO.jsx)(eG,{...y,children:(0,eO.jsxs)(tc,{required:v,scope:t,trigger:w,onTriggerChange:b,valueNode:x,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,h.B)(),value:N,onValueChange:k,open:A,onOpenChange:T,dir:R,triggerPointerDownPosRef:L,disabled:m,children:[(0,eO.jsx)(tr.Provider,{scope:t,children:(0,eO.jsx)(td,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{D(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),M?(0,eO.jsxs)(tQ,{"aria-hidden":!0,required:v,tabIndex:-1,name:f,autoComplete:p,value:N,onChange:e=>k(e.target.value),disabled:m,form:g,children:[void 0===N?(0,eO.jsx)("option",{value:""}):null,Array.from(P)]},O):null]})})};tp.displayName=tn;var th="SelectTrigger",tm=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=tu(n),c=ts(th,n),s=c.disabled||o,d=(0,u.s)(t,c.onTriggerChange),f=to(n),p=r.useRef("touch"),[h,m,v]=t1(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===c.value),r=t5(t,e,n);void 0!==r&&c.onValueChange(r.value)}),g=e=>{s||(c.onOpenChange(!0),v()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eO.jsx)(eK,{asChild:!0,...a,children:(0,eO.jsx)(eD.sG.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":t0(c.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&te.includes(e.key)&&(g(),e.preventDefault())})})})});tm.displayName=th;var tv="SelectValue",tg=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,c=ts(tv,n),{onValueNodeHasChildrenChange:s}=c,d=void 0!==i,f=(0,u.s)(t,c.onValueNodeChange);return(0,eF.N)(()=>{s(d)},[s,d]),(0,eO.jsx)(eD.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:t0(c.value)?(0,eO.jsx)(eO.Fragment,{children:l}):i})});tg.displayName=tv;var ty=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,eO.jsx)(eD.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});ty.displayName="SelectIcon";var tw=e=>(0,eO.jsx)(e2.Z,{asChild:!0,...e});tw.displayName="SelectPortal";var tb="SelectContent",tx=r.forwardRef((e,t)=>{let n=ts(tb,e.__scopeSelect),[i,l]=r.useState();return((0,eF.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,eO.jsx)(tR,{...e,ref:t}):i?o.createPortal((0,eO.jsx)(tE,{scope:e.__scopeSelect,children:(0,eO.jsx)(tr.Slot,{scope:e.__scopeSelect,children:(0,eO.jsx)("div",{children:e.children})})}),i):null});tx.displayName=tb;var[tE,tS]=tl(tb),tC=(0,e9.TL)("SelectContent.RemoveScroll"),tR=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:c,side:s,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E,...S}=e,C=ts(tb,n),[R,A]=r.useState(null),[T,N]=r.useState(null),k=(0,u.s)(t,e=>A(e)),[L,M]=r.useState(null),[P,D]=r.useState(null),O=to(n),[j,I]=r.useState(!1),F=r.useRef(!1);r.useEffect(()=>{if(R)return(0,e7.Eq)(R)},[R]),(0,f.Oh)();let W=r.useCallback(e=>{let[t,...n]=O().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&T&&(T.scrollTop=0),n===r&&T&&(T.scrollTop=T.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[O,T]),H=r.useCallback(()=>W([L,R]),[W,L,R]);r.useEffect(()=>{j&&H()},[j,H]);let{onOpenChange:B,triggerPointerDownPosRef:_}=C;r.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=_.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(i=null==(r=_.current)?void 0:r.y)?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():R.contains(n.target)||B(!1),document.removeEventListener("pointermove",t),_.current=null};return null!==_.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[R,B,_]),r.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[z,V]=t1(e=>{let t=O().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=t5(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),G=r.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==C.value&&C.value===t||r)&&(M(e),r&&(F.current=!0))},[C.value]),U=r.useCallback(()=>null==R?void 0:R.focus(),[R]),K=r.useCallback((e,t,n)=>{let r=!F.current&&!n;(void 0!==C.value&&C.value===t||r)&&D(e)},[C.value]),q="popper"===o?tT:tA,Y=q===tT?{side:s,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E}:{};return(0,eO.jsx)(tE,{scope:n,content:R,viewport:T,onViewportChange:N,itemRefCallback:G,selectedItem:L,onItemLeave:U,itemTextRefCallback:K,focusSelectedItem:H,selectedItemText:P,position:o,isPositioned:j,searchRef:z,children:(0,eO.jsx)(e8.A,{as:tC,allowPinchZoom:!0,children:(0,eO.jsx)(p.n,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{var t;null==(t=C.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eO.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,eO.jsx)(q,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...S,...Y,onPlaced:()=>I(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,l.m)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||V(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>W(t)),e.preventDefault()}})})})})})})});tR.displayName="SelectContentImpl";var tA=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=ts(tb,n),c=tS(tb,n),[s,d]=r.useState(null),[f,p]=r.useState(null),h=(0,u.s)(t,e=>p(e)),m=to(n),v=r.useRef(!1),g=r.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:b,focusSelectedItem:x}=c,E=r.useCallback(()=>{if(a.trigger&&a.valueNode&&s&&f&&y&&w&&b){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=b.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,u=e.width+a,c=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.left=d+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,u=e.width+a,c=Math.max(u,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-c)]);s.style.minWidth=u+"px",s.style.right=d+"px"}let l=m(),u=window.innerHeight-20,c=y.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),x=p+h+c+parseInt(d.paddingBottom,10)+g,E=Math.min(5*w.offsetHeight,x),S=window.getComputedStyle(y),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),A=e.top+e.height/2-10,T=w.offsetHeight/2,N=p+h+(w.offsetTop+T);if(N<=A){let e=l.length>0&&w===l[l.length-1].ref.current;s.style.bottom="0px";let t=Math.max(u-A,T+(e?R:0)+(f.clientHeight-y.offsetTop-y.offsetHeight)+g);s.style.height=N+t+"px"}else{let e=l.length>0&&w===l[0].ref.current;s.style.top="0px";let t=Math.max(A,p+y.offsetTop+(e?C:0)+T);s.style.height=t+(x-N)+"px",y.scrollTop=N-A+y.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=E+"px",s.style.maxHeight=u+"px",null==o||o(),requestAnimationFrame(()=>v.current=!0)}},[m,a.trigger,a.valueNode,s,f,y,w,b,a.dir,o]);(0,eF.N)(()=>E(),[E]);let[S,C]=r.useState();(0,eF.N)(()=>{f&&C(window.getComputedStyle(f).zIndex)},[f]);let R=r.useCallback(e=>{e&&!0===g.current&&(E(),null==x||x(),g.current=!1)},[E,x]);return(0,eO.jsx)(tN,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:v,onScrollButtonChange:R,children:(0,eO.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,eO.jsx)(eD.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tA.displayName="SelectItemAlignedPosition";var tT=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=tu(n);return(0,eO.jsx)(eZ,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tT.displayName="SelectPopperPosition";var[tN,tk]=tl(tb,{}),tL="SelectViewport",tM=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=tS(tL,n),c=tk(tL,n),s=(0,u.s)(t,a.onViewportChange),d=r.useRef(0);return(0,eO.jsxs)(eO.Fragment,{children:[(0,eO.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,eO.jsx)(tr.Slot,{scope:n,children:(0,eO.jsx)(eD.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=c;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tM.displayName=tL;var tP="SelectGroup",[tD,tO]=tl(tP),tj=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,eO.jsx)(tD,{scope:n,id:o,children:(0,eO.jsx)(eD.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tj.displayName=tP;var tI="SelectLabel",tF=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tO(tI,n);return(0,eO.jsx)(eD.sG.div,{id:o.id,...r,ref:t})});tF.displayName=tI;var tW="SelectItem",[tH,tB]=tl(tW),t_=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...c}=e,s=ts(tW,n),d=tS(tW,n),f=s.value===o,[p,m]=r.useState(null!=a?a:""),[v,g]=r.useState(!1),y=(0,u.s)(t,e=>{var t;return null==(t=d.itemRefCallback)?void 0:t.call(d,e,o,i)}),w=(0,h.B)(),b=r.useRef("touch"),x=()=>{i||(s.onValueChange(o),s.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eO.jsx)(tH,{scope:n,value:o,disabled:i,textId:w,isSelected:f,onItemTextChange:r.useCallback(e=>{m(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,eO.jsx)(tr.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,eO.jsx)(eD.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:y,onFocus:(0,l.m)(c.onFocus,()=>g(!0)),onBlur:(0,l.m)(c.onBlur,()=>g(!1)),onClick:(0,l.m)(c.onClick,()=>{"mouse"!==b.current&&x()}),onPointerUp:(0,l.m)(c.onPointerUp,()=>{"mouse"===b.current&&x()}),onPointerDown:(0,l.m)(c.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(c.onPointerMove,e=>{if(b.current=e.pointerType,i){var t;null==(t=d.onItemLeave)||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(c.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=d.onItemLeave)||t.call(d)}}),onKeyDown:(0,l.m)(c.onKeyDown,e=>{var t;((null==(t=d.searchRef)?void 0:t.current)===""||" "!==e.key)&&(tt.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});t_.displayName=tW;var tz="SelectItemText",tV=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,c=ts(tz,n),s=tS(tz,n),d=tB(tz,n),f=tf(tz,n),[p,h]=r.useState(null),m=(0,u.s)(t,e=>h(e),d.onItemTextChange,e=>{var t;return null==(t=s.itemTextRefCallback)?void 0:t.call(s,e,d.value,d.disabled)}),v=null==p?void 0:p.textContent,g=r.useMemo(()=>(0,eO.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=f;return(0,eF.N)(()=>(y(g),()=>w(g)),[y,w,g]),(0,eO.jsxs)(eO.Fragment,{children:[(0,eO.jsx)(eD.sG.span,{id:d.textId,...a,ref:m}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?o.createPortal(a.children,c.valueNode):null]})});tV.displayName=tz;var tG="SelectItemIndicator",tU=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return tB(tG,n).isSelected?(0,eO.jsx)(eD.sG.span,{"aria-hidden":!0,...r,ref:t}):null});tU.displayName=tG;var tK="SelectScrollUpButton",tq=r.forwardRef((e,t)=>{let n=tS(tK,e.__scopeSelect),o=tk(tK,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,eF.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eO.jsx)(tZ,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});tq.displayName=tK;var tY="SelectScrollDownButton",tX=r.forwardRef((e,t)=>{let n=tS(tY,e.__scopeSelect),o=tk(tY,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,eF.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eO.jsx)(tZ,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});tX.displayName=tY;var tZ=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=tS("SelectScrollButton",n),u=r.useRef(null),c=to(n),s=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>s(),[s]),(0,eF.N)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[c]),(0,eO.jsx)(eD.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{s()})})}),t$=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,eO.jsx)(eD.sG.div,{"aria-hidden":!0,...r,ref:t})});t$.displayName="SelectSeparator";var tJ="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tu(n),i=ts(tJ,n),l=tS(tJ,n);return i.open&&"popper"===l.position?(0,eO.jsx)(eQ,{...o,...r,ref:t}):null}).displayName=tJ;var tQ=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,...i}=e,l=r.useRef(null),a=(0,u.s)(t,l),c=(0,e4.Z)(o);return r.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(c!==o&&t){let n=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(n)}},[c,o]),(0,eO.jsx)(eD.sG.select,{...i,style:{...e3,...i.style},ref:a,defaultValue:o})});function t0(e){return""===e||void 0===e}function t1(e){let t=(0,eI.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function t5(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}tQ.displayName="SelectBubbleInput";var t2=tp,t9=tm,t6=tg,t4=ty,t3=tw,t7=tx,t8=tM,ne=tj,nt=tF,nn=t_,nr=tV,no=tU,ni=tq,nl=tX,na=t$},25519:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(12115),o=n(6101),i=n(63655),l=n(39033),a=n(95155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,l.c)(v),E=(0,l.c)(g),S=r.useRef(null),C=(0,o.s)(t,e=>b(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:h(S.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,R.paused]),r.useEffect(()=>{if(w){m.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,E),m.remove(R)},0)}}},[w,x,E,R]);let A=r.useCallback(e=>{if(!n&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,R.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:A})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null==(n=(e=v(e,t))[0])||n.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},28905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(12115),o=n(6101),i=n(52712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(u.current);s.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=a(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(s.current=a(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,l(e)},[])}}(t),u="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),c=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||l.isPresent?r.cloneElement(u,{ref:c}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},34378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(12115),o=n(47650),i=n(63655),l=n(52712),a=n(95155),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,l.N)(()=>f(!0),[]);let p=c||d&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},34869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},37328:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function o(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function i(e,t,n){var o=r(e,t,"set");if(o.set)o.set.call(e,n);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=n}return n}n.d(t,{N:()=>f});var l,a=n(12115),u=n(46081),c=n(6101),s=n(99708),d=n(95155);function f(e){let t=e+"CollectionProvider",[n,r]=(0,u.A)(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:n}=e,r=a.useRef(null),i=a.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};l.displayName=t;let f=e+"CollectionSlot",p=(0,s.TL)(f),h=a.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(f,n),l=(0,c.s)(t,o.collectionRef);return(0,d.jsx)(p,{ref:l,children:r})});h.displayName=f;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,s.TL)(m),y=a.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,l=a.useRef(null),u=(0,c.s)(t,l),s=i(m,n);return a.useEffect(()=>(s.itemMap.set(l,{ref:l,...o}),()=>void s.itemMap.delete(l))),(0,d.jsx)(g,{...{[v]:""},ref:u,children:r})});return y.displayName=m,[{Provider:l,Slot:h,ItemSlot:y},function(t){let n=i(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var p=new WeakMap;function h(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=m(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function m(e){return e!=e||0===e?0:Math.trunc(e)}l=new WeakMap},38168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var s=l[n],d=[],f=new Set,p=new Set(c),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,a),s.set(e,u),d.push(e),1===a&&l&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=s.get(e)-1;o.set(e,t),s.set(e,l),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),l||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},45503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(12115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},47863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},53904:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},61285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(12115),i=n(52712),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},62098:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},66474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},66932:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},91788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(12115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:l()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},93509:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},93795:(e,t,n)=>{n.d(t,{A:()=>K});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function l(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(12115)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,l=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(i)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return l.options=i({async:!0,ssr:!1},e),l}(),m=function(){},v=a.forwardRef(function(e,t){var n,r,o,u,c=a.useRef(null),p=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,S=e.shards,C=e.sideCar,R=e.noRelative,A=e.noIsolation,T=e.inert,N=e.allowPinchZoom,k=e.as,L=e.gapMode,M=l(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),D=i(i({},M),v);return a.createElement(a.Fragment,null,E&&a.createElement(C,{sideCar:h,removeScrollBar:x,shards:S,noRelative:R,noIsolation:A,inert:T,setCallbacks:g,allowPinchZoom:!!N,lockRef:c,gapMode:L}),y?a.cloneElement(a.Children.only(w),i(i({},D),{ref:P})):a.createElement(void 0===k?"div":k,i({},D,{className:b,ref:P}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=l(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=b(),A="data-scroll-locked",T=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},N=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},k=function(){a.useEffect(function(){return document.body.setAttribute(A,(N()+1).toString()),function(){var e=N()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},L=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;k();var i=a.useMemo(function(){return C(o)},[o]);return a.createElement(R,{styles:T(i,!t,o,n?"":"!important")})},M=!1;if("undefined"!=typeof window)try{var P=Object.defineProperty({},"passive",{get:function(){return M=!0,!0}});window.addEventListener("test",P,P),window.removeEventListener("test",P,P)}catch(e){M=!1}var D=!!M&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},j=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},W=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var h=F(e,u),m=h[0],v=h[1]-h[2]-l*m;(m||v)&&I(e,u)&&(f+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},H=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},_=function(e){return e&&"current"in e?e.current:e},z=0,V=[];let G=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(z++)[0],i=a.useState(b)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(_),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=H(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=j(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=j(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return W(p,t,e,"h"===p?u:c,!0)},[]),c=a.useCallback(function(e){if(V.length&&V[V.length-1]===i){var n="deltaY"in e?B(e):H(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(_).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=H(e),r.current=void 0},[]),f=a.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=a.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]);a.useEffect(function(){return V.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,D),document.addEventListener("touchmove",c,D),document.addEventListener("touchstart",d,D),function(){V=V.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,D),document.removeEventListener("touchmove",c,D),document.removeEventListener("touchstart",d,D)}},[]);var h=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(L,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),g);var U=a.forwardRef(function(e,t){return a.createElement(v,i({},e,{ref:t,sideCar:G}))});U.classNames=v.classNames;let K=U},94315:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(12115);n(95155);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}}}]);