<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, viewport-fit=cover, user-scalable=no"/><link rel="stylesheet" href="/_next/static/css/56cc0dd3233d2018.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-101e750c36e44303.js"/><script src="/_next/static/chunks/4bd1b696-58114847d125c4a3.js" async=""></script><script src="/_next/static/chunks/1684-0a620f6f17136dca.js" async=""></script><script src="/_next/static/chunks/main-app-ad34e622f20ad987.js" async=""></script><script src="/_next/static/chunks/app/layout-c92ee0b72061c2db.js" async=""></script><meta name="robots" content="noindex"/><title>404: This page could not be found.</title><meta name="theme-color" content="#3b82f6"/><script>
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\b(light|dark)\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\b(light|dark)\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\b(light|dark)\b/g, '').trim() + ' light';
                }
              })();
            </script><title>PeopleNest - Enterprise HRMS Platform</title><meta name="description" content="Modern, AI-powered Human Resource Management System for enterprise organizations"/><link rel="manifest" href="/manifest.json"/><meta name="keywords" content="HRMS,HR,Human Resources,Employee Management,Payroll,Performance"/><meta name="mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-status-bar-style" content="default"/><meta name="apple-mobile-web-app-title" content="PeopleNest"/><meta name="application-name" content="PeopleNest HRMS"/><meta name="msapplication-TileColor" content="#3b82f6"/><meta name="msapplication-config" content="/browserconfig.xml"/><meta name="format-detection" content="telephone=no"/><meta name="mobile-web-app-capable" content="yes"/><meta name="apple-mobile-web-app-title" content="PeopleNest HRMS"/><meta name="apple-mobile-web-app-status-bar-style" content="default"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_e8ce0c __variable_3c557b font-sans antialiased touch-manipulation"><div hidden=""><!--$--><!--/$--></div><script>
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\b(light|dark)\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\b(light|dark)\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\b(light|dark)\b/g, '').trim() + ' light';
                }
              })();
            </script><div style="font-family:system-ui,&quot;Segoe UI&quot;,Roboto,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;;height:100vh;text-align:center;display:flex;flex-direction:column;align-items:center;justify-content:center"><div><style>body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}</style><h1 class="next-error-h1" style="display:inline-block;margin:0 20px 0 0;padding:0 23px 0 0;font-size:24px;font-weight:500;vertical-align:top;line-height:49px">404</h1><div style="display:inline-block"><h2 style="font-size:14px;font-weight:400;line-height:49px;margin:0">This page could not be found.</h2></div></div></div><!--$--><!--/$--><script>
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            </script><script src="/_next/static/chunks/webpack-101e750c36e44303.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[77890,[\"7177\",\"static/chunks/app/layout-c92ee0b72061c2db.js\"],\"ThemeProvider\"]\n3:I[87555,[],\"\"]\n4:I[31295,[],\"\"]\n5:I[59665,[],\"OutletBoundary\"]\n8:I[74911,[],\"AsyncMetadataOutlet\"]\na:I[59665,[],\"ViewportBoundary\"]\nc:I[59665,[],\"MetadataBoundary\"]\ne:I[26614,[],\"\"]\n:HL[\"/_next/static/css/56cc0dd3233d2018.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"pDhezjYFuvgxkHqbZem0i\",\"p\":\"\",\"c\":[\"\",\"_not-found\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"/_not-found\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/56cc0dd3233d2018.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{\"children\":[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"\\n              (function() {\\n                try {\\n                  var theme = localStorage.getItem('theme-mode') || 'light';\\n                  var root = document.documentElement;\\n\\n                  // Apply theme immediately to prevent hydration mismatch\\n                  if (theme === 'system') {\\n                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + systemTheme;\\n                  } else {\\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + theme;\\n                  }\\n                } catch (e) {\\n                  // Fallback to light theme if anything goes wrong\\n                  document.documentElement.className = document.documentElement.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' light';\\n                }\\n              })();\\n            \"}}]}],[\"$\",\"body\",null,{\"className\":\"__variable_e8ce0c __variable_3c557b font-sans antialiased touch-manipulation\",\"children\":[[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"\\n              (function() {\\n                try {\\n                  var theme = localStorage.getItem('theme-mode') || 'light';\\n                  var root = document.documentElement;\\n\\n                  // Apply theme immediately to prevent hydration mismatch\\n                  if (theme === 'system') {\\n                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + systemTheme;\\n                  } else {\\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + theme;\\n                  }\\n                } catch (e) {\\n                  // Fallback to light theme if anything goes wrong\\n                  document.documentElement.className = document.documentElement.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' light';\\n                }\\n              })();\\n            \"}}],[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"script\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"\\n              if ('serviceWorker' in navigator) {\\n                window.addEventListener('load', function() {\\n                  navigator.serviceWorker.register('/sw.js')\\n                    .then(function(registration) {\\n                      console.log('SW registered: ', registration);\\n                    })\\n                    .catch(function(registrationError) {\\n                      console.log('SW registration failed: ', registrationError);\\n                    });\\n                });\\n              }\\n            \"}}]]}]]}]]}],{\"children\":[\"/_not-found\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:1:props:children:1:props:children:props:notFound:0:1:props:style\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":\"$0:f:0:1:1:props:children:1:props:children:1:props:children:1:props:children:props:notFound:0:1:props:children:props:children:1:props:style\",\"children\":404}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:1:props:children:1:props:children:props:notFound:0:1:props:children:props:children:2:props:style\",\"children\":[\"$\",\"h2\",null,{\"style\":\"$0:f:0:1:1:props:children:1:props:children:1:props:children:1:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style\",\"children\":\"This page could not be found.\"}]}]]}]}]],null,[\"$\",\"$L5\",null,{\"children\":[\"$L6\",\"$L7\",[\"$\",\"$L8\",null,{\"promise\":\"$@9\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[[\"$\",\"meta\",null,{\"name\":\"robots\",\"content\":\"noindex\"}],[\"$\",\"$1\",\"FcZVjKDrcoUp0IM40SqfLv\",{\"children\":[[\"$\",\"$La\",null,{\"children\":\"$Lb\"}],null]}],[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$e\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"f:\"$Sreact.suspense\"\n10:I[74911,[],\"AsyncMetadata\"]\nd:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$f\",null,{\"fallback\":null,\"children\":[\"$\",\"$L10\",null,{\"promise\":\"$@11\"}]}]}]\n7:null\n"])</script><script>self.__next_f.push([1,"b:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1, maximum-scale=1, viewport-fit=cover, user-scalable=no\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#3b82f6\"}]]\n6:null\n"])</script><script>self.__next_f.push([1,"9:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"PeopleNest - Enterprise HRMS Platform\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Modern, AI-powered Human Resource Management System for enterprise organizations\"}],[\"$\",\"link\",\"2\",{\"rel\":\"manifest\",\"href\":\"/manifest.json\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"HRMS,HR,Human Resources,Employee Management,Payroll,Performance\"}],[\"$\",\"meta\",\"4\",{\"name\":\"mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",\"5\",{\"name\":\"apple-mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",\"6\",{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"default\"}],[\"$\",\"meta\",\"7\",{\"name\":\"apple-mobile-web-app-title\",\"content\":\"PeopleNest\"}],[\"$\",\"meta\",\"8\",{\"name\":\"application-name\",\"content\":\"PeopleNest HRMS\"}],[\"$\",\"meta\",\"9\",{\"name\":\"msapplication-TileColor\",\"content\":\"#3b82f6\"}],[\"$\",\"meta\",\"10\",{\"name\":\"msapplication-config\",\"content\":\"/browserconfig.xml\"}],[\"$\",\"meta\",\"11\",{\"name\":\"format-detection\",\"content\":\"telephone=no\"}],[\"$\",\"meta\",\"12\",{\"name\":\"mobile-web-app-capable\",\"content\":\"yes\"}],[\"$\",\"meta\",\"13\",{\"name\":\"apple-mobile-web-app-title\",\"content\":\"PeopleNest HRMS\"}],[\"$\",\"meta\",\"14\",{\"name\":\"apple-mobile-web-app-status-bar-style\",\"content\":\"default\"}]],\"error\":null,\"digest\":\"$undefined\"}\n11:{\"metadata\":\"$9:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>