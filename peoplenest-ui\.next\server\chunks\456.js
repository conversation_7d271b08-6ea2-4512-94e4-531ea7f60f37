exports.id=456,exports.ids=[456],exports.modules={15079:(e,a,s)=>{"use strict";s.d(a,{bq:()=>x,eb:()=>p,gC:()=>f,l6:()=>c,yv:()=>m});var t=s(60687),r=s(43210),n=s(42133),i=s(78272),l=s(3589),d=s(13964),o=s(4780);let c=n.bL;n.YJ;let m=n.WT,x=r.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(n.l9,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[a,(0,t.jsx)(n.In,{asChild:!0,children:(0,t.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=n.l9.displayName;let h=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(n.PP,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(l.A,{className:"h-4 w-4"})}));h.displayName=n.PP.displayName;let u=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(n.wn,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=n.wn.displayName;let f=r.forwardRef(({className:e,children:a,position:s="popper",...r},i)=>(0,t.jsx)(n.ZL,{children:(0,t.jsxs)(n.UC,{ref:i,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,t.jsx)(h,{}),(0,t.jsx)(n.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,t.jsx)(u,{})]})}));f.displayName=n.UC.displayName,r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(n.JU,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=n.JU.displayName;let p=r.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(n.q7,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(n.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(n.p4,{children:a})]}));p.displayName=n.q7.displayName,r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(n.wv,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=n.wv.displayName},15487:(e,a,s)=>{Promise.resolve().then(s.bind(s,60008))},32584:(e,a,s)=>{"use strict";s.d(a,{BK:()=>o,eu:()=>d,q5:()=>c});var t=s(60687),r=s(43210),n=s(11096),i=s(4780);let l=(0,s(24224).F)("relative flex shrink-0 overflow-hidden rounded-full",{variants:{size:{sm:"h-8 w-8",default:"h-10 w-10",lg:"h-12 w-12",xl:"h-16 w-16","2xl":"h-20 w-20"}},defaultVariants:{size:"default"}}),d=r.forwardRef(({className:e,size:a,src:s,alt:r,fallback:d,status:m,...x},h)=>(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(n.bL,{ref:h,className:(0,i.cn)(l({size:a}),e),...x,children:[(0,t.jsx)(o,{src:s,alt:r}),(0,t.jsx)(c,{children:d})]}),m&&(0,t.jsx)("div",{className:(0,i.cn)("absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-white",{"bg-green-500":"online"===m,"bg-muted-foreground":"offline"===m,"bg-yellow-500":"away"===m,"bg-red-500":"busy"===m})})]}));d.displayName=n.bL.displayName;let o=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(n._V,{ref:s,className:(0,i.cn)("aspect-square h-full w-full",e),...a}));o.displayName=n._V.displayName;let c=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(n.H4,{ref:s,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted text-sm font-medium",e),...a}));c.displayName=n.H4.displayName},60008:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>I});var t=s(60687),r=s(43210),n=s(16189),i=s(85814),l=s.n(i),d=s(50371),o=s(88920),c=s(49625),m=s(53411),x=s(41312),h=s(58869),u=s(86561),f=s(79410),p=s(57800),g=s(1994),b=s(23928),j=s(25541),N=s(48730),y=s(40228),v=s(58887),w=s(10022),A=s(84027),k=s(99891),_=s(44709),C=s(17313),P=s(14952),R=s(47033),S=s(40083),z=s(4780),E=s(29523),F=s(32584),M=s(96834),$=s(78010),D=s(40565);let H=e=>[{title:"Overview",items:[{name:"Dashboard",icon:c.A,href:"/dashboard",badge:null},{name:"Analytics",icon:m.A,href:"/dashboard/analytics",badge:null}]},{title:"People Management",items:[...e.canReadAllEmployees()?[{name:"Employees",icon:x.A,href:"/dashboard/employees",badge:"124"}]:[],...e.canAccessHRModule()?[{name:"Recruitment",icon:h.A,href:"/dashboard/recruitment",badge:"12"},{name:"Onboarding",icon:u.A,href:"/dashboard/onboarding",badge:"3"}]:[]].filter(e=>e)},...e.canAccessOrganizationModule()?[{title:"Organization",items:[...e.canReadDepartments()?[{name:"Departments",icon:f.A,href:"/dashboard/organization/departments",badge:null}]:[],...e.canReadPositions()?[{name:"Positions",icon:p.A,href:"/dashboard/organization/positions",badge:null}]:[],{name:"Org Chart",icon:g.A,href:"/dashboard/organization/chart",badge:null}].filter(e=>e)}]:[],...e.canAccessHRModule()?[{title:"Operations",items:[{name:"Payroll",icon:b.A,href:"/dashboard/payroll",badge:null},{name:"Performance",icon:j.A,href:"/dashboard/performance",badge:"5"},{name:"Time & Attendance",icon:N.A,href:"/dashboard/attendance",badge:"2"},{name:"Leave Management",icon:y.A,href:"/dashboard/leave",badge:"8"}]}]:[],{title:"Communication",items:[{name:"Announcements",icon:v.A,href:"/dashboard/announcements",badge:"2"},{name:"Documents",icon:w.A,href:"/dashboard/documents",badge:null}]},{title:"System",items:[{name:"Settings",icon:A.A,href:"/dashboard/settings",badge:null},...e.canAccessAdminModule()?[{name:"Compliance",icon:k.A,href:"/dashboard/compliance",badge:null}]:[],{name:"Help & Support",icon:_.A,href:"/dashboard/help",badge:null}].filter(e=>e)}].filter(e=>e.items&&e.items.length>0);function L({className:e}){let[a,s]=(0,r.useState)(!1),i=(0,n.usePathname)(),c=(0,$.Sk)(),{logout:m}=(0,D.A)(),x=H(c);return(0,t.jsxs)(d.P.div,{initial:!1,animate:{width:a?80:280},transition:{duration:.3,ease:"easeInOut"},className:(0,z.cn)("relative flex flex-col bg-card border-r border-border shadow-sm",e),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-border",children:[(0,t.jsx)(o.N,{mode:"wait",children:!a&&(0,t.jsxs)(d.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-primary rounded-lg",children:(0,t.jsx)(C.A,{className:"w-5 h-5 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"HRMS Platform"})]})]})}),(0,t.jsx)(E.$,{variant:"ghost",size:"icon",onClick:()=>s(!a),className:"h-8 w-8 text-muted-foreground hover:text-foreground",children:a?(0,t.jsx)(P.A,{className:"h-4 w-4"}):(0,t.jsx)(R.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto py-4",children:(0,t.jsx)("nav",{className:"space-y-6 px-3",children:x.map((e,s)=>(0,t.jsxs)("div",{children:[(0,t.jsx)(o.N,{children:!a&&(0,t.jsx)(d.P.h3,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:e.title})}),(0,t.jsx)("ul",{className:"space-y-1",children:e.items.map(e=>{let s=i===e.href;return(0,t.jsx)("li",{children:(0,t.jsxs)(l(),{href:e.href,className:(0,z.cn)("w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",s?"bg-primary text-primary-foreground shadow-sm":"text-foreground hover:bg-muted hover:text-foreground"),children:[(0,t.jsx)(e.icon,{className:(0,z.cn)("flex-shrink-0 w-5 h-5",a?"mx-auto":"mr-3")}),(0,t.jsx)(o.N,{children:!a&&(0,t.jsxs)(d.P.div,{initial:{opacity:0,width:0},animate:{opacity:1,width:"auto"},exit:{opacity:0,width:0},className:"flex items-center justify-between flex-1 min-w-0",children:[(0,t.jsx)("span",{className:"truncate",children:e.name}),e.badge&&(0,t.jsx)(M.E,{variant:s?"secondary":"outline",className:"ml-2 text-xs",children:e.badge})]})})]})},e.name)})})]},e.title))})}),(0,t.jsx)("div",{className:"border-t border-border p-4",children:(0,t.jsxs)("div",{className:(0,z.cn)("flex items-center",a?"justify-center":"space-x-3"),children:[(0,t.jsxs)(F.eu,{className:"h-8 w-8",children:[(0,t.jsx)(F.BK,{src:"/avatars/user.jpg",alt:"User"}),(0,t.jsx)(F.q5,{children:"JD"})]}),(0,t.jsx)(o.N,{children:!a&&(0,t.jsxs)(d.P.div,{initial:{opacity:0,width:0},animate:{opacity:1,width:"auto"},exit:{opacity:0,width:0},className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-foreground truncate",children:"John Doe"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:"HR Manager"})]})}),(0,t.jsx)(o.N,{children:!a&&(0,t.jsx)(d.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,t.jsx)(E.$,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:m,title:"Sign out",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})})})})]})})]})}var U=s(99270),O=s(97051),q=s(12941),J=s(32192),V=s(11860);let B=[{name:"Dashboard",icon:c.A,href:"/dashboard",badge:null},{name:"Employees",icon:x.A,href:"/dashboard/employees",badge:"124"},{name:"Payroll",icon:b.A,href:"/dashboard/payroll",badge:null},{name:"Performance",icon:j.A,href:"/dashboard/performance",badge:"5"},{name:"Leave",icon:y.A,href:"/dashboard/leave",badge:"8"},{name:"Settings",icon:A.A,href:"/dashboard/settings",badge:null}],T=[{name:"Search",icon:U.A,action:"search"},{name:"Notifications",icon:O.A,action:"notifications",badge:"3"},{name:"Profile",icon:h.A,action:"profile"}];function W({className:e}){let[a,s]=(0,r.useState)(!1),[i,c]=(0,r.useState)("/dashboard");(0,n.usePathname)();let m=e=>{switch(e){case"search":console.log("Search triggered");break;case"notifications":console.log("Notifications triggered");break;case"profile":console.log("Profile triggered")}s(!1)};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"lg:hidden fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 px-4 py-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(E.$,{variant:"ghost",size:"icon",onClick:()=>s(!0),className:"h-10 w-10",children:(0,t.jsx)(q.A,{className:"h-6 w-6"})}),(0,t.jsx)("div",{children:(0,t.jsx)("h1",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"})})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:T.map(e=>(0,t.jsxs)(E.$,{variant:"ghost",size:"icon",onClick:()=>m(e.action),className:"h-10 w-10 relative",children:[(0,t.jsx)(e.icon,{className:"h-5 w-5"}),e.badge&&(0,t.jsx)(M.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs",children:e.badge})]},e.name))})]})}),(0,t.jsx)(o.N,{children:a&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:()=>s(!1),className:"lg:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"}),(0,t.jsx)(d.P.div,{initial:{x:"-100%"},animate:{x:0},exit:{x:"-100%"},transition:{type:"spring",damping:30,stiffness:300},className:"lg:hidden fixed top-0 left-0 bottom-0 z-50 w-80 bg-card shadow-xl",children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-border",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-primary rounded-lg flex items-center justify-center",children:(0,t.jsx)(J.A,{className:"h-6 w-6 text-primary-foreground"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-foreground",children:"PeopleNest"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"HRMS Dashboard"})]})]}),(0,t.jsx)(E.$,{variant:"ghost",size:"icon",onClick:()=>s(!1),className:"h-10 w-10",children:(0,t.jsx)(V.A,{className:"h-6 w-6"})})]}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto py-6",children:(0,t.jsx)("nav",{className:"px-6 space-y-2",children:B.map((e,a)=>{let s=i===e.href;return(0,t.jsx)(d.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*a},children:(0,t.jsxs)(l(),{href:e.href,onClick:()=>{c(e.href)},className:`
                              flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200
                              ${s?"bg-primary/10 text-primary border border-primary/20":"text-muted-foreground hover:bg-muted hover:text-foreground"}
                            `,children:[(0,t.jsx)(e.icon,{className:`h-5 w-5 ${s?"text-primary":"text-muted-foreground"}`}),(0,t.jsx)("span",{className:"font-medium",children:e.name}),e.badge&&(0,t.jsx)(M.E,{variant:s?"default":"secondary",className:"ml-auto",children:e.badge})]})},e.name)})})}),(0,t.jsx)("div",{className:"p-6 border-t border-border",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-muted rounded-lg",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-muted-foreground/20 rounded-full flex items-center justify-center",children:(0,t.jsx)(h.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-foreground",children:"John Doe"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"HR Manager"})]})]})})]})})]})}),(0,t.jsx)("div",{className:"lg:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 px-2 py-2",children:(0,t.jsx)("div",{className:"flex items-center justify-around",children:B.slice(0,4).map(e=>{let a=i===e.href;return(0,t.jsxs)(l(),{href:e.href,onClick:()=>{console.log("Bottom nav link clicked:",e.href),c(e.href)},className:`
                  flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-all duration-200 relative
                  ${a?"text-primary":"text-muted-foreground hover:text-foreground"}
                `,children:[(0,t.jsx)(e.icon,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"text-xs font-medium",children:e.name}),e.badge&&(0,t.jsx)(M.E,{variant:"destructive",className:"absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs",children:e.badge}),a&&(0,t.jsx)(d.P.div,{layoutId:"activeTab",className:"absolute -top-2 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"})]},e.name)})})})]})}function I({children:e}){return(0,t.jsx)(D.O,{children:(0,t.jsxs)("div",{className:"flex h-screen bg-background",children:[(0,t.jsx)(L,{className:"hidden lg:flex"}),(0,t.jsx)(W,{}),(0,t.jsx)("div",{className:"flex-1 flex flex-col overflow-hidden",children:(0,t.jsx)("main",{className:"flex-1 overflow-y-auto pt-16 lg:pt-0 pb-16 lg:pb-0",children:e})})]})})}},63144:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\dashboard\\layout.tsx","default")},63503:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>h,L3:()=>u,c7:()=>x,lG:()=>d,rr:()=>f});var t=s(60687),r=s(43210),n=s(26134),i=s(11860),l=s(4780);let d=n.bL;n.l9;let o=n.ZL;n.bm;let c=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(n.hJ,{ref:s,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));c.displayName=n.hJ.displayName;let m=r.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(o,{children:[(0,t.jsx)(c,{}),(0,t.jsxs)(n.UC,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[a,(0,t.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(i.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=n.UC.displayName;let x=({className:e,...a})=>(0,t.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});x.displayName="DialogHeader";let h=({className:e,...a})=>(0,t.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});h.displayName="DialogFooter";let u=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(n.hE,{ref:s,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));u.displayName=n.hE.displayName;let f=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(n.VY,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...a}));f.displayName=n.VY.displayName},78010:(e,a,s)=>{"use strict";s.d(a,{LQ:()=>i,Sk:()=>l});var t=s(60687);s(43210);var r=s(40565),n=s(63523);function i({children:e,permission:a,permissions:s,requireAll:i=!1,role:l,roles:d,minRole:o,fallback:c=null,condition:m}){let{user:x,isAuthenticated:h}=(0,r.A)();return h&&x?m?m(x)?(0,t.jsx)(t.Fragment,{children:e}):(0,t.jsx)(t.Fragment,{children:c}):a?(0,n._m)(x,a)?(0,t.jsx)(t.Fragment,{children:e}):(0,t.jsx)(t.Fragment,{children:c}):s&&s.length>0?(i?s.every(e=>(0,n._m)(x,e)):(0,n.sx)(x,s))?(0,t.jsx)(t.Fragment,{children:e}):(0,t.jsx)(t.Fragment,{children:c}):l?(0,n.hf)(x,l)?(0,t.jsx)(t.Fragment,{children:e}):(0,t.jsx)(t.Fragment,{children:c}):d&&d.length>0?(0,n.pX)(x,d)?(0,t.jsx)(t.Fragment,{children:e}):(0,t.jsx)(t.Fragment,{children:c}):o?(0,n.BU)(x,o)?(0,t.jsx)(t.Fragment,{children:e}):(0,t.jsx)(t.Fragment,{children:c}):(0,t.jsx)(t.Fragment,{children:e}):(0,t.jsx)(t.Fragment,{children:c})}function l(){let{user:e,isAuthenticated:a}=(0,r.A)();return{user:e,isAuthenticated:a,hasPermission:a=>(0,n._m)(e,a),hasAnyPermission:a=>(0,n.sx)(e,a),hasRole:a=>(0,n.hf)(e,a),hasAnyRole:a=>(0,n.pX)(e,a),isRoleAtLeast:a=>(0,n.BU)(e,a),canReadDepartments:()=>(0,n.sx)(e,["department_read","hr","hr_admin","super_admin"]),canWriteDepartments:()=>(0,n.sx)(e,["department_write","hr_admin","super_admin"]),canReadPositions:()=>(0,n.sx)(e,["position_read","hr","hr_admin","super_admin"]),canWritePositions:()=>(0,n.sx)(e,["position_write","hr_admin","super_admin"]),canReadAllEmployees:()=>(0,n.sx)(e,["employee_read_all","hr","hr_admin","super_admin"]),canWriteAllEmployees:()=>(0,n.sx)(e,["employee_update_all","hr","hr_admin","super_admin"]),canManageUsers:()=>(0,n.sx)(e,["user_management","hr_admin","super_admin"]),canAccessOrganizationModule:()=>(0,n.sx)(e,["department_read","position_read","hr","hr_admin","super_admin"]),canAccessHRModule:()=>(0,n.sx)(e,["hr","hr_admin","super_admin"]),canAccessAdminModule:()=>(0,n.sx)(e,["hr_admin","super_admin"])}}},85223:(e,a,s)=>{Promise.resolve().then(s.bind(s,63144))},96724:(e,a,s)=>{"use strict";s.d(a,{Y:()=>U});var t=s(60687),r=s(43210),n=s(88920),i=s(50371),l=s(96474),d=s(31158),o=s(78122),c=s(80462),m=s(99270),x=s(97051),h=s(58887),u=s(363),f=s(21134),p=s(78272),g=s(58869),b=s(99891),j=s(84027),N=s(11437),y=s(40083),v=s(29523),w=s(89667),A=s(32584),k=s(96834),_=s(44493),C=s(4780);function P(e){return e&&({super_admin:"Super Admin",system_admin:"System Admin",hr_admin:"HR Admin",hr:"HR",manager:"Manager",employee:"Employee",recruiter:"Recruiter",payroll:"Payroll",it:"IT"})[e]||"Employee"}function R(e){return{text:P(e),className:function(e){let a={super_admin:"text-red-600 bg-red-500/10 border-red-500/20 dark:text-red-400",system_admin:"text-purple-600 bg-purple-500/10 border-purple-500/20 dark:text-purple-400",hr_admin:"text-blue-600 bg-blue-500/10 border-blue-500/20 dark:text-blue-400",hr:"text-green-600 bg-green-500/10 border-green-500/20 dark:text-green-400",manager:"text-orange-600 bg-orange-500/10 border-orange-500/20 dark:text-orange-400",employee:"text-muted-foreground bg-muted border-border",recruiter:"text-indigo-600 bg-indigo-500/10 border-indigo-500/20 dark:text-indigo-400",payroll:"text-yellow-600 bg-yellow-500/10 border-yellow-500/20 dark:text-yellow-400",it:"text-cyan-600 bg-cyan-500/10 border-cyan-500/20 dark:text-cyan-400"};return e&&a[e]||a.employee}(e)}}var S=s(35862),z=s(40565),E=s(63503),F=s(15079),M=s(92363),$=s(41312),D=s(57800);let H=[{value:"super_admin",label:"Super Admin",description:"Full system access with all permissions",icon:(0,t.jsx)(M.A,{className:"h-4 w-4"})},{value:"system_admin",label:"System Admin",description:"System administration and configuration",icon:(0,t.jsx)(j.A,{className:"h-4 w-4"})},{value:"hr_admin",label:"HR Admin",description:"HR management and employee administration",icon:(0,t.jsx)(b.A,{className:"h-4 w-4"})},{value:"hr",label:"HR",description:"Human resources operations",icon:(0,t.jsx)($.A,{className:"h-4 w-4"})},{value:"manager",label:"Manager",description:"Team and department management",icon:(0,t.jsx)(D.A,{className:"h-4 w-4"})},{value:"employee",label:"Employee",description:"Standard employee access",icon:(0,t.jsx)(g.A,{className:"h-4 w-4"})}];function L({isOpen:e,onClose:a}){var s;let{user:n,updateUser:l}=(0,z.A)(),[d,o]=(0,r.useState)(n?.role||"employee"),[c,m]=(0,r.useState)(!1),x=async()=>{if(!n||d===n.role)return void a();m(!0);try{let e={...n,role:d};l(e),await new Promise(e=>setTimeout(e,1e3)),a()}catch(e){console.error("Failed to change role:",e)}finally{m(!1)}},h=R(n?.role),u=R(d);return(0,t.jsx)(E.lG,{open:e,onOpenChange:a,children:(0,t.jsxs)(E.Cf,{className:"sm:max-w-md",children:[(0,t.jsxs)(E.c7,{children:[(0,t.jsxs)(E.L3,{className:"flex items-center space-x-2",children:[(0,t.jsx)(b.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Role Management"})]}),(0,t.jsx)(E.rr,{children:"Change your current role for testing different permission levels"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Current Role"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.E,{variant:"outline",className:`px-3 py-1 ${h.className}`,children:h.text}),!!(s=n?.role)&&["super_admin","system_admin","hr_admin"].includes(s)&&(0,t.jsx)(k.E,{variant:"secondary",className:"text-xs",children:"Admin Level"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Select New Role"}),(0,t.jsxs)(F.l6,{value:d,onValueChange:e=>o(e),children:[(0,t.jsx)(F.bq,{children:(0,t.jsx)(F.yv,{placeholder:"Select a role"})}),(0,t.jsx)(F.gC,{children:H.map(e=>(0,t.jsx)(F.eb,{value:e.value,children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[e.icon,(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.label}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})]})},e.value))})]})]}),d!==n?.role&&(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"p-3 bg-muted rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium mb-2",children:"Preview:"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm",children:"You will appear as:"}),(0,t.jsx)(k.E,{variant:"outline",className:`px-2 py-1 ${u.className}`,children:u.text})]})]})]}),(0,t.jsxs)(E.Es,{children:[(0,t.jsx)(v.$,{variant:"outline",onClick:a,disabled:c,children:"Cancel"}),(0,t.jsx)(v.$,{onClick:x,disabled:c||d===n?.role,children:c?"Changing...":"Change Role"})]})]})})}function U({title:e,subtitle:a,actions:s,className:R}){let[E,F]=(0,r.useState)(!1),[M,$]=(0,r.useState)(!1),[D,H]=(0,r.useState)(!1),[U,O]=(0,r.useState)(""),{theme:q,toggleTheme:J,actualTheme:V}=(0,S.D)(),{logout:B,user:T}=(0,z.A)(),W=[{id:1,title:"New Employee Onboarding",message:"Sarah Johnson has completed her onboarding checklist",time:"2 minutes ago",unread:!0,type:"success"},{id:2,title:"Leave Request Pending",message:"Mike Chen has requested 3 days of annual leave",time:"1 hour ago",unread:!0,type:"warning"},{id:3,title:"Performance Review Due",message:"5 performance reviews are due this week",time:"3 hours ago",unread:!1,type:"info"}],I=[{name:"Add Employee",icon:l.A,action:()=>{}},{name:"Export Data",icon:d.A,action:()=>{}},{name:"Refresh",icon:o.A,action:()=>{}},{name:"Filter",icon:c.A,action:()=>{}}];return(0,t.jsxs)("header",{className:(0,C.cn)("sticky top-0 z-40 w-full border-b border-border bg-background/80 backdrop-blur-sm",R),children:[(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between px-6",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("div",{children:[e&&(0,t.jsx)("h1",{className:"text-xl font-semibold text-foreground",children:e}),a&&(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:a})]})}),(0,t.jsx)("div",{className:"flex-1 max-w-md mx-8",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(w.p,{type:"text",placeholder:"Search employees, documents, or anything...",value:U,onChange:e=>O(e.target.value),className:"pl-10 pr-4 w-full bg-muted border-border focus:bg-background transition-colors"})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[s&&(0,t.jsx)("div",{className:"flex items-center space-x-2 mr-4",children:s}),(0,t.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:I.map(e=>(0,t.jsx)(v.$,{variant:"ghost",size:"icon",onClick:e.action,className:"h-9 w-9 text-muted-foreground hover:text-foreground",title:e.name,children:(0,t.jsx)(e.icon,{className:"h-4 w-4"})},e.name))}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(v.$,{variant:"ghost",size:"icon",onClick:()=>F(!E),className:"h-9 w-9 text-muted-foreground hover:text-foreground relative",children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),W.some(e=>e.unread)&&(0,t.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full"})]}),(0,t.jsx)(n.N,{children:E&&(0,t.jsx)(i.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute right-0 mt-2 w-80 z-50",children:(0,t.jsx)(_.Zp,{className:"shadow-lg border-border",children:(0,t.jsxs)(_.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"p-4 border-b border-border",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"font-semibold text-foreground",children:"Notifications"}),(0,t.jsxs)(k.E,{variant:"secondary",className:"text-xs",children:[W.filter(e=>e.unread).length," new"]})]})}),(0,t.jsx)("div",{className:"max-h-80 overflow-y-auto",children:W.map(e=>(0,t.jsx)("div",{className:(0,C.cn)("p-4 border-b border-border hover:bg-muted cursor-pointer transition-colors",e.unread&&"bg-primary/5"),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:(0,C.cn)("w-2 h-2 rounded-full mt-2 flex-shrink-0","success"===e.type&&"bg-green-500","warning"===e.type&&"bg-yellow-500","info"===e.type&&"bg-primary")}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-foreground",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.message}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground/70 mt-2",children:e.time})]})]})},e.id))}),(0,t.jsx)("div",{className:"p-3 border-t border-border",children:(0,t.jsx)(v.$,{variant:"ghost",className:"w-full text-sm",children:"View all notifications"})})]})})})})]}),(0,t.jsx)(v.$,{variant:"ghost",size:"icon",className:"h-9 w-9 text-muted-foreground hover:text-foreground",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})}),(0,t.jsx)(v.$,{variant:"ghost",size:"icon",onClick:J,className:"h-9 w-9 text-muted-foreground hover:text-foreground",title:`Switch to ${"light"===V?"dark":"light"} mode`,children:"light"===V?(0,t.jsx)(u.A,{className:"h-4 w-4"}):(0,t.jsx)(f.A,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(v.$,{variant:"ghost",onClick:()=>$(!M),className:"flex items-center space-x-2 h-9 px-3 text-foreground hover:text-foreground/80",children:[(0,t.jsxs)(A.eu,{className:"h-7 w-7",children:[(0,t.jsx)(A.BK,{src:"/avatars/user.jpg",alt:T?.firstName||"User"}),(0,t.jsxs)(A.q5,{children:[T?.firstName?.[0],T?.lastName?.[0]||"U"]})]}),(0,t.jsx)(p.A,{className:"h-3 w-3"})]}),(0,t.jsx)(n.N,{children:M&&(0,t.jsx)(i.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute right-0 mt-2 w-56 z-50",children:(0,t.jsx)(_.Zp,{className:"shadow-lg border-border",children:(0,t.jsxs)(_.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"p-4 border-b border-border",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(A.eu,{className:"h-10 w-10",children:[(0,t.jsx)(A.BK,{src:"/avatars/user.jpg",alt:T?.firstName||"User"}),(0,t.jsxs)(A.q5,{children:[T?.firstName?.[0],T?.lastName?.[0]||"U"]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"font-medium text-foreground",children:[T?.firstName," ",T?.lastName]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:P(T?.role)})]})]})}),(0,t.jsxs)("div",{className:"py-2",children:[[{icon:g.A,label:"Profile",href:"/dashboard",action:()=>{}},{icon:b.A,label:"Change Role",href:"#",action:()=>H(!0)},{icon:j.A,label:"Settings",href:"/dashboard/settings",action:()=>{}},{icon:N.A,label:"Language",href:"#",action:()=>{}}].map(e=>(0,t.jsxs)("button",{type:"button",onClick:e.action,className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors",children:[(0,t.jsx)(e.icon,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.label})]},e.label)),(0,t.jsxs)("button",{type:"button",onClick:J,className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors",children:["light"===V?(0,t.jsx)(u.A,{className:"h-4 w-4"}):(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"light"===V?"Dark Mode":"Light Mode"})]}),(0,t.jsx)("hr",{className:"my-2"}),(0,t.jsxs)("button",{type:"button",onClick:B,className:"w-full flex items-center space-x-3 px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Sign out"})]})]})]})})})})]})]})]}),(0,t.jsx)(L,{isOpen:D,onClose:()=>H(!1)})]})}}};