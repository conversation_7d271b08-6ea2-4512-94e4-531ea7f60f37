(()=>{var e={};e.id=974,e.ids=[974],e.modules={3216:(e,t,r)=>{Promise.resolve().then(r.bind(r,5388))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4148:(e,t,r)=>{"use strict";r.d(t,{AZ:()=>n,OD:()=>i,fu:()=>s,zS:()=>o});function o(){return"system"}function s(e){}function i(){let e=o()}function n(e){}},5388:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});var o=r(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\components\\providers\\theme-provider.tsx","useTheme");let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\components\\providers\\theme-provider.tsx","ThemeProvider")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12944:(e,t,r)=>{Promise.resolve().then(r.bind(r,35862))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19731:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},21204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\app\\page.tsx","default")},22396:(e,t,r)=>{Promise.resolve().then(r.bind(r,75694))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33218:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>m});var o=r(65239),s=r(48088),i=r(88170),n=r.n(i),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let m={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\PeopleNest\\peoplenest-ui\\src\\app\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})},33873:e=>{"use strict";e.exports=require("path")},35862:(e,t,r)=>{"use strict";r.d(t,{D:()=>a,ThemeProvider:()=>l});var o=r(60687),s=r(43210),i=r(4148);let n=(0,s.createContext)(void 0);function a(){let e=(0,s.useContext)(n);return void 0===e?{theme:"system",setTheme:()=>{},toggleTheme:()=>{},systemTheme:"light",actualTheme:"light"}:e}function l({children:e,defaultTheme:t="light"}){let[r,a]=(0,s.useState)(t),[l,m]=(0,s.useState)("light"),[d,c]=(0,s.useState)(!1),p=e=>{a(e),(0,i.fu)(e),(0,i.AZ)(e)};return(0,o.jsx)(n.Provider,{value:{theme:r,setTheme:p,toggleTheme:()=>{p("light"===("system"===r?l:r)?"dark":"light")},systemTheme:l,actualTheme:"system"===r?l:r},children:e})}},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64252:(e,t,r)=>{Promise.resolve().then(r.bind(r,21204))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var o=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},75694:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var o=r(60687);r(43210);var s=r(16189),i=r(50371),n=r(17313),a=r(41862);function l(){return(0,s.useRouter)(),(0,o.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-primary/5 via-background to-primary/10 flex items-center justify-center",children:(0,o.jsxs)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.5},className:"text-center",children:[(0,o.jsx)(i.P.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2,duration:.3},className:"inline-flex items-center justify-center w-20 h-20 bg-primary rounded-3xl mb-6 shadow-lg",children:(0,o.jsx)(n.A,{className:"w-10 h-10 text-primary-foreground"})}),(0,o.jsx)(i.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4,duration:.3},className:"text-4xl font-bold text-foreground mb-4",children:"PeopleNest"}),(0,o.jsx)(i.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6,duration:.3},className:"text-xl text-muted-foreground mb-8",children:"Enterprise HRMS Platform"}),(0,o.jsxs)(i.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8,duration:.3},className:"flex items-center justify-center space-x-2 text-muted-foreground",children:[(0,o.jsx)(a.A,{className:"w-5 h-5 animate-spin"}),(0,o.jsx)("span",{children:"Loading your workspace..."})]})]})})}},79551:e=>{"use strict";e.exports=require("url")},82939:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>m,viewport:()=>d});var o=r(37413),s=r(58745),i=r.n(s),n=r(64066),a=r.n(n);r(61135);var l=r(5388);let m={title:"PeopleNest - Enterprise HRMS Platform",description:"Modern, AI-powered Human Resource Management System for enterprise organizations",keywords:["HRMS","HR","Human Resources","Employee Management","Payroll","Performance"],manifest:"/manifest.json",appleWebApp:{capable:!0,statusBarStyle:"default",title:"PeopleNest HRMS"},formatDetection:{telephone:!1},other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","apple-mobile-web-app-status-bar-style":"default","apple-mobile-web-app-title":"PeopleNest","application-name":"PeopleNest HRMS","msapplication-TileColor":"#3b82f6","msapplication-config":"/browserconfig.xml"}},d={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,viewportFit:"cover",themeColor:"#3b82f6"};function c({children:e}){return(0,o.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,o.jsx)("head",{children:(0,o.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' light';
                }
              })();
            `}})}),(0,o.jsxs)("body",{className:`${i().variable} ${a().variable} font-sans antialiased touch-manipulation`,children:[(0,o.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' light';
                }
              })();
            `}}),(0,o.jsx)(l.ThemeProvider,{children:e}),(0,o.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `}})]})]})}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,912,18],()=>r(33218));module.exports=o})();