(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5930],{16583:(e,s,t)=>{Promise.resolve().then(t.bind(t,27645))},27645:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ew});var l=t(95155),r=t(12115),i=t(91788),a=t(84616),n=t(17576),o=t(33109),c=t(17580),d=t(55868),u=t(47924),m=t(66932),x=t(48136),h=t(5623),j=t(92657),p=t(13717),v=t(62525),y=t(30285),g=t(62523),f=t(66695),b=t(26126),N=t(85127),w=t(44838),C=t(54165),k=t(25409),S=t(86292),P=t(56671),A=t(79323);let R=t(91950).i3.API_BASE_URL;class E{async makeRequest(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let t=(0,A.Pt)(),l=await fetch("".concat(R).concat(e),{headers:{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)},...s.headers},...s});if(!l.ok){let e=await l.json().catch(()=>({}));throw Error(e.message||"HTTP error! status: ".concat(l.status))}return{data:await l.json()}}catch(e){return console.error("API request failed:",e),{error:e instanceof Error?e.message:"An unexpected error occurred"}}}async getPositions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=new URLSearchParams;Object.entries(e).forEach(e=>{let[t,l]=e;null!=l&&""!==l&&s.append(t,l.toString())});let t=s.toString();return this.makeRequest("/positions".concat(t?"?".concat(t):""))}async getPositionById(e){return this.makeRequest("/positions/".concat(e))}async createPosition(e){return this.makeRequest("/positions",{method:"POST",body:JSON.stringify(e)})}async updatePosition(e,s){return this.makeRequest("/positions/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deletePosition(e){return this.makeRequest("/positions/".concat(e),{method:"DELETE"})}async getPositionEmployees(e){return this.makeRequest("/positions/".concat(e,"/employees"))}async getPositionAnalytics(){return this.makeRequest("/positions/analytics")}async searchPositions(e){return this.getPositions({search:e,limit:50}).then(e=>{var s;return{...e,data:(null==(s=e.data)?void 0:s.positions)||[]}})}async getActivePositions(){return this.getPositions({includeInactive:!1}).then(e=>{var s;return{...e,data:(null==(s=e.data)?void 0:s.positions)||[]}})}async getPositionsByDepartment(e){return this.getPositions({departmentId:e}).then(e=>{var s;return{...e,data:(null==(s=e.data)?void 0:s.positions)||[]}})}async getPositionsByLevel(e){return this.getPositions({level:e}).then(e=>{var s;return{...e,data:(null==(s=e.data)?void 0:s.positions)||[]}})}async getRemotePositions(){return this.getPositions({remoteEligible:!0}).then(e=>{var s;return{...e,data:(null==(s=e.data)?void 0:s.positions)||[]}})}}let q=new E,_=function(e,s){let t=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return e.error?(t&&P.oR.error(e.error),null):(s&&e.data&&P.oR.success(s),e.data||null)},I=async(e,s)=>{try{return s(!0),await e()}catch(e){return console.error("Operation failed:",e),P.oR.error(e instanceof Error?e.message:"Operation failed"),null}finally{s(!1)}};var z=t(62177),L=t(48778),B=t(71153),D=t(88539),M=t(6101),V=t(46081),T=t(85185),O=t(5845),$=t(45503),J=t(11275),Y=t(28905),F=t(63655),U="Checkbox",[Z,W]=(0,V.A)(U),[G,K]=Z(U);function H(e){let{__scopeCheckbox:s,checked:t,children:i,defaultChecked:a,disabled:n,form:o,name:c,onCheckedChange:d,required:u,value:m="on",internal_do_not_use_render:x}=e,[h,j]=(0,O.i)({prop:t,defaultProp:null!=a&&a,onChange:d,caller:U}),[p,v]=r.useState(null),[y,g]=r.useState(null),f=r.useRef(!1),b=!p||!!o||!!p.closest("form"),N={checked:h,disabled:n,setChecked:j,control:p,setControl:v,name:c,form:o,value:m,hasConsumerStoppedPropagationRef:f,required:u,defaultChecked:!ei(a)&&a,isFormControl:b,bubbleInput:y,setBubbleInput:g};return(0,l.jsx)(G,{scope:s,...N,children:"function"==typeof x?x(N):i})}var Q="CheckboxTrigger",X=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,onKeyDown:i,onClick:a,...n}=e,{control:o,value:c,disabled:d,checked:u,required:m,setControl:x,setChecked:h,hasConsumerStoppedPropagationRef:j,isFormControl:p,bubbleInput:v}=K(Q,t),y=(0,M.s)(s,x),g=r.useRef(u);return r.useEffect(()=>{let e=null==o?void 0:o.form;if(e){let s=()=>h(g.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[o,h]),(0,l.jsx)(F.sG.button,{type:"button",role:"checkbox","aria-checked":ei(u)?"mixed":u,"aria-required":m,"data-state":ea(u),"data-disabled":d?"":void 0,disabled:d,value:c,...n,ref:y,onKeyDown:(0,T.m)(i,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,T.m)(a,e=>{h(e=>!!ei(e)||!e),v&&p&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})})});X.displayName=Q;var ee=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:r,checked:i,defaultChecked:a,required:n,disabled:o,value:c,onCheckedChange:d,form:u,...m}=e;return(0,l.jsx)(H,{__scopeCheckbox:t,checked:i,defaultChecked:a,disabled:o,required:n,onCheckedChange:d,name:r,form:u,value:c,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(X,{...m,ref:s,__scopeCheckbox:t}),r&&(0,l.jsx)(er,{__scopeCheckbox:t})]})}})});ee.displayName=U;var es="CheckboxIndicator",et=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:r,...i}=e,a=K(es,t);return(0,l.jsx)(Y.C,{present:r||ei(a.checked)||!0===a.checked,children:(0,l.jsx)(F.sG.span,{"data-state":ea(a.checked),"data-disabled":a.disabled?"":void 0,...i,ref:s,style:{pointerEvents:"none",...e.style}})})});et.displayName=es;var el="CheckboxBubbleInput",er=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,...i}=e,{control:a,hasConsumerStoppedPropagationRef:n,checked:o,defaultChecked:c,required:d,disabled:u,name:m,value:x,form:h,bubbleInput:j,setBubbleInput:p}=K(el,t),v=(0,M.s)(s,p),y=(0,$.Z)(o),g=(0,J.X)(a);r.useEffect(()=>{if(!j)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,s=!n.current;if(y!==o&&e){let t=new Event("click",{bubbles:s});j.indeterminate=ei(o),e.call(j,!ei(o)&&o),j.dispatchEvent(t)}},[j,y,o,n]);let f=r.useRef(!ei(o)&&o);return(0,l.jsx)(F.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=c?c:f.current,required:d,disabled:u,name:m,value:x,form:h,...i,tabIndex:-1,ref:v,style:{...i.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function ei(e){return"indeterminate"===e}function ea(e){return ei(e)?"indeterminate":e?"checked":"unchecked"}er.displayName=el;var en=t(5196),eo=t(59434);let ec=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,l.jsx)(ee,{ref:s,className:(0,eo.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...r,children:(0,l.jsx)(et,{className:(0,eo.cn)("flex items-center justify-center text-current"),children:(0,l.jsx)(en.A,{className:"h-4 w-4"})})})});ec.displayName=ee.displayName;var ed=t(17759),eu=t(59409),em=t(54416),ex=t(51154),eh=t(4229),ej=t(22886);let ep=B.Ik({title:B.Yj().min(2,"Position title must be at least 2 characters").max(100,"Position title must be less than 100 characters"),description:B.Yj().optional(),departmentId:B.Yj().optional(),level:B.k5(["entry","junior","mid","senior","lead","manager","director","vp","c_level"]),type:B.k5(["full_time","part_time","contract","intern"]),minSalary:B.ai().min(0,"Minimum salary must be positive").optional(),maxSalary:B.ai().min(0,"Maximum salary must be positive").optional(),currency:B.Yj().default("USD"),requiredSkills:B.YO(B.Yj()).default([]),responsibilities:B.YO(B.Yj()).default([]),requirements:B.YO(B.Yj()).default([]),remoteEligible:B.zM().default(!1),travelRequired:B.zM().default(!1)}).refine(e=>!e.minSalary||!e.maxSalary||e.maxSalary>=e.minSalary,{message:"Maximum salary must be greater than or equal to minimum salary",path:["maxSalary"]}),ev=[{value:"entry",label:"Entry Level"},{value:"junior",label:"Junior"},{value:"mid",label:"Mid Level"},{value:"senior",label:"Senior"},{value:"lead",label:"Lead"},{value:"manager",label:"Manager"},{value:"director",label:"Director"},{value:"vp",label:"Vice President"},{value:"c_level",label:"C-Level"}],ey=[{value:"full_time",label:"Full Time"},{value:"part_time",label:"Part Time"},{value:"contract",label:"Contract"},{value:"intern",label:"Intern"}];function eg(e){let{position:s,onSuccess:t,onCancel:i,className:n}=e,[o,c]=(0,r.useState)(!1),[d,u]=(0,r.useState)([]),[m,x]=(0,r.useState)(!1),[h,j]=(0,r.useState)(""),[p,N]=(0,r.useState)(""),[w,C]=(0,r.useState)(""),k=!!s,S=(0,z.mN)({resolver:(0,L.u)(ep),defaultValues:{title:(null==s?void 0:s.title)||"",description:(null==s?void 0:s.description)||"",departmentId:(null==s?void 0:s.departmentId)||"",level:(null==s?void 0:s.level)||"entry",type:(null==s?void 0:s.type)||"full_time",minSalary:(null==s?void 0:s.minSalary)||void 0,maxSalary:(null==s?void 0:s.maxSalary)||void 0,currency:(null==s?void 0:s.currency)||"USD",requiredSkills:(null==s?void 0:s.requiredSkills)||[],responsibilities:(null==s?void 0:s.responsibilities)||[],requirements:(null==s?void 0:s.requirements)||[],remoteEligible:(null==s?void 0:s.remoteEligible)||!1,travelRequired:(null==s?void 0:s.travelRequired)||!1}});(0,r.useEffect)(()=>{P()},[]);let P=async()=>{await I(async()=>{let e=_(await ej.br.getActiveDepartments(),void 0,!1);e&&u(e)},x)},A=async e=>{await I(async()=>{let l,r;if(k&&s){let t={...e,minSalary:e.minSalary||void 0,maxSalary:e.maxSalary||void 0};l=await q.updatePosition(s.id,t),r="Position updated successfully"}else{let s={...e,minSalary:e.minSalary||void 0,maxSalary:e.maxSalary||void 0};l=await q.createPosition(s),r="Position created successfully"}let i=_(l,r);i&&(null==t||t(i),k||(S.reset(),j(""),N(""),C("")))},c)},R=()=>{if(h.trim()){let e=S.getValues("requiredSkills");e.includes(h.trim())||(S.setValue("requiredSkills",[...e,h.trim()]),j(""))}},E=e=>{let s=S.getValues("requiredSkills");S.setValue("requiredSkills",s.filter(s=>s!==e))},B=()=>{if(p.trim()){let e=S.getValues("responsibilities");S.setValue("responsibilities",[...e,p.trim()]),N("")}},M=e=>{let s=S.getValues("responsibilities");S.setValue("responsibilities",s.filter((s,t)=>t!==e))},V=()=>{if(w.trim()){let e=S.getValues("requirements");S.setValue("requirements",[...e,w.trim()]),C("")}},T=e=>{let s=S.getValues("requirements");S.setValue("requirements",s.filter((s,t)=>t!==e))};return(0,l.jsxs)(f.Zp,{className:n,children:[(0,l.jsx)(f.aR,{children:(0,l.jsx)(f.ZB,{children:k?"Edit Position":"Create New Position"})}),(0,l.jsx)(f.Wu,{children:(0,l.jsx)(ed.lV,{...S,children:(0,l.jsxs)("form",{onSubmit:S.handleSubmit(A),className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsx)(ed.zB,{control:S.control,name:"title",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{children:[(0,l.jsx)(ed.lR,{children:"Position Title *"}),(0,l.jsx)(ed.MJ,{children:(0,l.jsx)(g.p,{placeholder:"e.g., Senior Software Engineer",...s})}),(0,l.jsx)(ed.Rr,{children:"The official job title"}),(0,l.jsx)(ed.C5,{})]})}}),(0,l.jsx)(ed.zB,{control:S.control,name:"departmentId",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{children:[(0,l.jsx)(ed.lR,{children:"Department"}),(0,l.jsxs)(eu.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,l.jsx)(ed.MJ,{children:(0,l.jsx)(eu.bq,{children:(0,l.jsx)(eu.yv,{placeholder:"Select department"})})}),(0,l.jsxs)(eu.gC,{children:[(0,l.jsx)(eu.eb,{value:"",children:"No department"}),m?(0,l.jsx)(eu.eb,{value:"",disabled:!0,children:"Loading departments..."}):d.map(e=>(0,l.jsxs)(eu.eb,{value:e.id,children:[e.name," (",e.code,")"]},e.id))]})]}),(0,l.jsx)(ed.C5,{})]})}})]}),(0,l.jsx)(ed.zB,{control:S.control,name:"description",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{children:[(0,l.jsx)(ed.lR,{children:"Description"}),(0,l.jsx)(ed.MJ,{children:(0,l.jsx)(D.T,{placeholder:"Brief description of the position",className:"min-h-[100px]",...s})}),(0,l.jsx)(ed.C5,{})]})}}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsx)(ed.zB,{control:S.control,name:"level",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{children:[(0,l.jsx)(ed.lR,{children:"Level *"}),(0,l.jsxs)(eu.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,l.jsx)(ed.MJ,{children:(0,l.jsx)(eu.bq,{children:(0,l.jsx)(eu.yv,{placeholder:"Select level"})})}),(0,l.jsx)(eu.gC,{children:ev.map(e=>(0,l.jsx)(eu.eb,{value:e.value,children:e.label},e.value))})]}),(0,l.jsx)(ed.C5,{})]})}}),(0,l.jsx)(ed.zB,{control:S.control,name:"type",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{children:[(0,l.jsx)(ed.lR,{children:"Employment Type *"}),(0,l.jsxs)(eu.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,l.jsx)(ed.MJ,{children:(0,l.jsx)(eu.bq,{children:(0,l.jsx)(eu.yv,{placeholder:"Select type"})})}),(0,l.jsx)(eu.gC,{children:ey.map(e=>(0,l.jsx)(eu.eb,{value:e.value,children:e.label},e.value))})]}),(0,l.jsx)(ed.C5,{})]})}})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,l.jsx)(ed.zB,{control:S.control,name:"minSalary",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{children:[(0,l.jsx)(ed.lR,{children:"Minimum Salary"}),(0,l.jsx)(ed.MJ,{children:(0,l.jsx)(g.p,{type:"number",placeholder:"e.g., 80000",...s,onChange:e=>s.onChange(e.target.value?parseFloat(e.target.value):void 0)})}),(0,l.jsx)(ed.C5,{})]})}}),(0,l.jsx)(ed.zB,{control:S.control,name:"maxSalary",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{children:[(0,l.jsx)(ed.lR,{children:"Maximum Salary"}),(0,l.jsx)(ed.MJ,{children:(0,l.jsx)(g.p,{type:"number",placeholder:"e.g., 120000",...s,onChange:e=>s.onChange(e.target.value?parseFloat(e.target.value):void 0)})}),(0,l.jsx)(ed.C5,{})]})}}),(0,l.jsx)(ed.zB,{control:S.control,name:"currency",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{children:[(0,l.jsx)(ed.lR,{children:"Currency"}),(0,l.jsx)(ed.MJ,{children:(0,l.jsx)(g.p,{placeholder:"USD",...s})}),(0,l.jsx)(ed.C5,{})]})}})]}),(0,l.jsxs)("div",{className:"flex space-x-6",children:[(0,l.jsx)(ed.zB,{control:S.control,name:"remoteEligible",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,l.jsx)(ed.MJ,{children:(0,l.jsx)(ec,{checked:s.value,onCheckedChange:s.onChange})}),(0,l.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,l.jsx)(ed.lR,{children:"Remote Eligible"}),(0,l.jsx)(ed.Rr,{children:"Position can be performed remotely"})]})]})}}),(0,l.jsx)(ed.zB,{control:S.control,name:"travelRequired",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,l.jsx)(ed.MJ,{children:(0,l.jsx)(ec,{checked:s.value,onCheckedChange:s.onChange})}),(0,l.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,l.jsx)(ed.lR,{children:"Travel Required"}),(0,l.jsx)(ed.Rr,{children:"Position requires business travel"})]})]})}})]}),(0,l.jsx)(ed.zB,{control:S.control,name:"requiredSkills",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{children:[(0,l.jsx)(ed.lR,{children:"Required Skills"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(g.p,{placeholder:"Add a skill",value:h,onChange:e=>j(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),R())}),(0,l.jsx)(y.$,{type:"button",onClick:R,size:"sm",children:(0,l.jsx)(a.A,{className:"w-4 h-4"})})]}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:s.value.map((e,s)=>(0,l.jsxs)(b.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,l.jsx)(y.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-0 text-muted-foreground hover:text-destructive",onClick:()=>E(e),children:(0,l.jsx)(em.A,{className:"w-3 h-3"})})]},s))})]}),(0,l.jsx)(ed.C5,{})]})}}),(0,l.jsx)(ed.zB,{control:S.control,name:"responsibilities",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{children:[(0,l.jsx)(ed.lR,{children:"Key Responsibilities"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(g.p,{placeholder:"Add a responsibility",value:p,onChange:e=>N(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),B())}),(0,l.jsx)(y.$,{type:"button",onClick:B,size:"sm",children:(0,l.jsx)(a.A,{className:"w-4 h-4"})})]}),(0,l.jsx)("div",{className:"space-y-2",children:s.value.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-start space-x-2 p-2 bg-muted rounded",children:[(0,l.jsx)("span",{className:"flex-1 text-sm",children:e}),(0,l.jsx)(y.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1 text-muted-foreground hover:text-destructive",onClick:()=>M(s),children:(0,l.jsx)(v.A,{className:"w-3 h-3"})})]},s))})]}),(0,l.jsx)(ed.C5,{})]})}}),(0,l.jsx)(ed.zB,{control:S.control,name:"requirements",render:e=>{let{field:s}=e;return(0,l.jsxs)(ed.eI,{children:[(0,l.jsx)(ed.lR,{children:"Requirements"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(g.p,{placeholder:"Add a requirement",value:w,onChange:e=>C(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),V())}),(0,l.jsx)(y.$,{type:"button",onClick:V,size:"sm",children:(0,l.jsx)(a.A,{className:"w-4 h-4"})})]}),(0,l.jsx)("div",{className:"space-y-2",children:s.value.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-start space-x-2 p-2 bg-muted rounded",children:[(0,l.jsx)("span",{className:"flex-1 text-sm",children:e}),(0,l.jsx)(y.$,{type:"button",variant:"ghost",size:"sm",className:"h-auto p-1 text-muted-foreground hover:text-destructive",onClick:()=>T(s),children:(0,l.jsx)(v.A,{className:"w-3 h-3"})})]},s))})]}),(0,l.jsx)(ed.C5,{})]})}}),(0,l.jsxs)("div",{className:"flex justify-end space-x-4 pt-6",children:[(0,l.jsxs)(y.$,{type:"button",variant:"outline",onClick:()=>{S.reset(),j(""),N(""),C(""),null==i||i()},disabled:o,children:[(0,l.jsx)(em.A,{className:"w-4 h-4 mr-2"}),"Cancel"]}),(0,l.jsxs)(y.$,{type:"submit",disabled:o,children:[o?(0,l.jsx)(ex.A,{className:"w-4 h-4 mr-2 animate-spin"}):(0,l.jsx)(eh.A,{className:"w-4 h-4 mr-2"}),k?"Update Position":"Create Position"]})]})]})})})]})}var ef=t(38068),eb=t(6096);let eN={entry:"bg-green-500/10 text-green-600 dark:text-green-400",junior:"bg-blue-500/10 text-blue-600 dark:text-blue-400",mid:"bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",senior:"bg-orange-500/10 text-orange-600 dark:text-orange-400",lead:"bg-purple-500/10 text-purple-600 dark:text-purple-400",manager:"bg-red-500/10 text-red-600 dark:text-red-400",director:"bg-muted text-muted-foreground",vp:"bg-indigo-500/10 text-indigo-600 dark:text-indigo-400",c_level:"bg-foreground text-background"};function ew(){var e,s,t,P,A,R;let E=(0,ef.Sk)(),[z,L]=(0,r.useState)({positions:[],analytics:null,loading:!0,error:null}),[B,D]=(0,r.useState)(""),[M,V]=(0,r.useState)(!1),[T,O]=(0,r.useState)(null),[$,J]=(0,r.useState)(null),[Y,F]=(0,r.useState)(!1);(0,r.useEffect)(()=>{U(),Z()},[]);let U=async()=>{await I(async()=>{let e=_(await q.getPositions({includeInactive:!0,sortBy:"title",sortOrder:"asc"}),void 0,!0);e?L(s=>({...s,positions:e.positions,loading:!1,error:null})):L(e=>({...e,loading:!1,error:"Failed to load positions"}))},e=>L(s=>({...s,loading:e})))},Z=async()=>{let e=_(await q.getPositionAnalytics(),void 0,!1);e&&L(s=>({...s,analytics:e}))},W=z.positions.filter(e=>e.title.toLowerCase().includes(B.toLowerCase())||e.departmentName&&e.departmentName.toLowerCase().includes(B.toLowerCase())||e.level.toLowerCase().includes(B.toLowerCase())),G=async()=>{await U(),await Z()},K=e=>{O(e),V(!0)},H=e=>{J(e),F(!0)},Q=async e=>{confirm("Are you sure you want to delete this position?")&&await I(async()=>{null!==_(await q.deletePosition(e),"Position deleted successfully")&&(await U(),await Z())},e=>L(s=>({...s,loading:e})))},X=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),ee=e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");return(0,l.jsx)(eb.$G,{permissions:["position_read","hr","hr_admin","super_admin"],children:(0,l.jsxs)(S.k,{onRefresh:G,className:"flex-1",children:[(0,l.jsxs)("div",{className:"space-y-6 p-6",children:[(0,l.jsx)(k.Y,{title:"Position Management",subtitle:"Managing ".concat((null==(e=z.analytics)?void 0:e.totalPositions)||0," positions with ").concat((null==(s=z.analytics)?void 0:s.totalEmployees)||0," employees"),actions:(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(ef.LQ,{permissions:["position_read","hr","hr_admin","super_admin"],children:(0,l.jsxs)(y.$,{variant:"outline",size:"sm",children:[(0,l.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Export"]})}),(0,l.jsx)(ef.LQ,{permissions:["position_write","hr_admin","super_admin"],children:(0,l.jsxs)(y.$,{size:"sm",onClick:()=>{O(null),V(!0)},children:[(0,l.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"Add Position"]})})]})}),z.loading&&(0,l.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,l.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Loading positions..."})]})}),z.error&&(0,l.jsxs)("div",{className:"bg-destructive/10 border border-destructive/20 rounded-lg p-4",children:[(0,l.jsx)("p",{className:"text-destructive",children:z.error}),(0,l.jsx)(y.$,{variant:"outline",size:"sm",onClick:G,className:"mt-2",children:"Try Again"})]}),!z.loading&&!z.error&&(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,l.jsx)(f.Zp,{children:(0,l.jsx)(f.Wu,{className:"p-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Positions"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:(null==(t=z.analytics)?void 0:t.totalPositions)||0})]}),(0,l.jsx)(n.A,{className:"h-8 w-8 text-primary"})]})})}),(0,l.jsx)(f.Zp,{children:(0,l.jsx)(f.Wu,{className:"p-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Active Positions"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:(null==(P=z.analytics)?void 0:P.activePositions)||0})]}),(0,l.jsx)(o.A,{className:"h-8 w-8 text-green-600"})]})})}),(0,l.jsx)(f.Zp,{children:(0,l.jsx)(f.Wu,{className:"p-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Employees"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:(null==(A=z.analytics)?void 0:A.totalEmployees)||0})]}),(0,l.jsx)(c.A,{className:"h-8 w-8 text-blue-600"})]})})}),(0,l.jsx)(f.Zp,{children:(0,l.jsx)(f.Wu,{className:"p-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Remote Positions"}),(0,l.jsx)("p",{className:"text-2xl font-bold",children:(null==(R=z.analytics)?void 0:R.remotePositions)||0})]}),(0,l.jsx)(d.A,{className:"h-8 w-8 text-yellow-600"})]})})})]}),(0,l.jsx)(f.Zp,{children:(0,l.jsx)(f.Wu,{className:"p-6",children:(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,l.jsxs)("div",{className:"relative flex-1",children:[(0,l.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,l.jsx)(g.p,{placeholder:"Search positions...",value:B,onChange:e=>D(e.target.value),className:"pl-10"})]}),(0,l.jsxs)(y.$,{variant:"outline",size:"sm",children:[(0,l.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Filters"]})]})})}),(0,l.jsxs)(f.Zp,{children:[(0,l.jsx)(f.aR,{children:(0,l.jsx)(f.ZB,{children:"Positions"})}),(0,l.jsx)(f.Wu,{children:(0,l.jsxs)(N.XI,{children:[(0,l.jsx)(N.A0,{children:(0,l.jsxs)(N.Hj,{children:[(0,l.jsx)(N.nd,{children:"Position"}),(0,l.jsx)(N.nd,{children:"Department"}),(0,l.jsx)(N.nd,{children:"Level"}),(0,l.jsx)(N.nd,{children:"Employees"}),(0,l.jsx)(N.nd,{children:"Salary Range"}),(0,l.jsx)(N.nd,{children:"Remote"}),(0,l.jsx)(N.nd,{children:"Status"}),(0,l.jsx)(N.nd,{className:"text-right",children:"Actions"})]})}),(0,l.jsx)(N.BF,{children:W.map(e=>(0,l.jsxs)(N.Hj,{children:[(0,l.jsx)(N.nA,{children:(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-medium",children:e.title}),(0,l.jsx)("div",{className:"text-sm text-muted-foreground",children:e.description||"No description"})]})}),(0,l.jsx)(N.nA,{children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(x.A,{className:"h-4 w-4 mr-2 text-muted-foreground"}),e.departmentName||"No department"]})}),(0,l.jsx)(N.nA,{children:(0,l.jsx)(b.E,{className:eN[e.level]||"bg-muted text-muted-foreground",children:ee(e.level)})}),(0,l.jsx)(N.nA,{children:e.employeeCount||0}),(0,l.jsx)(N.nA,{children:(0,l.jsx)("div",{className:"text-sm",children:e.minSalary&&e.maxSalary?"".concat(X(e.minSalary)," - ").concat(X(e.maxSalary)):"Not set"})}),(0,l.jsx)(N.nA,{children:(0,l.jsx)(b.E,{variant:e.remoteEligible?"default":"secondary",children:e.remoteEligible?"Yes":"No"})}),(0,l.jsx)(N.nA,{children:(0,l.jsx)(b.E,{variant:e.isActive?"default":"secondary",children:e.isActive?"Active":"Inactive"})}),(0,l.jsx)(N.nA,{className:"text-right",children:(0,l.jsxs)(w.rI,{children:[(0,l.jsx)(w.ty,{asChild:!0,children:(0,l.jsx)(y.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,l.jsx)(h.A,{className:"h-4 w-4"})})}),(0,l.jsxs)(w.SQ,{align:"end",children:[(0,l.jsxs)(w._2,{onClick:()=>H(e),children:[(0,l.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"View Details"]}),E.canWritePositions()&&(0,l.jsxs)(w._2,{onClick:()=>K(e),children:[(0,l.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Edit"]}),E.canWritePositions()&&(0,l.jsxs)(w._2,{onClick:()=>Q(e.id),className:"text-destructive",children:[(0,l.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e.id))})]})})]})]}),(0,l.jsx)(C.lG,{open:M,onOpenChange:V,children:(0,l.jsxs)(C.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,l.jsx)(C.c7,{children:(0,l.jsx)(C.L3,{children:T?"Edit Position":"Create New Position"})}),(0,l.jsx)(eg,{position:T,onSuccess:e=>{V(!1),O(null),U(),Z()},onCancel:()=>{V(!1),O(null)},className:"border-0 shadow-none"})]})}),(0,l.jsx)(C.lG,{open:Y,onOpenChange:F,children:(0,l.jsxs)(C.Cf,{className:"max-w-4xl",children:[(0,l.jsx)(C.c7,{children:(0,l.jsx)(C.L3,{children:"Position Details"})}),(0,l.jsx)("div",{className:"p-4",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"Position details view will be implemented here."})})]})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3706,9380,6110,1647,2426,5106,1712,5409,6579,8441,1684,7358],()=>s(16583)),_N_E=e.O()}]);