(()=>{var e={};e.id=492,e.ids=[492],e.modules={3216:(e,t,r)=>{Promise.resolve().then(r.bind(r,5388))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4148:(e,t,r)=>{"use strict";r.d(t,{AZ:()=>i,OD:()=>s,fu:()=>n,zS:()=>o});function o(){return"system"}function n(e){}function s(){let e=o()}function i(e){}},5388:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var o=r(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\components\\providers\\theme-provider.tsx","useTheme");let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PeopleNest\\peoplenest-ui\\src\\components\\providers\\theme-provider.tsx","ThemeProvider")},7696:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>m});var o=r(65239),n=r(48088),s=r(88170),i=r.n(s),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let m={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\PeopleNest\\peoplenest-ui\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=[],c={require:r,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12944:(e,t,r)=>{Promise.resolve().then(r.bind(r,35862))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19731:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35862:(e,t,r)=>{"use strict";r.d(t,{D:()=>a,ThemeProvider:()=>l});var o=r(60687),n=r(43210),s=r(4148);let i=(0,n.createContext)(void 0);function a(){let e=(0,n.useContext)(i);return void 0===e?{theme:"system",setTheme:()=>{},toggleTheme:()=>{},systemTheme:"light",actualTheme:"light"}:e}function l({children:e,defaultTheme:t="light"}){let[r,a]=(0,n.useState)(t),[l,m]=(0,n.useState)("light"),[d,c]=(0,n.useState)(!1),p=e=>{a(e),(0,s.fu)(e),(0,s.AZ)(e)};return(0,o.jsx)(i.Provider,{value:{theme:r,setTheme:p,toggleTheme:()=>{p("light"===("system"===r?l:r)?"dark":"light")},systemTheme:l,actualTheme:"system"===r?l:r},children:e})}},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82939:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>m,viewport:()=>d});var o=r(37413),n=r(58745),s=r.n(n),i=r(64066),a=r.n(i);r(61135);var l=r(5388);let m={title:"PeopleNest - Enterprise HRMS Platform",description:"Modern, AI-powered Human Resource Management System for enterprise organizations",keywords:["HRMS","HR","Human Resources","Employee Management","Payroll","Performance"],manifest:"/manifest.json",appleWebApp:{capable:!0,statusBarStyle:"default",title:"PeopleNest HRMS"},formatDetection:{telephone:!1},other:{"mobile-web-app-capable":"yes","apple-mobile-web-app-capable":"yes","apple-mobile-web-app-status-bar-style":"default","apple-mobile-web-app-title":"PeopleNest","application-name":"PeopleNest HRMS","msapplication-TileColor":"#3b82f6","msapplication-config":"/browserconfig.xml"}},d={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,viewportFit:"cover",themeColor:"#3b82f6"};function c({children:e}){return(0,o.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,o.jsx)("head",{children:(0,o.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' light';
                }
              })();
            `}})}),(0,o.jsxs)("body",{className:`${s().variable} ${a().variable} font-sans antialiased touch-manipulation`,children:[(0,o.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              (function() {
                try {
                  var theme = localStorage.getItem('theme-mode') || 'light';
                  var root = document.documentElement;

                  // Apply theme immediately to prevent hydration mismatch
                  if (theme === 'system') {
                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + systemTheme;
                  } else {
                    root.className = root.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' ' + theme;
                  }
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.className = document.documentElement.className.replace(/\\b(light|dark)\\b/g, '').trim() + ' light';
                }
              })();
            `}}),(0,o.jsx)(l.ThemeProvider,{children:e}),(0,o.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `}})]})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,912],()=>r(7696));module.exports=o})();