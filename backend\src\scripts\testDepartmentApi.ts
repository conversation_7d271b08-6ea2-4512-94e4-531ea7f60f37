import axios from 'axios';

async function testDepartmentApi() {
  try {
    console.log('Testing department API...');

    // First, login to get token
    console.log('1. Logging in...');
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
      email: '<EMAIL>',
      password: 'Password1234'
    });

    const token = loginResponse.data.token;
    console.log('✅ Login successful, token received');
    console.log('User info:', {
      id: loginResponse.data.user.id,
      email: loginResponse.data.user.email,
      role: loginResponse.data.user.role,
      isActive: loginResponse.data.user.isActive
    });

    // Wait a moment to ensure session is properly created
    console.log('Waiting 2 seconds for session to be established...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test departments API
    console.log('2. Testing departments API...');
    const departmentsResponse = await axios.get('http://localhost:3002/api/departments', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ Departments API successful');
    console.log('Departments found:', departmentsResponse.data.length);
    console.log('Department names:', departmentsResponse.data.map((d: any) => d.name));

  } catch (error: any) {
    console.error('❌ Error:', error.response?.data || error.message);
    if (error.response?.status === 401) {
      console.log('This appears to be an authentication issue. Let me check the session validation...');
    }
  }
}

testDepartmentApi();
